{"version": 3, "sources": ["index.js", "cloudbase.js", "functions/index.js", "utils/httpRequest.js", "utils/tracing.js", "utils/utils.js", "utils/metadata.js", "const/code.js", "const/symbol.js", "utils/request.js", "utils/retry.js", "utils/request-timings-measurer.js", "utils/requestHook.js", "utils/wxCloudToken.js", "utils/secretManager.js", "../package.json", "auth/index.js", "wx/index.js", "storage/index.js", "analytics/index.js", "utils/dbRequest.js", "log/index.js"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ACHA,AFMA;ACFA,ACHA,AFMA;ACFA,ACHA,AFMA;ACFA,ACHA,AFMA,AGTA;AFOA,ACHA,AFMA,AGTA;AFOA,ACHA,AFMA,AGTA;AFOA,ACHA,AFMA,AGTA,ACHA;AHUA,ACHA,AFMA,AGTA,ACHA;AHUA,ACHA,AFMA,AGTA,ACHA;AHUA,ACHA,AFMA,AGTA,ACHA,ACHA;AJaA,ACHA,AFMA,AGTA,ACHA,ACHA;AJaA,ACHA,AFMA,AGTA,ACHA,ACHA;AJaA,ACHA,AFMA,AGTA,AGTA,AFMA,ACHA;AJaA,ACHA,AFMA,AGTA,AGTA,AFMA,ACHA;AJaA,ACHA,AFMA,AGTA,AGTA,AFMA,ACHA;AJaA,AMlBA,ALeA,AFMA,AGTA,AGTA,AFMA,ACHA;AJaA,AMlBA,ALeA,AFMA,AGTA,AGTA,AFMA,ACHA;AJaA,AMlBA,ALeA,AFMA,AGTA,AGTA,AFMA,ACHA;AJaA,AMlBA,ACHA,ANkBA,ACHA,AGTA,AFMA,ACHA;AJaA,AMlBA,ACHA,ANkBA,ACHA,AGTA,AFMA,ACHA;AJaA,AMlBA,ACHA,ANkBA,ACHA,AGTA,AFMA,ACHA;AJaA,AMlBA,ACHA,ANkBA,ACHA,AGTA,AGTA,ALeA,ACHA;AJaA,AMlBA,ALeA,ACHA,AGTA,AGTA,ALeA,ACHA;AJaA,AMlBA,ALeA,ACHA,AGTA,AGTA,ALeA,ACHA;AJaA,AMlBA,ALeA,ACHA,AGTA,AGTA,ACHA,ALeA;AJaA,AMlBA,ALeA,ACHA,AGTA,AGTA,ACHA,ALeA;AJaA,AMlBA,ALeA,ACHA,AGTA,AGTA,ACHA,ALeA;AJaA,AMlBA,ALeA,ACHA,AGTA,AKfA,AFMA,ACHA,ALeA;AJaA,AMlBA,ALeA,ACHA,AGTA,AKfA,AFMA,ACHA,ALeA;AJaA,AMlBA,ALeA,ACHA,AGTA,AKfA,AFMA,ACHA,ALeA;AJaA,AMlBA,ALeA,ACHA,AGTA,AKfA,AFMA,AGTA,AFMA,ALeA;AJaA,AMlBA,ALeA,ACHA,AGTA,AKfA,AFMA,AGTA,AFMA,ALeA;AJaA,AMlBA,ALeA,ACHA,AGTA,AKfA,AFMA,AGTA,AFMA,ALeA;AJaA,AMlBA,ALeA,ACHA,AGTA,AKfA,AFMA,AGTA,AFMA,ALeA,AQxBA;AZqCA,AMlBA,ALeA,ACHA,AGTA,AKfA,AFMA,AGTA,AFMA,ALeA,AQxBA;AZqCA,AMlBA,ALeA,ACHA,AGTA,AKfA,AFMA,AGTA,AFMA,ALeA,AQxBA;AZqCA,AMlBA,ALeA,ACHA,AGTA,AKfA,AFMA,AGTA,AFMA,AIZA,AT2BA,AQxBA;AZqCA,AMlBA,ALeA,ACHA,AGTA,AKfA,AFMA,AGTA,AFMA,AIZA,AT2BA,AQxBA;AZqCA,AMlBA,ALeA,ACHA,AGTA,AKfA,AFMA,AGTA,AFMA,AIZA,AT2BA,AQxBA;AELA,Ad0CA,AMlBA,ALeA,ACHA,AGTA,AKfA,AFMA,AGTA,AFMA,AIZA,AT2BA,AQxBA;AELA,Ad0CA,AMlBA,ALeA,ACHA,AGTA,AKfA,AFMA,AGTA,AFMA,AIZA,AT2BA,AQxBA;AELA,Ad0CA,AMlBA,ALeA,ACHA,AGTA,AKfA,AFMA,AGTA,AFMA,AIZA,AT2BA,AQxBA;AELA,ACHA,Af6CA,AMlBA,ALeA,ACHA,AGTA,AKfA,AFMA,AGTA,AFMA,AIZA,AT2BA,AQxBA;AELA,ACHA,Af6CA,AMlBA,ALeA,ACHA,AGTA,AKfA,AFMA,AGTA,AFMA,AIZA,AT2BA,AQxBA;AELA,ACHA,Af6CA,ACHA,ACHA,AGTA,AKfA,AFMA,AGTA,AFMA,AIZA,AT2BA,AQxBA;AELA,ACHA,Af6CA,ACHA,ACHA,AGTA,AKfA,AFMA,AGTA,AFMA,AIZA,AT2BA,AQxBA,AIZA;AFOA,ACHA,Af6CA,ACHA,ACHA,AGTA,AKfA,AFMA,AGTA,AFMA,AIZA,AT2BA,AQxBA,AIZA;AFOA,ACHA,Af6CA,ACHA,ACHA,AGTA,AKfA,AFMA,AGTA,AFMA,AIZA,AT2BA,AQxBA,AIZA;AFOA,ACHA,Af6CA,ACHA,AgBhDA,Af6CA,AGTA,AKfA,AFMA,AGTA,AFMA,AIZA,AT2BA,AQxBA,AIZA;AFOA,ACHA,Af6CA,ACHA,AgBhDA,Af6CA,AGTA,AKfA,AFMA,ACHA,AIZA,AT2BA,AQxBA,AIZA;AFOA,ACHA,Af6CA,ACHA,AgBhDA,Af6CA,AGTA,AKfA,AFMA,ACHA,AIZA,AT2BA,AQxBA,AIZA;AFOA,AIZA,AHSA,Af6CA,ACHA,AgBhDA,Af6CA,AGTA,AKfA,AFMA,ACHA,AIZA,AT2BA,AQxBA,AIZA;AFOA,AIZA,AHSA,Af6CA,ACHA,AgBhDA,Af6CA,AGTA,AKfA,AFMA,ACHA,AIZA,AT2BA,AQxBA,AIZA;AFOA,AIZA,AHSA,Af6CA,ACHA,AgBhDA,Af6CA,AGTA,AKfA,AFMA,ACHA,AIZA,AT2BA,AQxBA,AIZA;AFOA,AIZA,AHSA,Af6CA,ACHA,AgBhDA,AENA,AjBmDA,AGTA,AKfA,AFMA,ACHA,AIZA,AT2BA,AQxBA,AIZA;AFOA,AIZA,AHSA,Af6CA,ACHA,AgBhDA,AENA,AjBmDA,AGTA,AKfA,AFMA,ACHA,AIZA,AT2BA,AQxBA,AIZA;AFOA,AIZA,AHSA,Af6CA,ACHA,AgBhDA,AENA,AjBmDA,AGTA,AKfA,AFMA,ACHA,AIZA,AT2BA,AQxBA,AIZA;AFOA,AIZA,AHSA,Af6CA,ACHA,AmBzDA,AHSA,AENA,AjBmDA,AGTA,AKfA,AFMA,ACHA,AIZA,AT2BA,AQxBA,AIZA;AFOA,AIZA,AHSA,Af6CA,ACHA,AmBzDA,AHSA,AENA,AjBmDA,AGTA,AKfA,AFMA,ACHA,AIZA,AT2BA,AQxBA,AIZA;AFOA,AIZA,AHSA,Af6CA,AoB5DA,AHSA,AENA,AjBmDA,AGTA,AKfA,AFMA,ACHA,AIZA,AT2BA,AQxBA,AIZA;AFOA,AIZA,AHSA,Af6CA,AoB5DA,AHSA,AENA,AjBmDA,AGTA,AKfA,AFMA,ACHA,AIZA,AT2BA,AQxBA,AIZA;AFOA,AIZA,AHSA,Af6CA,AoB5DA,AHSA,AENA,AjBmDA,AGTA,AKfA,AFMA,ACHA,AIZA,AT2BA,AQxBA,AIZA;AFOA,AIZA,AHSA,Af6CA,AoB5DA,AHSA,AENA,AjBmDA,AGTA,AKfA,AFMA,ACHA,AIZA,AT2BA,AQxBA,AIZA;AFOA,AIZA,AHSA,Af6CA,AoB5DA,AHSA,AENA,AjBmDA,AGTA,AKfA,AFMA,ACHA,AIZA,AT2BA,AQxBA,AIZA;AFOA,AIZA,AHSA,Af6CA,AoB5DA,AHSA,AENA,AjBmDA,AGTA,AKfA,AFMA,ACHA,AIZA,AT2BA,AQxBA,AIZA;AFOA,AIZA,AHSA,Af6CA,AoB5DA,AHSA,AENA,AjBmDA,AGTA,AKfA,AFMA,ACHA,AIZA,AT2BA,AQxBA,AIZA;AFOA,AIZA,AHSA,Af6CA,AoB5DA,AHSA,AENA,AjBmDA,AQxBA,AFMA,ACHA,AIZA,AT2BA,AQxBA,AIZA;AFOA,AIZA,AHSA,Af6CA,AoB5DA,AHSA,AENA,AjBmDA,AQxBA,AFMA,ACHA,AIZA,AT2BA,AQxBA,AIZA;AFOA,AIZA,AHSA,Af6CA,AoB5DA,AHSA,AENA,AjBmDA,AQxBA,AFMA,ACHA,AIZA,AT2BA,AQxBA,AIZA;AFOA,AIZA,AHSA,Af6CA,AoB5DA,AHSA,AENA,AjBmDA,AQxBA,AFMA,ACHA,AIZA,AT2BA,AQxBA,AIZA;AFOA,AIZA,AHSA,Af6CA,AoB5DA,AHSA,AENA,AjBmDA,AQxBA,AFMA,ACHA,AIZA,AT2BA,AQxBA,AIZA;AFOA,AIZA,AHSA,Af6CA,AoB5DA,AHSA,AENA,AjBmDA,AQxBA,AFMA,ACHA,AIZA,AT2BA,AQxBA,AIZA;AFOA,AIZA,AHSA,Af6CA,AoB5DA,AHSA,AENA,AjBmDA,AQxBA,AFMA,ACHA,AIZA,AT2BA,AQxBA,AIZA;AFOA,AIZA,AHSA,Af6CA,AoB5DA,AHSA,AENA,AjBmDA,AQxBA,AFMA,ACHA,AIZA,AT2BA,AQxBA,AIZA;AFOA,AIZA,AHSA,Af6CA,AoB5DA,AHSA,AENA,AjBmDA,AQxBA,AFMA,ACHA,AIZA,AT2BA,AQxBA,AIZA;AFOA,AIZA,AHSA,Af6CA,AoB5DA,AHSA,AENA,AjBmDA,AQxBA,AFMA,ACHA,AIZA,AT2BA,AQxBA,AIZA;AFOA,AIZA,AHSA,Af6CA,AoB5DA,AHSA,AENA,AjBmDA,AQxBA,AFMA,ACHA,AIZA,AT2BA,AQxBA,AIZA;AFOA,AIZA,AHSA,Af6CA,AoB5DA,AHSA,AENA,AjBmDA,AQxBA,AFMA,ACHA,AIZA,AT2BA,AQxBA,AIZA;AFOA,AIZA,AHSA,Af6CA,AoB5DA,AHSA,AENA,AjBmDA,AQxBA,AFMA,ACHA,AIZA,AT2BA,AQxBA,AIZA;AFOA,AIZA,AHSA,Af6CA,AoB5DA,AHSA,AENA,AjBmDA,AQxBA,AFMA,ACHA,AIZA,AT2BA,AQxBA,AIZA;AFOA,AIZA,AHSA,Af6CA,AoB5DA,AHSA,AENA,AjBmDA,AQxBA,AFMA,ACHA,AIZA,AT2BA,AQxBA,AIZA;AFOA,AIZA,AHSA,Af6CA,AoB5DA,AHSA,AENA,AjBmDA,AQxBA,AFMA,ACHA,AIZA,AT2BA,AQxBA,AIZA;AFOA,AIZA,AHSA,Af6CA,AoB5DA,AHSA,AENA,AjBmDA,AQxBA,AFMA,ACHA,AIZA,AT2BA,AQxBA,AIZA;AFOA,AIZA,AHSA,Af6CA,AoB5DA,AHSA,AENA,AjBmDA,AQxBA,AFMA,ACHA,AIZA,AT2BA,AQxBA,AIZA;AFOA,AIZA,AHSA,Af6CA,AoB5DA,AHSA,AENA,AjBmDA,AQxBA,AFMA,ACHA,AIZA,AT2BA,AYpCA;AFOA,AIZA,AHSA,Af6CA,AoB5DA,AHSA,AENA,AjBmDA,AQxBA,AFMA,ACHA,AIZA,AT2BA,AYpCA;AFOA,AIZA,AHSA,Af6CA,AoB5DA,AHSA,AENA,AjBmDA,AQxBA,AFMA,ACHA,AIZA,AT2BA,AYpCA;AFOA,AIZA,AHSA,Af6CA,AoB5DA,AHSA,AENA,AjBmDA,AQxBA,AFMA,ACHA,AIZA,AT2BA,AYpCA;AFOA,AIZA,AHSA,Af6CA,AoB5DA,AHSA,AENA,AjBmDA,AQxBA,AFMA,ACHA,AIZA,AT2BA,AYpCA;AFOA,AIZA,AHSA,Af6CA,AoB5DA,AHSA,AENA,AjBmDA,AQxBA,AFMA,ACHA,AIZA,AT2BA,AYpCA;AFOA,AIZA,AHSA,Af6CA,AoB5DA,AHSA,AENA,AjBmDA,AQxBA,AFMA,ACHA,AIZA,AT2BA,AYpCA;AFOA,AIZA,AHSA,Af6CA,AoB5DA,AHSA,AENA,AjBmDA,AQxBA,AFMA,ACHA,AIZA,AT2BA,AYpCA;AFOA,AIZA,AHSA,Af6CA,AoB5DA,AHSA,AENA,AjBmDA,AQxBA,AFMA,ACHA,AIZA,AT2BA,AYpCA;AFOA,AIZA,AHSA,Af6CA,AoB5DA,AHSA,AENA,AjBmDA,AQxBA,AFMA,ACHA,AIZA,AT2BA,AYpCA;AFOA,AIZA,AHSA,Af6CA,AoB5DA,AHSA,AENA,AjBmDA,AQxBA,AFMA,ACHA,AIZA,AT2BA,AYpCA;AFOA,AIZA,AHSA,Af6CA,AoB5DA,AHSA,AENA,AjBmDA,AQxBA,AFMA,ACHA,AIZA,AT2BA,AYpCA;AFOA,AIZA,AHSA,Af6CA,AoB5DA,AHSA,AENA,AjBmDA,AQxBA,AFMA,ACHA,AIZA,AT2BA,AYpCA;AFOA,AIZA,AHSA,Af6CA,AoB5DA,AHSA,Af6CA,AQxBA,AFMA,ACHA,AIZA,AT2BA,AYpCA;AFOA,AIZA,AHSA,Af6CA,AoB5DA,AHSA,Af6CA,AQxBA,AFMA,ACHA,ALeA,AYpCA;AFOA,AIZA,AHSA,Af6CA,AoB5DA,AHSA,Af6CA,AQxBA,AFMA,ACHA,ALeA,AYpCA;AFOA,AIZA,AHSA,Af6CA,AoB5DA,AHSA,Af6CA,AQxBA,AFMA,ACHA,ALeA,AYpCA;AFOA,AIZA,AHSA,Af6CA,AoB5DA,AHSA,Af6CA,AQxBA,AFMA,ACHA,ALeA,AYpCA;AFOA,AIZA,AHSA,Af6CA,AoB5DA,AHSA,Af6CA,AQxBA,AFMA,ACHA,ALeA,AYpCA;AFOA,AIZA,AHSA,Af6CA,AoB5DA,AHSA,Af6CA,AQxBA,AFMA,ACHA,ALeA,AYpCA;AFOA,AIZA,AHSA,Af6CA,AoB5DA,AHSA,Af6CA,AQxBA,AFMA,ACHA,ALeA,AYpCA;AFOA,AIZA,AHSA,Af6CA,AoB5DA,AHSA,Af6CA,AQxBA,AFMA,ACHA,ALeA,AYpCA;AFOA,ACHA,Af6CA,AoB5DA,AHSA,Af6CA,AQxBA,AFMA,ACHA,ALeA,AYpCA;AFOA,ACHA,Af6CA,AoB5DA,AHSA,Af6CA,AQxBA,AFMA,ACHA,ALeA,AYpCA;AFOA,ACHA,Af6CA,AoB5DA,AHSA,Af6CA,AQxBA,AFMA,ACHA,ALeA,AYpCA;AFOA,ACHA,Af6CA,AoB5DA,AHSA,Af6CA,AQxBA,AFMA,ACHA,ALeA,AYpCA;AFOA,ACHA,Af6CA,AoB5DA,AHSA,Af6CA,AQxBA,AFMA,ACHA,ALeA,AYpCA;AFOA,ACHA,Af6CA,AoB5DA,AHSA,Af6CA,AQxBA,AFMA,ACHA,ALeA,AYpCA;AFOA,ACHA,Af6CA,AoB5DA,AHSA,Af6CA,AQxBA,AFMA,AJYA,AYpCA;AFOA,ACHA,Af6CA,AoB5DA,AHSA,Af6CA,AQxBA,AFMA,AJYA,AYpCA;AFOA,ACHA,Af6CA,AoB5DA,AHSA,Af6CA,AQxBA,AFMA,AJYA,AYpCA;AFOA,ACHA,Af6CA,AoB5DA,AHSA,Af6CA,AQxBA,AFMA,AJYA,AYpCA;AFOA,ACHA,Af6CA,AoB5DA,AHSA,Af6CA,AQxBA,AFMA,AJYA,AYpCA;AFOA,ACHA,Af6CA,AoB5DA,AHSA,Af6CA,AQxBA,AFMA,AJYA,AYpCA;AFOA,ACHA,Af6CA,AoB5DA,AHSA,Af6CA,AQxBA,AFMA,AJYA,AYpCA;ADIA,Af6CA,AoB5DA,AHSA,Af6CA,AQxBA,AFMA,AJYA,AYpCA;ADIA,Af6CA,AoB5DA,AHSA,Af6CA,AQxBA,AFMA,AJYA,AYpCA;ADIA,Af6CA,AoB5DA,AHSA,Af6CA,AQxBA,AFMA,AJYA,AYpCA;ADIA,Af6CA,AoB5DA,AHSA,Af6CA,AQxBA,AFMA,AJYA,AYpCA;ADIA,Af6CA,AoB5DA,AHSA,Af6CA,AQxBA,AFMA,AJYA,AYpCA;ADIA,Af6CA,AoB5DA,AHSA,Af6CA,AQxBA,AFMA,AJYA,AYpCA;ADIA,Af6CA,AoB5DA,AHSA,Af6CA,AQxBA,AFMA,AJYA,AYpCA;ADIA,Af6CA,AoB5DA,AHSA,Af6CA,AQxBA,AFMA,AJYA,AYpCA;ADIA,Af6CA,AoB5DA,AHSA,Af6CA,AQxBA,AFMA,AJYA,AYpCA;ADIA,Af6CA,AoB5DA,AHSA,Af6CA,AQxBA,AFMA,AJYA,AYpCA;ADIA,Af6CA,AoB5DA,AHSA,Af6CA,AQxBA,AFMA,AJYA,AYpCA;ADIA,Af6CA,AoB5DA,AHSA,Af6CA,AQxBA,AFMA,AJYA,AYpCA;ADIA,Af6CA,AoB5DA,AHSA,Af6CA,AQxBA,AFMA,AJYA,AYpCA;ADIA,Af6CA,AoB5DA,AHSA,Af6CA,AQxBA,AFMA,AJYA,AYpCA;ADIA,Af6CA,AoB5DA,AHSA,Af6CA,AQxBA,AFMA,AJYA,AYpCA;ADIA,Af6CA,AoB5DA,AHSA,Af6CA,AQxBA,AFMA,AJYA,AYpCA;ADIA,Af6CA,AoB5DA,AHSA,Af6CA,AQxBA,AFMA,AJYA,AYpCA;ADIA,Af6CA,AoB5DA,AHSA,Af6CA,AQxBA,AFMA,AJYA,AYpCA;ADIA,Af6CA,AoB5DA,AHSA,Af6CA,AQxBA,AFMA,AJYA,AYpCA;ADIA,Af6CA,AoB5DA,AHSA,Af6CA,AQxBA,AFMA,AJYA,AYpCA;ADIA,Af6CA,AoB5DA,AHSA,Af6CA,AQxBA,AFMA,AJYA,AYpCA;ADIA,Af6CA,AoB5DA,AHSA,Af6CA,AQxBA,ANkBA,AYpCA;ADIA,Af6CA,AoB5DA,AHSA,Af6CA,AQxBA,ANkBA,AYpCA;ADIA,Af6CA,AoB5DA,AHSA,Af6CA,AQxBA,ANkBA,AYpCA;ADIA,Af6CA,AoB5DA,AHSA,Af6CA,AQxBA,ANkBA,AYpCA;ADIA,Af6CA,AoB5DA,AHSA,Af6CA,AQxBA,ANkBA,AYpCA;ADIA,Af6CA,AoB5DA,AHSA,Af6CA,AQxBA,ANkBA,AYpCA;ADIA,Af6CA,AoB5DA,AHSA,Af6CA,AQxBA,ANkBA,AYpCA;ADIA,Af6CA,AoB5DA,AHSA,Af6CA,AQxBA,ANkBA,AYpCA;ADIA,Af6CA,AoB5DA,AHSA,Af6CA,AQxBA,ANkBA,AYpCA;ADIA,Af6CA,AoB5DA,AHSA,Af6CA,AQxBA,ANkBA,AYpCA;ADIA,Af6CA,AoB5DA,AHSA,Af6CA,AQxBA,ANkBA,AYpCA;ADIA,Af6CA,AoB5DA,AHSA,Af6CA,AQxBA,ANkBA,AYpCA;ADIA,Af6CA,AoB5DA,AHSA,Af6CA,AQxBA,ANkBA,AYpCA;ADIA,Af6CA,AoB5DA,AHSA,Af6CA,AQxBA,AMlBA;ADIA,Af6CA,AoB5DA,AHSA,Af6CA,Ac1CA;ADIA,Af6CA,AoB5DA,AHSA,Af6CA,Ac1CA;ADIA,Af6CA,AoB5DA,AHSA,Af6CA,Ac1CA;ADIA,Af6CA,AoB5DA,AHSA,Af6CA,Ac1CA;ADIA,Af6CA,AoB5DA,AHSA,Af6CA,Ac1CA;ADIA,Af6CA,AoB5DA,AHSA,Af6CA,Ac1CA;ADIA,Af6CA,AoB5DA,AHSA,Af6CA,Ac1CA;ADIA,Af6CA,AoB5DA,AHSA,Af6CA,Ac1CA;ADIA,Af6CA,AoB5DA,AHSA,Af6CA,Ac1CA;ADIA,Af6CA,AiBnDA,Af6CA,Ac1CA;ADIA,Af6CA,AiBnDA,Af6CA,Ac1CA;ADIA,Af6CA,AiBnDA,Af6CA,Ac1CA;ADIA,Af6CA,AiBnDA,Af6CA,Ac1CA;ADIA,Af6CA,AiBnDA,Af6CA,Ac1CA;ADIA,Af6CA,AiBnDA,Af6CA,Ac1CA;ADIA,Af6CA,AiBnDA,Af6CA,Ac1CA;ADIA,Af6CA,AiBnDA,Af6CA,Ac1CA;ADIA,Af6CA,AiBnDA,Af6CA,Ac1CA;ADIA,Af6CA,AiBnDA,Af6CA,Ac1CA;ADIA,Af6CA,AiBnDA,Af6CA,Ac1CA;ADIA,Af6CA,AiBnDA,Af6CA,Ac1CA;ADIA,Af6CA,AiBnDA,Af6CA,Ac1CA;ADIA,Af6CA,AiBnDA,Af6CA,Ac1CA;ADIA,Af6CA,AiBnDA,Af6CA,Ac1CA;ADIA,Af6CA,AiBnDA,Af6CA;AatCA,Af6CA,AiBnDA,Af6CA;AatCA,Af6CA,AiBnDA,Af6CA;AatCA,Af6CA,AiBnDA,Af6CA;AatCA,Af6CA,AiBnDA,Af6CA;AatCA,Af6CA,AiBnDA,Af6CA;AatCA,Af6CA,AiBnDA,Af6CA;AatCA,Af6CA,AiBnDA,Af6CA;AatCA,Af6CA,AiBnDA,Af6CA;AatCA,Af6CA,AiBnDA,Af6CA;AatCA,Af6CA,AiBnDA,Af6CA;AatCA,Af6CA,AiBnDA,Af6CA;AatCA,Af6CA,AiBnDA,Af6CA;AatCA,Af6CA,AiBnDA,Af6CA;AFOA,AiBnDA,Af6CA;AFOA,AiBnDA,Af6CA;AFOA,AiBnDA,Af6CA;AFOA,AiBnDA,Af6CA;AFOA,AiBnDA,Af6CA;AFOA,AiBnDA,Af6CA;AFOA,AiBnDA,Af6CA;AFOA,AiBnDA,Af6CA;AFOA,AiBnDA,Af6CA;AFOA,AiBnDA,Af6CA;AFOA,AiBnDA,Af6CA;AFOA,AiBnDA,Af6CA;AFOA,AiBnDA,Af6CA;AFOA,AiBnDA,Af6CA;AFOA,AiBnDA,Af6CA;AFOA,AiBnDA,Af6CA;AFOA,AiBnDA,Af6CA;AFOA,AiBnDA,Af6CA;AFOA,AiBnDA,Af6CA;AFOA,AiBnDA,Af6CA;AFOA,AiBnDA,Af6CA;AFOA,AiBnDA,Af6CA;AFOA,AiBnDA,Af6CA;AFOA,AiBnDA,Af6CA;AFOA,AiBnDA,Af6CA;AFOA,AiBnDA,Af6CA;AFOA,AiBnDA,Af6CA;AFOA,AiBnDA,Af6CA;AFOA,AiBnDA,Af6CA;AFOA,AiBnDA,Af6CA;AFOA,AiBnDA,Af6CA;AFOA,AiBnDA,Af6CA;AFOA,AiBnDA,Af6CA;AFOA,AiBnDA,Af6CA;AFOA,AiBnDA,Af6CA;AFOA,AiBnDA,Af6CA;AFOA,AiBnDA,Af6CA;AFOA,AiBnDA,Af6CA;AFOA,AiBnDA,Af6CA;AFOA,AiBnDA,Af6CA;AFOA,AiBnDA,Af6CA;AFOA,AiBnDA,Af6CA;AFOA,AiBnDA,Af6CA;AFOA,AiBnDA,Af6CA;AFOA,AiBnDA,Af6CA;AFOA,AiBnDA,Af6CA;AFOA,AiBnDA,Af6CA;AFOA,AiBnDA,Af6CA;AFOA,AiBnDA,Af6CA;AFOA,AiBnDA,Af6CA;AFOA,AiBnDA,Af6CA;AFOA,AiBnDA,Af6CA;AFOA,AiBnDA,Af6CA;AFOA,AiBnDA,Af6CA;AFOA,AiBnDA,Af6CA;AFOA,AiBnDA,Af6CA;AFOA,AiBnDA,Af6CA;AFOA,AiBnDA,Af6CA;AFOA,AiBnDA,Af6CA;AFOA,AiBnDA,Af6CA;AFOA,AiBnDA,Af6CA;AFOA,AiBnDA,Af6CA;AFOA,AiBnDA,Af6CA;AFOA,AiBnDA,Af6CA;AFOA,AiBnDA,Af6CA;AFOA,AiBnDA,Af6CA;AFOA,AiBnDA,Af6CA;AFOA,AiBnDA,Af6CA;AFOA,AiBnDA,Af6CA;AFOA,AiBnDA,Af6CA;AFOA,AiBnDA,Af6CA;AFOA,AiBnDA,Af6CA;AFOA,AiBnDA,Af6CA;AFOA,AiBnDA,Af6CA;AFOA,AiBnDA,Af6CA;AFOA,AiBnDA,Af6CA;AFOA,AiBnDA,Af6CA;AFOA,AiBnDA,Af6CA;AFOA,AiBnDA,Af6CA;AFOA,AiBnDA,Af6CA;AFOA,AiBnDA,Af6CA;AFOA,AiBnDA,Af6CA;AFOA,AiBnDA,Af6CA;AFOA,AiBnDA,Af6CA;AFOA,AiBnDA,Af6CA;AFOA,AiBnDA,Af6CA;AFOA,AiBnDA,Af6CA;AFOA,AiBnDA,Af6CA;AFOA,AiBnDA,Af6CA;AFOA,AiBnDA,Af6CA;AFOA,AiBnDA,Af6CA;AFOA,AiBnDA,Af6CA;Ae5CA,Af6CA;Ae5CA,Af6CA;Ae5CA,Af6CA;Ae5CA,Af6CA;Ae5CA,Af6CA;Ae5CA,Af6CA;Ae5CA,Af6CA;Ae5CA,Af6CA;Ae5CA,Af6CA;Ae5CA,Af6CA;Ae5CA,Af6CA;Ae5CA,Af6CA;Ae5CA,Af6CA;Ae5CA,Af6CA;Ae5CA,Af6CA;Ae5CA,Af6CA;Ae5CA,Af6CA;Ae5CA,Af6CA;Ae5CA,Af6CA;Ae5CA,Af6CA;Ae5CA,Af6CA;Ae5CA,Af6CA;Ae5CA,Af6CA;Ae5CA,Af6CA;Ae5CA,Af6CA;Ae5CA,Af6CA;Ae5CA,Af6CA;Ae5CA,Af6CA;Ae5CA,Af6CA;Ae5CA,Af6CA;Ae5CA,Af6CA;Ae5CA,Af6CA;Ae5CA,Af6CA;Ae5CA,Af6CA;Ae5CA,Af6CA;Ae5CA,Af6CA;Ae5CA,Af6CA;Ae5CA,Af6CA;Ae5CA,Af6CA;Ae5CA,Af6CA;Ae5CA,Af6CA;Ae5CA,Af6CA;Ae5CA,Af6CA;Ae5CA,Af6CA;Ae5CA,Af6CA;Ae5CA,Af6CA;Ae5CA,Af6CA;Ae5CA,Af6CA;Ae5CA,Af6CA;Ae5CA,Af6CA;Ae5CA,Af6CA;Ae5CA,Af6CA;Ae5CA,Af6CA;Ae5CA,Af6CA;Ae5CA,Af6CA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.js", "sourcesContent": ["\nconst cloudbase_1 = require(\"./cloudbase\");\nconst symbol_1 = require(\"./const/symbol\");\nconst request_1 = require(\"./utils/request\");\nconst { version } = require('../package.json');\nmodule.exports = {\n    request: request_1.extraRequest,\n    init: (config) => {\n        return new cloudbase_1.CloudBase(config);\n    },\n    parseContext: (context) => {\n        // 校验context 是否正确\n        return cloudbase_1.CloudBase.parseContext(context);\n    },\n    version,\n    getCloudbaseContext: (context) => {\n        return cloudbase_1.CloudBase.getCloudbaseContext(context);\n    },\n    /**\n     * 云函数下获取当前env\n     */\n    SYMBOL_CURRENT_ENV: symbol_1.SYMBOL_CURRENT_ENV\n};\n", "\nvar __rest = (this && this.__rest) || function (s, e) {\n    var t = {};\n    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\n        t[p] = s[p];\n    if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\n        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))\n                t[p[i]] = s[p[i]];\n        }\n    return t;\n};\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst database_1 = require(\"@cloudbase/database\");\nconst functions_1 = require(\"./functions\");\nconst auth_1 = require(\"./auth\");\nconst wx_1 = require(\"./wx\");\nconst storage_1 = require(\"./storage\");\nconst analytics_1 = require(\"./analytics\");\nconst dbRequest_1 = require(\"./utils/dbRequest\");\nconst log_1 = require(\"./log\");\nconst code_1 = require(\"./const/code\");\nconst utils_1 = require(\"./utils/utils\");\nconst axios_1 = __importDefault(require(\"axios\"));\nclass CloudBase {\n    static parseContext(context) {\n        if (typeof context !== 'object') {\n            throw utils_1.E(Object.assign({}, code_1.ERROR.INVALID_CONTEXT, { message: 'context 必须为对象类型' }));\n        }\n        let { memory_limit_in_mb, time_limit_in_ms, request_id, environ, function_version, namespace, function_name, environment } = context;\n        let parseResult = {};\n        try {\n            parseResult.memoryLimitInMb = memory_limit_in_mb;\n            parseResult.timeLimitIns = time_limit_in_ms;\n            parseResult.requestId = request_id;\n            parseResult.functionVersion = function_version;\n            parseResult.namespace = namespace;\n            parseResult.functionName = function_name;\n            // 存在environment 为新架构 上新字段 JSON序列化字符串\n            if (environment) {\n                parseResult.environment = JSON.parse(environment);\n                return parseResult;\n            }\n            // 不存在environment 则为老字段，老架构上存在bug，无法识别value含特殊字符(若允许特殊字符，影响解析，这里特殊处理)\n            const parseEnviron = environ.split(';');\n            let parseEnvironObj = {};\n            for (let i in parseEnviron) {\n                // value含分号影响切割，未找到= 均忽略\n                if (parseEnviron[i].indexOf('=') >= 0) {\n                    const equalIndex = parseEnviron[i].indexOf('=');\n                    const key = parseEnviron[i].slice(0, equalIndex);\n                    let value = parseEnviron[i].slice(equalIndex + 1);\n                    // value 含, 为数组\n                    if (value.indexOf(',') >= 0) {\n                        value = value.split(',');\n                    }\n                    parseEnvironObj[key] = value;\n                }\n            }\n            parseResult.environ = parseEnvironObj;\n        }\n        catch (err) {\n            throw utils_1.E(Object.assign({}, code_1.ERROR.INVALID_CONTEXT));\n        }\n        CloudBase.scfContext = parseResult;\n        return parseResult;\n    }\n    /**\n     * 获取当前函数内的所有环境变量(作为获取变量的统一方法，取值来源process.env 和 context)\n     */\n    static getCloudbaseContext(context) {\n        // WX_CONTEXT_ENV  WX_APPID WX_OPENID WX_UNIONID WX_API_TOKEN\n        // TCB_CONTEXT_ENV TCB_ENV TCB_SEQID TRIGGER_SRC LOGINTYPE QQ_OPENID QQ_APPID TCB_UUID TCB_ISANONYMOUS_USER TCB_SESSIONTOKEN TCB_CUSTOM_USER_ID TCB_SOURCE_IP TCB_SOURCE TCB_ROUTE_KEY TCB_HTTP_CONTEXT TCB_CONTEXT_CNFG\n        // 解析process.env\n        const { TENCENTCLOUD_RUNENV, SCF_NAMESPACE, TCB_CONTEXT_KEYS, TENCENTCLOUD_SECRETID, TENCENTCLOUD_SECRETKEY, TENCENTCLOUD_SESSIONTOKEN, TRIGGER_SRC, WX_CONTEXT_KEYS, WX_TRIGGER_API_TOKEN_V0, WX_CLIENTIP, WX_CLIENTIPV6, _SCF_TCB_LOG, TCB_CONTEXT_CNFG, LOGINTYPE } = process.env;\n        let contextEnv = {};\n        if (context) {\n            const { environment, environ } = CloudBase.parseContext(context);\n            contextEnv = environment || environ || {};\n        }\n        // 从TCB_CONTEXT_KEYS 和 WX_CONTEXT_KEYS中解析环境变量 取值优先级为 context > process.env\n        const tcb_context_keys = contextEnv.TCB_CONTEXT_KEYS || TCB_CONTEXT_KEYS;\n        const wx_context_keys = contextEnv.WX_CONTEXT_KEYS || WX_CONTEXT_KEYS;\n        let rawContext = {\n            TENCENTCLOUD_RUNENV,\n            SCF_NAMESPACE,\n            TCB_CONTEXT_KEYS,\n            TENCENTCLOUD_SECRETID,\n            TENCENTCLOUD_SECRETKEY,\n            TENCENTCLOUD_SESSIONTOKEN,\n            TRIGGER_SRC,\n            WX_TRIGGER_API_TOKEN_V0,\n            WX_CLIENTIP,\n            WX_CLIENTIPV6,\n            WX_CONTEXT_KEYS,\n            _SCF_TCB_LOG,\n            TCB_CONTEXT_CNFG,\n            LOGINTYPE\n        };\n        // 遍历keys\n        if (tcb_context_keys) {\n            try {\n                const tcbKeysList = tcb_context_keys.split(',');\n                for (let item of tcbKeysList) {\n                    rawContext[item] = contextEnv[item] || process.env[item];\n                }\n            }\n            catch (e) { }\n        }\n        if (wx_context_keys) {\n            try {\n                const wxKeysList = wx_context_keys.split(',');\n                for (let item of wxKeysList) {\n                    rawContext[item] = contextEnv[item] || process.env[item];\n                }\n            }\n            catch (e) { }\n        }\n        rawContext = Object.assign({}, rawContext, contextEnv);\n        let finalContext = {};\n        for (let key in rawContext) {\n            if (rawContext[key] !== undefined) {\n                finalContext[key] = rawContext[key];\n            }\n        }\n        return finalContext;\n    }\n    constructor(config) {\n        this.init(config);\n    }\n    init(config = {}) {\n        let { debug, secretId, secretKey, sessionToken, env, timeout, headers = {}, throwOnCode } = config, restConfig = __rest(config, [\"debug\", \"secretId\", \"secretKey\", \"sessionToken\", \"env\", \"timeout\", \"headers\", \"throwOnCode\"]);\n        if ((secretId && !secretKey) || (!secretId && secretKey)) {\n            throw utils_1.E(Object.assign({}, code_1.ERROR.INVALID_PARAM, { message: 'secretId and secretKey must be a pair' }));\n        }\n        const newConfig = Object.assign({}, restConfig, { debug: !!debug, secretId: secretId, secretKey: secretKey, sessionToken: sessionToken, envName: env, headers: Object.assign({}, headers), timeout: timeout || 15000, throwOnCode: throwOnCode !== undefined ? throwOnCode : true });\n        this.config = newConfig;\n        this.extensionMap = {};\n    }\n    registerExtension(ext) {\n        this.extensionMap[ext.name] = ext;\n    }\n    async invokeExtension(name, opts) {\n        const ext = this.extensionMap[name];\n        if (!ext) {\n            throw Error(`扩展${name} 必须先注册`);\n        }\n        console.log(opts);\n        return ext.invoke(opts, this);\n    }\n    database(dbConfig = {}) {\n        database_1.Db.reqClass = dbRequest_1.DBRequest;\n        // 兼容方法预处理\n        if (Object.prototype.toString.call(dbConfig).slice(8, -1) !== 'Object') {\n            throw utils_1.E(Object.assign({}, code_1.ERROR.INVALID_PARAM, { message: 'dbConfig must be an object' }));\n        }\n        if (dbConfig && dbConfig.env) {\n            // env变量名转换\n            dbConfig.envName = dbConfig.env;\n            delete dbConfig.env;\n        }\n        return new database_1.Db(Object.assign({}, this.config, dbConfig));\n    }\n    /**\n     * 调用云函数\n     *\n     * @param param0\n     * @param opts\n     */\n    callFunction(callFunctionOptions, opts) {\n        return functions_1.callFunction(this, callFunctionOptions, opts);\n    }\n    auth() {\n        return auth_1.auth(this);\n    }\n    /**\n     * openapi调用\n     *\n     * @param param0\n     * @param opts\n     */\n    callWxOpenApi(wxOpenApiOptions, opts) {\n        return wx_1.callWxOpenApi(this, wxOpenApiOptions, opts);\n    }\n    /**\n     * wxpayapi调用\n     *\n     * @param param0\n     * @param opts\n     */\n    callWxPayApi(wxOpenApiOptions, opts) {\n        return wx_1.callWxPayApi(this, wxOpenApiOptions, opts);\n    }\n    /**\n     * wxpayapi调用\n     *\n     * @param param0\n     * @param opts\n     */\n    wxCallContainerApi(wxOpenApiOptions, opts) {\n        return wx_1.wxCallContainerApi(this, wxOpenApiOptions, opts);\n    }\n    /**\n     * 微信云调用\n     *\n     * @param param0\n     * @param opts\n     */\n    callCompatibleWxOpenApi(wxOpenApiOptions, opts) {\n        return wx_1.callCompatibleWxOpenApi(this, wxOpenApiOptions, opts);\n    }\n    /**\n     * 上传文件\n     *\n     * @param param0\n     * @param opts\n     */\n    uploadFile({ cloudPath, fileContent }, opts) {\n        return storage_1.uploadFile(this, { cloudPath, fileContent }, opts);\n    }\n    /**\n     * 删除文件\n     *\n     * @param param0\n     * @param opts\n     */\n    deleteFile({ fileList }, opts) {\n        return storage_1.deleteFile(this, { fileList }, opts);\n    }\n    /**\n     * 获取临时连接\n     *\n     * @param param0\n     * @param opts\n     */\n    getTempFileURL({ fileList }, opts) {\n        return storage_1.getTempFileURL(this, { fileList }, opts);\n    }\n    /**\n     * 下载文件\n     *\n     * @param params\n     * @param opts\n     */\n    downloadFile(params, opts) {\n        return storage_1.downloadFile(this, params, opts);\n    }\n    /**\n     * 获取上传元数据\n     *\n     * @param param0\n     * @param opts\n     */\n    getUploadMetadata({ cloudPath }, opts) {\n        return storage_1.getUploadMetadata(this, { cloudPath }, opts);\n    }\n    getFileAuthority({ fileList }, opts) {\n        return storage_1.getFileAuthority(this, { fileList }, opts);\n    }\n    /**\n     * 获取logger\n     *\n     */\n    logger() {\n        if (!this.clsLogger) {\n            this.clsLogger = log_1.logger();\n        }\n        return this.clsLogger;\n    }\n    analytics(reportData) {\n        return analytics_1.analytics(this, reportData);\n    }\n    // shim for tcb extension ci\n    get requestClient() {\n        return {\n            get: axios_1.default,\n            post: axios_1.default,\n            put: axios_1.default,\n            delete: axios_1.default\n        };\n    }\n}\nexports.CloudBase = CloudBase;\n", "\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst httpRequest_1 = __importDefault(require(\"../utils/httpRequest\"));\nconst utils_1 = require(\"../utils/utils\");\nconst code_1 = require(\"../const/code\");\nconst cloudbase_1 = require(\"../cloudbase\");\n/**\n * 调用云函数\n * @param {String} name  函数名\n * @param {Object} functionParam 函数参数\n * @return {Promise}\n */\nasync function callFunction(cloudbase, { name, qualifier, data }, opts) {\n    const { TCB_ROUTE_KEY } = cloudbase_1.CloudBase.getCloudbaseContext();\n    let transformData;\n    try {\n        transformData = data ? JSON.stringify(data) : '';\n    }\n    catch (e) {\n        throw utils_1.E(Object.assign({}, e, { code: code_1.ERROR.INVALID_PARAM.code, message: '对象出现了循环引用' }));\n    }\n    if (!name) {\n        throw utils_1.E(Object.assign({}, code_1.ERROR.INVALID_PARAM, { message: '函数名不能为空' }));\n    }\n    const params = {\n        action: 'functions.invokeFunction',\n        function_name: name,\n        qualifier: qualifier,\n        // async: async,\n        request_data: transformData\n    };\n    return httpRequest_1.default({\n        config: cloudbase.config,\n        params,\n        method: 'post',\n        opts,\n        headers: Object.assign({ 'content-type': 'application/json' }, (TCB_ROUTE_KEY ? { 'X-Tcb-Route-Key': TCB_ROUTE_KEY } : {}))\n    }).then(res => {\n        if (res.code) {\n            return res;\n        }\n        let result;\n        try {\n            result = JSON.parse(res.data.response_data);\n        }\n        catch (e) {\n            result = res.data.response_data;\n        }\n        return {\n            result,\n            requestId: res.requestId\n        };\n    });\n}\nexports.callFunction = callFunction;\n", "\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nvar __importStar = (this && this.__importStar) || function (mod) {\n    if (mod && mod.__esModule) return mod;\n    var result = {};\n    if (mod != null) for (var k in mod) if (Object.hasOwnProperty.call(mod, k)) result[k] = mod[k];\n    result[\"default\"] = mod;\n    return result;\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst http_1 = __importDefault(require(\"http\"));\nconst tracing_1 = require(\"./tracing\");\nconst utils = __importStar(require(\"./utils\"));\nconst code_1 = require(\"../const/code\");\nconst symbol_1 = require(\"../const/symbol\");\nconst cloudbase_1 = require(\"../cloudbase\");\nconst request_1 = require(\"./request\");\nconst requestHook_1 = require(\"./requestHook\");\nconst wxCloudToken_1 = require(\"./wxCloudToken\");\nconst signature_nodejs_1 = require(\"@cloudbase/signature-nodejs\");\nconst url_1 = __importDefault(require(\"url\"));\n// import { version } from '../../package.json'\nconst secretManager_1 = __importDefault(require(\"./secretManager\"));\nconst { version } = require('../../package.json');\nconst { E, second, processReturn, getServerInjectUrl } = utils;\nclass Request {\n    constructor(args) {\n        this.urlPath = '/admin';\n        this.defaultTimeout = 15000;\n        this.timestamp = new Date().valueOf();\n        this.tracingInfo = tracing_1.generateTracingInfo();\n        this.slowWarnTimer = null;\n        // 请求参数\n        this.hooks = {};\n        this.args = args;\n        this.config = args.config;\n        this.opts = args.opts || {};\n        this.secretManager = new secretManager_1.default();\n    }\n    /**\n     * 最终发送请求\n     */\n    async request() {\n        // 校验密钥是否存在\n        await this.validateSecretIdAndKey();\n        // 构造请求参数\n        const params = await this.makeParams();\n        const opts = await this.makeReqOpts(params);\n        const action = this.getAction();\n        const key = {\n            functions: 'function_name',\n            database: 'collectionName',\n            wx: 'apiName'\n        }[action.split('.')[0]];\n        const argopts = this.opts;\n        const config = this.config;\n        // 发请求时未找到有效环境字段\n        if (!params.envName) {\n            // 检查config中是否有设置\n            if (config.envName) {\n                return processReturn(config.throwOnCode, Object.assign({}, code_1.ERROR.INVALID_PARAM, { message: '未取到init 指定 env！' }));\n            }\n            else {\n                console.warn(`当前未指定env，将默认使用第一个创建的环境！`);\n            }\n        }\n        // 注意：必须初始化为 null\n        let retryOptions = null;\n        if (argopts.retryOptions) {\n            retryOptions = argopts.retryOptions;\n        }\n        else if (config.retries && typeof config.retries === 'number') {\n            retryOptions = { retries: config.retries };\n        }\n        return request_1.extraRequest(opts, {\n            debug: config.debug,\n            op: `${action}:${this.args.params[key]}@${params.envName}`,\n            seqId: this.getSeqId(),\n            retryOptions: retryOptions,\n            timingsMeasurerOptions: config.timingsMeasurerOptions || {}\n        }).then((response) => {\n            this.slowWarnTimer && clearTimeout(this.slowWarnTimer);\n            const { body } = response;\n            if (response.statusCode === 200) {\n                let res;\n                try {\n                    res = typeof body === 'string' ? JSON.parse(body) : body;\n                    if (this.hooks && this.hooks.handleData) {\n                        res = this.hooks.handleData(res, null, response, body);\n                    }\n                }\n                catch (e) {\n                    res = body;\n                }\n                return res;\n            }\n            else {\n                const e = E({\n                    code: response.statusCode,\n                    message: ` ${response.statusCode} ${http_1.default.STATUS_CODES[response.statusCode]} | [${opts.url}]`\n                });\n                throw e;\n            }\n        });\n    }\n    setHooks(hooks) {\n        Object.assign(this.hooks, hooks);\n    }\n    getSeqId() {\n        return this.tracingInfo.seqId;\n    }\n    /**\n     * 接口action\n     */\n    getAction() {\n        const { params } = this.args;\n        const { action } = params;\n        return action;\n    }\n    /**\n     * 设置超时warning\n     */\n    setSlowWarning(timeout) {\n        const action = this.getAction();\n        const { seqId } = this.tracingInfo;\n        this.slowWarnTimer = setTimeout(() => {\n            /* istanbul ignore next */\n            const msg = `Your current request ${action ||\n                ''} is longer than 3s, it may be due to the network or your query performance | [${seqId}]`;\n            /* istanbul ignore next */\n            console.warn(msg);\n        }, timeout);\n    }\n    /**\n     * 构造params\n     */\n    async makeParams() {\n        const { TCB_SESSIONTOKEN, TCB_ENV, SCF_NAMESPACE } = cloudbase_1.CloudBase.getCloudbaseContext();\n        const args = this.args;\n        const opts = this.opts;\n        const config = this.config;\n        const { eventId } = this.tracingInfo;\n        const crossAuthorizationData = opts.getCrossAccountInfo && (await opts.getCrossAccountInfo()).authorization;\n        const { wxCloudApiToken, wxCloudbaseAccesstoken } = wxCloudToken_1.getWxCloudToken();\n        const params = Object.assign({}, args.params, { envName: config.envName, eventId,\n            wxCloudApiToken,\n            wxCloudbaseAccesstoken, tcb_sessionToken: TCB_SESSIONTOKEN || '', sessionToken: config.sessionToken, crossAuthorizationToken: crossAuthorizationData\n                ? Buffer.from(JSON.stringify(crossAuthorizationData)).toString('base64')\n                : '' });\n        // 取当前云函数环境时，替换为云函数下环境变量\n        if (params.envName === symbol_1.SYMBOL_CURRENT_ENV) {\n            params.envName = TCB_ENV || SCF_NAMESPACE;\n        }\n        // 过滤value undefined\n        utils.filterUndefined(params);\n        return params;\n    }\n    /**\n     *  构造请求项\n     */\n    async makeReqOpts(params) {\n        const config = this.config;\n        const args = this.args;\n        const isInternal = await utils.checkIsInternalAsync();\n        const url = this.getUrl({ isInternal });\n        const method = this.getMethod();\n        const opts = {\n            url: url,\n            method,\n            // 先取模块的timeout，没有则取sdk的timeout，还没有就使用默认值\n            // timeout: args.timeout || config.timeout || 15000,\n            timeout: this.getTimeout(),\n            // 优先取config，其次取模块，最后取默认\n            headers: await this.getHeaders(url),\n            proxy: config.proxy\n        };\n        opts.keepalive = config.keepalive === true;\n        if (args.method === 'post') {\n            if (args.isFormData) {\n                opts.formData = params;\n                opts.encoding = null;\n            }\n            else {\n                opts.body = params;\n                opts.json = true;\n            }\n        }\n        else {\n            /* istanbul ignore next */\n            opts.qs = params;\n        }\n        return opts;\n    }\n    /**\n     * 协议\n     */\n    getProtocol() {\n        return this.config.isHttp === true ? 'http' : 'https';\n    }\n    /**\n     * 请求方法\n     */\n    getMethod() {\n        return this.args.method || 'get';\n    }\n    /**\n     * 超时时间\n     */\n    getTimeout() {\n        const { opts = {} } = this.args;\n        // timeout优先级 自定义接口timeout > config配置timeout > 默认timeout\n        return opts.timeout || this.config.timeout || this.defaultTimeout;\n    }\n    /**\n     * 校验密钥和token是否存在\n     */\n    async validateSecretIdAndKey() {\n        const { TENCENTCLOUD_SECRETID, TENCENTCLOUD_SECRETKEY, TENCENTCLOUD_SESSIONTOKEN } = cloudbase_1.CloudBase.getCloudbaseContext(); // 放在此处是为了兼容本地环境下读环境变量\n        const isInSCF = utils.checkIsInScf();\n        const isInContainer = utils.checkIsInEks();\n        let opts = this.opts;\n        let getCrossAccountInfo = opts.getCrossAccountInfo || this.config.getCrossAccountInfo;\n        /* istanbul ignore if  */\n        if (getCrossAccountInfo) {\n            let crossAccountInfo = await getCrossAccountInfo();\n            let { credential } = await getCrossAccountInfo();\n            let { secretId, secretKey, token } = credential || {};\n            this.config = Object.assign({}, this.config, { secretId,\n                secretKey, sessionToken: token });\n            this.opts.getCrossAccountInfo = () => Promise.resolve(crossAccountInfo);\n            if (!this.config.secretId || !this.config.secretKey) {\n                throw E(Object.assign({}, code_1.ERROR.INVALID_PARAM, { message: 'missing secretId or secretKey of tencent cloud' }));\n            }\n        }\n        else {\n            const { secretId, secretKey } = this.config;\n            if (!secretId || !secretKey) {\n                /* istanbul ignore if  */\n                if (isInContainer) {\n                    // 这种情况有可能是在容器内，此时尝试拉取临时\n                    const tmpSecret = await this.secretManager.getTmpSecret();\n                    this.config = Object.assign({}, this.config, { secretId: tmpSecret.id, secretKey: tmpSecret.key, sessionToken: tmpSecret.token });\n                    return;\n                }\n                if (!TENCENTCLOUD_SECRETID || !TENCENTCLOUD_SECRETKEY) {\n                    if (isInSCF) {\n                        throw E(Object.assign({}, code_1.ERROR.INVALID_PARAM, { message: 'missing authoration key, redeploy the function' }));\n                    }\n                    else {\n                        throw E(Object.assign({}, code_1.ERROR.INVALID_PARAM, { message: 'missing secretId or secretKey of tencent cloud' }));\n                    }\n                }\n                else {\n                    this.config = Object.assign({}, this.config, { secretId: TENCENTCLOUD_SECRETID, secretKey: TENCENTCLOUD_SECRETKEY, sessionToken: TENCENTCLOUD_SESSIONTOKEN });\n                }\n            }\n        }\n    }\n    /**\n     *\n     * 获取headers 此函数中设置authorization\n     */\n    async getHeaders(url) {\n        const config = this.config;\n        const { secretId, secretKey } = config;\n        const args = this.args;\n        const method = this.getMethod();\n        const { TCB_SOURCE } = cloudbase_1.CloudBase.getCloudbaseContext();\n        // Note: 云函数被调用时可能调用端未传递 SOURCE，TCB_SOURCE 可能为空\n        const SOURCE = utils.checkIsInScf() ? `${TCB_SOURCE || ''},scf` : ',not_scf';\n        let requiredHeaders = {\n            'User-Agent': `tcb-node-sdk/${version}`,\n            'x-tcb-source': SOURCE,\n            'x-client-timestamp': this.timestamp,\n            'X-SDK-Version': `tcb-node-sdk/${version}`,\n            Host: url_1.default.parse(url).host\n        };\n        if (config.version) {\n            requiredHeaders['X-SDK-Version'] = config.version;\n        }\n        if (this.tracingInfo.trace) {\n            requiredHeaders['x-tcb-tracelog'] = this.tracingInfo.trace;\n        }\n        const region = this.config.region || process.env.TENCENTCLOUD_REGION || '';\n        if (region) {\n            requiredHeaders['X-TCB-Region'] = region;\n        }\n        requiredHeaders = Object.assign({}, config.headers, args.headers, requiredHeaders);\n        const { authorization, timestamp } = signature_nodejs_1.sign({\n            secretId: secretId,\n            secretKey: secretKey,\n            method: method,\n            url: url,\n            params: await this.makeParams(),\n            headers: requiredHeaders,\n            withSignedParams: true,\n            timestamp: second() - 1\n        });\n        requiredHeaders['Authorization'] = authorization;\n        requiredHeaders['X-Signature-Expires'] = 600;\n        requiredHeaders['X-Timestamp'] = timestamp;\n        return Object.assign({}, requiredHeaders);\n    }\n    /**\n     * 获取url\n     * @param action\n     */\n    /* eslint-disable-next-line complexity */\n    getUrl(options = {\n        isInternal: false\n    }) {\n        if (utils.checkIsInScf()) {\n            // 云函数环境下，应该包含以下环境变量，如果没有，后续逻辑可能会有问题\n            if (!process.env.TENCENTCLOUD_REGION) {\n                console.error('[ERROR] missing `TENCENTCLOUD_REGION` environment');\n            }\n            if (!process.env.SCF_NAMESPACE) {\n                console.error('[ERROR] missing `SCF_NAMESPACE` environment');\n            }\n        }\n        const { TCB_ENV, SCF_NAMESPACE } = cloudbase_1.CloudBase.getCloudbaseContext();\n        // 优先级：用户配置 > 环境变量\n        const region = this.config.region || process.env.TENCENTCLOUD_REGION || '';\n        // 有地域信息则访问地域级别域名，无地域信息则访问默认域名，默认域名固定解析到上海地域保持兼容\n        const internetRegionEndpoint = region\n            ? `${region}.tcb-api.tencentcloudapi.com`\n            : `tcb-api.tencentcloudapi.com`;\n        const internalRegionEndpoint = region\n            ? `internal.${region}.tcb-api.tencentcloudapi.com`\n            : `internal.tcb-api.tencentcloudapi.com`;\n        // 同地域走内网，跨地域走公网\n        const isSameRegionVisit = this.config.region\n            ? this.config.region === process.env.TENCENTCLOUD_REGION\n            : true;\n        const endpoint = isSameRegionVisit && (options.isInternal)\n            ? internalRegionEndpoint\n            : internetRegionEndpoint;\n        const envName = this.config.envName || '';\n        const currEnv = TCB_ENV || SCF_NAMESPACE || '';\n        // 注意：特殊环境ID不能拼在请求地址的域名中，所以这里需要特殊处理\n        const envId = envName === symbol_1.SYMBOL_CURRENT_ENV || utils.isPageModuleName(envName)\n            ? currEnv\n            : envName;\n        const envEndpoint = utils.isValidEnvFormat(envId) ? `${envId}.${endpoint}` : endpoint;\n        const protocol = options.isInternal ? 'http' : this.getProtocol();\n        // 注意：云函数环境下有地域信息，云应用环境下不确定是否有，如果没有，用户必须显式的传入\n        const defaultUrl = `${protocol}://${envEndpoint}${this.urlPath}`;\n        const { eventId, seqId } = this.tracingInfo;\n        const { serviceUrl } = this.config;\n        const serverInjectUrl = getServerInjectUrl();\n        const url = serviceUrl || serverInjectUrl || defaultUrl;\n        const qs = cloudbase_1.CloudBase.scfContext\n            ? `&eventId=${eventId}&seqId=${seqId}&scfRequestId=${cloudbase_1.CloudBase.scfContext.requestId}`\n            : `&eventId=${eventId}&seqId=${seqId}`;\n        return url.includes('?') ? `${url}${qs}` : `${url}?${qs}`;\n    }\n}\nexports.Request = Request;\n// 业务逻辑都放在这里处理\nexports.default = async (args) => {\n    const req = new Request(args);\n    const config = args.config;\n    const { action } = args.params;\n    if (action === 'wx.openApi' || action === 'wx.wxPayApi') {\n        req.setHooks({ handleData: requestHook_1.handleWxOpenApiData });\n    }\n    if (action.startsWith('database')) {\n        req.setSlowWarning(3000);\n    }\n    try {\n        const res = await req.request();\n        // 检查res是否为return {code, message}回包\n        if (res && res.code) {\n            // 判断是否设置config._returnCodeByThrow = false\n            return processReturn(config.throwOnCode, res);\n        }\n        return res;\n    }\n    finally {\n        //\n    }\n};\n", "\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst cloudbase_1 = require(\"../cloudbase\");\nlet seqNum = 0;\nfunction getSeqNum() {\n    return ++seqNum;\n}\nfunction generateEventId() {\n    return Date.now().toString(16) + '_' + getSeqNum().toString(16);\n}\nexports.generateTracingInfo = () => {\n    let { TCB_SEQID, TCB_TRACELOG } = cloudbase_1.CloudBase.getCloudbaseContext();\n    TCB_SEQID = TCB_SEQID || '';\n    const eventId = generateEventId();\n    const seqId = TCB_SEQID ? `${TCB_SEQID}-${eventId}` : eventId;\n    return { eventId, seqId, trace: TCB_TRACELOG };\n};\n", "\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst cloudbase_1 = require(\"../cloudbase\");\nconst metadata_1 = require(\"./metadata\");\nclass TcbError extends Error {\n    constructor(error) {\n        super(error.message);\n        this.code = error.code;\n        this.message = error.message;\n        this.requestId = error.requestId;\n    }\n}\nexports.TcbError = TcbError;\nfunction isAppId(appIdStr) {\n    return /^[1-9][0-9]{4,64}$/gim.test(appIdStr);\n}\nexports.isAppId = isAppId;\nexports.filterValue = function filterValue(o, value) {\n    for (let key in o) {\n        if (o[key] === value) {\n            delete o[key];\n        }\n    }\n};\nexports.filterUndefined = function (o) {\n    return exports.filterValue(o, undefined);\n};\nexports.E = (errObj) => {\n    return new TcbError(errObj);\n};\nfunction isNonEmptyString(str) {\n    return typeof str === 'string' && str !== '';\n}\nexports.isNonEmptyString = isNonEmptyString;\nfunction checkIsInScf() {\n    // TENCENTCLOUD_RUNENV\n    return process.env.TENCENTCLOUD_RUNENV === 'SCF';\n}\nexports.checkIsInScf = checkIsInScf;\nfunction checkIsInEks() {\n    // EKS_CLUSTER_ID=cls-abcdefg\n    // EKS_LOGS_xxx=\n    // return isNonEmptyString(process.env.EKS_CLUSTER_ID)\n    return !!process.env.KUBERNETES_SERVICE_HOST;\n}\nexports.checkIsInEks = checkIsInEks;\nconst kSumeruEnvSet = new Set(['formal', 'pre', 'test']);\nfunction checkIsInSumeru() {\n    // SUMERU_ENV=formal | test | pre\n    return kSumeruEnvSet.has(process.env.SUMERU_ENV);\n}\nexports.checkIsInSumeru = checkIsInSumeru;\nasync function checkIsInTencentCloud() {\n    return isNonEmptyString(await metadata_1.lookupAppId());\n}\nexports.checkIsInTencentCloud = checkIsInTencentCloud;\nfunction second() {\n    // istanbul ignore next\n    return Math.floor(new Date().getTime() / 1000);\n}\nexports.second = second;\nfunction processReturn(throwOnCode, res) {\n    if (throwOnCode === false) {\n        // 不抛报错，正常return，兼容旧逻辑\n        return res;\n    }\n    throw exports.E(Object.assign({}, res));\n}\nexports.processReturn = processReturn;\nfunction getServerInjectUrl() {\n    const tcbContextConfig = getTcbContextConfig();\n    return tcbContextConfig['URL'] || '';\n}\nexports.getServerInjectUrl = getServerInjectUrl;\nfunction getTcbContextConfig() {\n    try {\n        const { TCB_CONTEXT_CNFG } = cloudbase_1.CloudBase.getCloudbaseContext();\n        if (TCB_CONTEXT_CNFG) {\n            // 检查约定环境变量字段是否存在\n            return JSON.parse(TCB_CONTEXT_CNFG);\n        }\n        return {};\n    }\n    catch (e) {\n        /* istanbul ignore next */\n        console.log('parse context error...');\n        /* istanbul ignore next */\n        return {};\n    }\n}\nexports.getTcbContextConfig = getTcbContextConfig;\n/* istanbul ignore next */\nfunction getWxUrl(config) {\n    const protocal = config.isHttp === true ? 'http' : 'https';\n    let wxUrl = protocal + '://tcb-open.tencentcloudapi.com/admin';\n    if (checkIsInScf()) {\n        wxUrl = 'http://tcb-open.tencentyun.com/admin';\n    }\n    return wxUrl;\n}\nexports.getWxUrl = getWxUrl;\nfunction checkIsInternal() {\n    return checkIsInScf() || checkIsInEks() || checkIsInSumeru();\n}\nexports.checkIsInternal = checkIsInternal;\nfunction checkIsInternalAsync() {\n    return checkIsInternal() ? Promise.resolve(true) : checkIsInTencentCloud();\n}\nexports.checkIsInternalAsync = checkIsInternalAsync;\nfunction getCurrRunEnvTag() {\n    if (checkIsInScf()) {\n        return 'scf';\n    }\n    else if (checkIsInEks()) {\n        return 'eks';\n    }\n    else if (checkIsInSumeru()) {\n        return 'sumeru';\n    }\n    else if (checkIsInTencentCloud()) {\n        return 'tencentcloud';\n    }\n    return 'unknown';\n}\nexports.getCurrRunEnvTag = getCurrRunEnvTag;\n/**\n * 是否是场景模块名\n *\n * $: 前缀，表示SaaS场景模块名，非实际环境ID，当前通过特殊环境ID标识\n *\n * @param env\n * @returns\n */\nfunction isPageModuleName(env = '') {\n    return typeof env === 'string' && env.startsWith('$:');\n}\nexports.isPageModuleName = isPageModuleName;\n// 20 + 1 + 16, 限制长度 40\nconst env_rule_reg = /^[a-z0-9_-]{1,40}$/;\nfunction isValidEnvFormat(env = '') {\n    return typeof env === 'string' && env_rule_reg.test(env);\n}\nexports.isValidEnvFormat = isValidEnvFormat;\n", "\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst axios_1 = __importDefault(require(\"axios\"));\nexports.kMetadataBaseUrl = 'http://metadata.tencentyun.com';\nvar kMetadataVersions;\n(function (kMetadataVersions) {\n    kMetadataVersions[\"v20170919\"] = \"2017-09-19\";\n    kMetadataVersions[\"v1.0\"] = \"1.0\";\n    kMetadataVersions[\"latest\"] = \"latest\";\n})(kMetadataVersions = exports.kMetadataVersions || (exports.kMetadataVersions = {}));\nfunction isAppId(appIdStr) {\n    return /^[1-9][0-9]{4,64}$/gim.test(appIdStr);\n}\nexports.isAppId = isAppId;\nasync function lookup(path, options = {}) {\n    const url = `${exports.kMetadataBaseUrl}/${kMetadataVersions.latest}/${path}`;\n    const resp = await axios_1.default.get(url, options);\n    if (resp.status === 200) {\n        return resp.data;\n    }\n    else {\n        throw new Error(`[ERROR] GET ${url} status: ${resp.status}`);\n    }\n}\nexports.lookup = lookup;\nconst metadataCache = {\n    appId: undefined\n};\n/**\n * lookupAppId - 该方法主要用于判断是否在云上环境\n * @returns\n */\nasync function lookupAppId() {\n    if (metadataCache.appId === undefined) {\n        metadataCache.appId = '';\n        try {\n            // 只有首次会请求且要求快速返回，超时时间很短，DNS无法解析将会超时返回\n            // 在云环境中，这个时间通常在 10ms 内，部分耗时长（30+ms）的情况是 DNS 解析耗时长（27+ms）\n            const appId = await lookup('meta-data/app-id', { timeout: 30 });\n            if (isAppId(appId)) {\n                metadataCache.appId = appId;\n            }\n        }\n        catch (e) {\n            // ignore\n        }\n    }\n    return metadataCache.appId || '';\n}\nexports.lookupAppId = lookupAppId;\n", "\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.ERROR = {\n    INVALID_PARAM: {\n        code: 'INVALID_PARAM',\n        message: 'invalid param'\n    },\n    SYS_ERR: {\n        code: 'SYS_ERR',\n        message: 'system error'\n    },\n    STORAGE_REQUEST_FAIL: {\n        code: 'STORAGE_REQUEST_FAIL',\n        message: 'storage request fail'\n    },\n    STORAGE_FILE_NONEXIST: {\n        code: 'STORAGE_FILE_NONEXIST',\n        message: 'storage file not exist'\n    },\n    TCB_CLS_UNOPEN: {\n        code: 'TCB_CLS_UNOPEN',\n        message: '需要先开通日志检索功能'\n    },\n    INVALID_CONTEXT: {\n        code: 'INVALID_CONTEXT',\n        message: '无效的context对象，请使用 云函数入口的context参数'\n    }\n};\n", "\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.SYMBOL_CURRENT_ENV = Symbol.for(\"SYMBOL_CURRENT_ENV\");\n", "\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nvar __importStar = (this && this.__importStar) || function (mod) {\n    if (mod && mod.__esModule) return mod;\n    var result = {};\n    if (mod != null) for (var k in mod) if (Object.hasOwnProperty.call(mod, k)) result[k] = mod[k];\n    result[\"default\"] = mod;\n    return result;\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst http_1 = __importDefault(require(\"http\"));\nconst request_1 = __importDefault(require(\"request\"));\nconst retry_1 = require(\"./retry\");\nconst request_timings_measurer_1 = require(\"./request-timings-measurer\");\nconst agentkeepalive_1 = __importStar(require(\"agentkeepalive\"));\nconst SAFE_RETRY_CODE_SET = new Set([\n    'ENOTFOUND',\n    'ENETDOWN',\n    'EHOSTDOWN',\n    'ENETUNREACH',\n    'EHOSTUNREACH',\n    'ECONNREFUSED'\n]);\nconst RETRY_CODE_SET = new Set(['ECONNRESET', 'ESOCKETTIMEDOUT']);\nconst RETRY_STATUS_CODE_SET = new Set([]);\n/* istanbul ignore next */\nfunction shouldRetry(e, result, operation) {\n    // 重试的错误码\n    if (e && SAFE_RETRY_CODE_SET.has(e.code)) {\n        return {\n            retryAble: true,\n            message: e.message\n        };\n    }\n    // 连接超时\n    if (e && e.code === 'ETIMEDOUT' && e.connect === true) {\n        return {\n            retryAble: true,\n            message: e.message\n        };\n    }\n    // 重试的状态码\n    if (result && RETRY_STATUS_CODE_SET.has(result.statusCode)) {\n        return {\n            retryAble: true,\n            message: `${result.request.method} ${result.request.href} ${result.statusCode} ${http_1.default.STATUS_CODES[result.statusCode]}`\n        };\n    }\n    return {\n        retryAble: false,\n        message: ''\n    };\n}\n/* istanbul ignore next */\nfunction requestWithTimingsMeasure(opts, extraOptions) {\n    return new Promise((resolve, reject) => {\n        const timingsMeasurerOptions = extraOptions.timingsMeasurerOptions || {};\n        const { waitingTime = 1000, interval = 200, enable = !!extraOptions.debug } = timingsMeasurerOptions;\n        const timingsMeasurer = request_timings_measurer_1.RequestTimgingsMeasurer.new({\n            waitingTime,\n            interval,\n            enable\n        });\n        timingsMeasurer.on('progress', (timings, reason = '') => {\n            const timingsLine = `s:${timings.socket || '-'}|l:${timings.lookup ||\n                '-'}|c:${timings.connect || '-'}|r:${timings.ready || '-'}|w:${timings.waiting ||\n                '-'}|d:${timings.download || '-'}|e:${timings.end || '-'}|E:${timings.error || '-'}`;\n            console.warn(`[RequestTimgings][${extraOptions.op || ''}] spent ${Date.now() -\n                timings.start}ms(${timingsLine}) [${extraOptions.seqId}][${extraOptions.attempts || 1}][${reason}]`);\n        });\n        if (opts.keepalive) {\n            ;\n            opts.agentClass = opts.url.startsWith('https')\n                ? agentkeepalive_1.HttpsAgent\n                : agentkeepalive_1.default;\n            opts.agentOptions = {\n                // keepAlive: true,\n                keepAliveMsecs: 3000,\n                maxSockets: 100,\n                maxFreeSockets: 10,\n                freeSocketTimeout: 20000,\n                timeout: 20000,\n                socketActiveTTL: null\n            };\n        }\n        ;\n        (function r(times) {\n            const clientRequest = request_1.default(opts, function (err, response, body) {\n                const reusedSocket = !!(clientRequest && clientRequest.req && clientRequest.req.reusedSocket);\n                if (err && extraOptions.debug) {\n                    console.warn(`[RequestTimgings][keepalive:${opts.keepalive}][reusedSocket:${reusedSocket}][times:${times}][code:${err.code}][message:${err.message}]${opts.url}`);\n                }\n                if (err && err.code === 'ECONNRESET' && reusedSocket && times >= 0 && opts.keepalive) {\n                    return r(--times);\n                }\n                return err ? reject(err) : resolve(response);\n            });\n            if ((request_1.default.Request && clientRequest instanceof request_1.default.Request) ||\n                clientRequest instanceof http_1.default.ClientRequest) {\n                timingsMeasurer.measure(clientRequest);\n            }\n        }(1));\n    });\n}\nexports.requestWithTimingsMeasure = requestWithTimingsMeasure;\nfunction extraRequest(opts, extraOptions) {\n    if (extraOptions && extraOptions.retryOptions) {\n        return retry_1.withRetry(attempts => {\n            return requestWithTimingsMeasure(opts, Object.assign({}, extraOptions, { attempts }));\n        }, Object.assign({ shouldRetry }, extraOptions.retryOptions));\n    }\n    else {\n        return requestWithTimingsMeasure(opts, Object.assign({}, extraOptions, { attempts: 1 }));\n    }\n}\nexports.extraRequest = extraRequest;\n", "\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst retry_1 = __importDefault(require(\"retry\"));\n// import { RetryOperation } from 'retry/lib/retry_operation'\nconst RetryOperation = require('retry/lib/retry_operation');\n/* istanbul ignore next */\nfunction defaultShouldRetry(e, result) {\n    return { retryAble: false, message: '' };\n}\n/**\n * withRetry 重试封装函数\n * @param fn\n * @param retryOptions\n */\n/* istanbul ignore next */\nfunction withRetry(fn, retryOptions) {\n    // 默认不重试，0 表达未开启的含义，所以直接返回 promise\n    if (!retryOptions || retryOptions.retries === 0) {\n        return fn();\n    }\n    // 默认重试策略采取指数退避策略，超时时间计算公式及参数可查文档\n    // https://github.com/tim-kos/node-retry/\n    // 自定重试时间：\n    // timeouts: [1000, 2000, 4000, 8000]\n    const timeouts = retryOptions.timeouts\n        ? [...retryOptions.timeouts]\n        : retry_1.default.timeouts(retryOptions);\n    const operation = new RetryOperation(timeouts, {\n        forever: retryOptions.forever,\n        unref: retryOptions.unref,\n        maxRetryTime: retryOptions.maxRetryTime // 重试总的时间，单位毫秒，默认：Infinity\n    });\n    const shouldRetry = retryOptions.shouldRetry || defaultShouldRetry;\n    return new Promise((resolve, reject) => {\n        const isReadyToRetry = (e, resp, operation) => {\n            // 外层有效识别需要或者能够进行重试\n            // shouldRetry 中可调用 operation.stop 停掉重试，operation.retry 返回 false\n            const { retryAble, message } = shouldRetry(e, resp, operation);\n            const info = {};\n            info.nth = operation.attempts();\n            info.at = new Date();\n            info.message = message;\n            // 双重条件判断是否重试，外层判断满足条件与否，还需判断是否满足再次重试条件\n            const readyToRetry = retryAble && operation.retry(Object.assign({}, info));\n            if (!readyToRetry) {\n                // 如果不准备进行重试，并且尝试不止一次\n                // 最后一个错误记录重试信息\n                const ref = e || resp;\n                if (ref && operation.attempts() > 1) {\n                    ref.attempt = {};\n                    ref.attempt.timeouts = operation._originalTimeouts;\n                    ref.attempt.attempts = operation.attempts();\n                    ref.attempt.errors = operation.errors();\n                    // 如果最后一次因为 !retryAble 而没有进行重试\n                    // ref.attempt.errors 中将缺少最后的这个错误\n                    // ref.attempt.errors 中包含最后一次错误信息\n                    if (!retryAble) {\n                        ref.attempt.errors.push(info);\n                    }\n                }\n            }\n            return readyToRetry;\n        };\n        operation.attempt(async () => {\n            try {\n                const result = await fn(operation.attempts());\n                if (!isReadyToRetry(null, result, operation)) {\n                    resolve(result);\n                }\n            }\n            catch (e) {\n                try {\n                    if (!isReadyToRetry(e, null, operation)) {\n                        reject(e);\n                    }\n                }\n                catch (e) {\n                    reject(e);\n                }\n            }\n        }, retryOptions.timeoutOps);\n    });\n}\nexports.withRetry = withRetry;\n", "\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst EventEmitter = require('events').EventEmitter;\nclass RequestTimgingsMeasurer extends EventEmitter {\n    constructor(options) {\n        super();\n        this.e = null;\n        this.timings = {\n        // start: 0,\n        // lookup: -1,\n        // connect: -1,\n        // ready: -1,\n        // waiting: -1,\n        // download: -1,\n        // end: -1\n        };\n        this.e = null;\n        this.enable = options.enable === true;\n        this.timerStarted = false;\n        this.intervalId = null;\n        this.timeoutId = null;\n        this.waitingTime = options.waitingTime || 1000;\n        this.interval = options.interval || 200;\n    }\n    static new(options) {\n        return new RequestTimgingsMeasurer(options);\n    }\n    /* istanbul ignore next */\n    measure(clientRequest) {\n        if (!this.enable) {\n            return;\n        }\n        this.startTimer();\n        const timings = this.timings;\n        timings.start = Date.now();\n        clientRequest\n            .once('response', message => {\n            timings.response = Date.now();\n            timings.waiting = Date.now() - timings.start;\n            message.once('end', () => {\n                timings.socket = timings.socket || 0;\n                // timings.lookup = timings.lookup || timings.socket\n                // timings.connect = timings.connect || timings.lookup\n                timings.download = Date.now() - timings.response;\n                timings.end = Date.now() - timings.start;\n                this.stopTimer('end');\n            });\n        })\n            .once('socket', socket => {\n            timings.socket = Date.now() - timings.start;\n            const onlookup = () => {\n                this.timings.lookup = Date.now() - this.timings.start;\n            };\n            const onconnect = () => {\n                this.timings.connect = Date.now() - this.timings.start;\n            };\n            const onready = () => {\n                this.timings.ready = Date.now() - this.timings.start;\n            };\n            if (socket.connecting) {\n                socket.once('lookup', onlookup);\n                socket.once('connect', onconnect);\n                socket.once('ready', onready);\n                socket.once('error', e => {\n                    socket.off('lookup', onlookup);\n                    socket.off('connect', onconnect);\n                    socket.off('ready', onready);\n                    this.e = e;\n                    this.timings.error = Date.now() - this.timings.start;\n                    this.stopTimer(`ee:${e.message}`);\n                });\n            }\n            else {\n                this.timings.lookup = -1;\n                this.timings.connect = -1;\n                this.timings.ready = -1;\n            }\n            // socket.once('data', () => {})\n            // socket.once('drain', () => {})\n            // socket.once('end', () => {\n            //   this.stopTimer('end')\n            // })\n            // socket.once('timeout', () => {\n            //   this.timings.timeout = Date.now() - this.timings.start\n            // })\n        })\n            .on('error', (e) => {\n            this.stopTimer(`ee:${e.message}`);\n        });\n    }\n    /* istanbul ignore next */\n    startTimer() {\n        if (!this.enable) {\n            return;\n        }\n        if (this.timerStarted) {\n            return;\n        }\n        this.timerStarted = true;\n        this.intervalId = null;\n        this.timeoutId = setTimeout(() => {\n            this.process('inprogress');\n            this.intervalId = setInterval(() => {\n                this.process('inprogress');\n            }, this.interval);\n        }, this.waitingTime);\n    }\n    /* istanbul ignore next */\n    stopTimer(reason) {\n        // if (!this.enable) {\n        //   return\n        // }\n        // if (!this.timerStarted) {\n        //   return\n        // }\n        this.timerStarted = false;\n        clearTimeout(this.timeoutId);\n        clearInterval(this.intervalId);\n        this.process(reason);\n    }\n    /* istanbul ignore next */\n    process(reason) {\n        this.emit('progress', Object.assign({}, this.timings), reason);\n    }\n}\nexports.RequestTimgingsMeasurer = RequestTimgingsMeasurer;\n", "\nObject.defineProperty(exports, \"__esModule\", { value: true });\n/**\n * 处理wxopenapi返回\n *\n * @param err\n * @param response\n * @param body\n */\nexports.handleWxOpenApiData = (res, err, response, body) => {\n    // wx.openApi 调用时，需用content-type区分buffer or JSON\n    const { headers } = response;\n    let transformRes = res;\n    if (headers['content-type'] === 'application/json; charset=utf-8') {\n        transformRes = JSON.parse(transformRes.toString()); // JSON错误时buffer转JSON\n    }\n    return transformRes;\n};\n", "\nvar __importStar = (this && this.__importStar) || function (mod) {\n    if (mod && mod.__esModule) return mod;\n    var result = {};\n    if (mod != null) for (var k in mod) if (Object.hasOwnProperty.call(mod, k)) result[k] = mod[k];\n    result[\"default\"] = mod;\n    return result;\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\n// 由定时触发器触发时（TRIGGER_SRC=timer）：优先使用 WX_TRIGGER_API_TOKEN_V0，不存在的话，为了兼容兼容旧的开发者工具，也是使用 WX_API_TOKEN\n// 非定时触发器触发时（TRIGGER_SRC!=timer）: 使用 WX_API_TOKEN\nconst cloudbase_1 = require(\"../cloudbase\");\nconst utils = __importStar(require(\"./utils\"));\nconst fs = __importStar(require(\"fs\"));\nexports.CLOUDBASE_ACCESS_TOKEN_PATH = '/.tencentcloudbase/wx/cloudbase_access_token';\nfunction getWxCloudToken() {\n    const { TRIGGER_SRC, WX_TRIGGER_API_TOKEN_V0, WX_API_TOKEN, WX_CLOUDBASE_ACCESSTOKEN = '' } = cloudbase_1.CloudBase.getCloudbaseContext();\n    const wxCloudToken = {};\n    if (TRIGGER_SRC === 'timer') {\n        wxCloudToken.wxCloudApiToken = WX_TRIGGER_API_TOKEN_V0 || WX_API_TOKEN || '';\n    }\n    else {\n        wxCloudToken.wxCloudApiToken = WX_API_TOKEN || '';\n    }\n    // 只在不存在 wxCloudApiToken 时，才尝试读取 wxCloudbaseAccesstoken\n    if (!wxCloudToken.wxCloudApiToken) {\n        wxCloudToken.wxCloudbaseAccesstoken = WX_CLOUDBASE_ACCESSTOKEN || loadWxCloudbaseAccesstoken();\n    }\n    return wxCloudToken;\n}\nexports.getWxCloudToken = getWxCloudToken;\nconst maxCacheAge = 10 * 60 * 1000;\nconst cloudbaseAccessTokenInfo = { token: '', timestamp: 0 };\nfunction loadWxCloudbaseAccesstoken() {\n    if (cloudbaseAccessTokenInfo.token && Date.now() - cloudbaseAccessTokenInfo.timestamp < maxCacheAge) {\n        return cloudbaseAccessTokenInfo.token;\n    }\n    try {\n        if (utils.checkIsInEks() && fs.existsSync(exports.CLOUDBASE_ACCESS_TOKEN_PATH)) {\n            cloudbaseAccessTokenInfo.token = fs.readFileSync(exports.CLOUDBASE_ACCESS_TOKEN_PATH).toString();\n            cloudbaseAccessTokenInfo.timestamp = Date.now();\n            return cloudbaseAccessTokenInfo.token;\n        }\n    }\n    catch (e) {\n        console.warn('[ERROR]: loadWxCloudbaseAccesstoken error: ', e.message);\n    }\n    return '';\n}\nexports.loadWxCloudbaseAccesstoken = loadWxCloudbaseAccesstoken;\n", "\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst request_1 = __importDefault(require(\"request\"));\nconst metadata_1 = require(\"./metadata\");\n/**\n * 容器托管内的密钥管理器\n */\nclass SecretManager {\n    constructor() {\n        this.TMP_SECRET_URL = `${metadata_1.kMetadataBaseUrl}/meta-data/cam/security-credentials/TCB_QcsRole`;\n        this.tmpSecret = null;\n    }\n    /* istanbul ignore next */\n    async getTmpSecret() {\n        if (this.tmpSecret) {\n            const now = new Date().getTime();\n            const expire = this.tmpSecret.expire * 1000;\n            const oneHour = 3600 * 1000;\n            if (now < expire - oneHour) {\n                // 密钥没过期\n                return this.tmpSecret;\n            }\n            else {\n                // 密钥过期\n                return this.fetchTmpSecret();\n            }\n        }\n        else {\n            return this.fetchTmpSecret();\n        }\n    }\n    /* istanbul ignore next */\n    async fetchTmpSecret() {\n        const body = await this.get(this.TMP_SECRET_URL);\n        const payload = JSON.parse(body);\n        this.tmpSecret = {\n            id: payload.TmpSecretId,\n            key: payload.TmpSecretKey,\n            expire: payload.ExpiredTime,\n            token: payload.Token\n        };\n        return this.tmpSecret;\n    }\n    /* istanbul ignore next */\n    get(url) {\n        return new Promise((resolve, reject) => {\n            request_1.default.get(url, (err, res, body) => {\n                if (err) {\n                    reject(err);\n                }\n                else {\n                    resolve(body);\n                }\n            });\n        });\n    }\n}\nexports.default = SecretManager;\n", "module.exports = {\n    \"name\": \"@cloudbase/node-sdk\",\n    \"version\": \"2.10.0\",\n    \"description\": \"tencent cloud base server sdk for node.js\",\n    \"main\": \"lib/index.js\",\n    \"scripts\": {\n        \"eslint\": \"eslint \\\"./**/*.ts\\\"\",\n        \"eslint-fix\": \"eslint --fix \\\"./**/*.ts\\\"\",\n        \"build\": \"rm -rf lib/* && npm run tsc\",\n        \"tsc\": \"tsc -p tsconfig.json\",\n        \"tsc:w\": \"tsc -p tsconfig.json -w\",\n        \"tstest\": \"mocha --timeout 5000 --require espower-typescript/guess test/**/*.test.ts\",\n        \"test\": \"jest --detectOpenHandles --verbose --coverage --runInBand\",\n        \"coverage\": \"jest --detectOpenHandles --coverage\",\n        \"coveralls\": \"cat ./coverage/lcov.info | coveralls\"\n    },\n    \"repository\": {\n        \"type\": \"git\",\n        \"url\": \"https://github.com/TencentCloudBase/node-sdk\"\n    },\n    \"bugs\": {\n        \"url\": \"https://github.com/TencentCloudBase/node-sdk/issues\"\n    },\n    \"homepage\": \"https://github.com/TencentCloudBase/node-sdk#readme\",\n    \"keywords\": [\n        \"node sdk\"\n    ],\n    \"author\": \"lukejyhuang\",\n    \"license\": \"MIT\",\n    \"typings\": \"types/index.d.ts\",\n    \"dependencies\": {\n        \"@cloudbase/database\": \"1.4.1\",\n        \"@cloudbase/signature-nodejs\": \"1.0.0-beta.0\",\n        \"agentkeepalive\": \"^4.3.0\",\n        \"axios\": \"^0.21.1\",\n        \"jsonwebtoken\": \"^8.5.1\",\n        \"request\": \"^2.87.0\",\n        \"retry\": \"^0.13.1\",\n        \"xml2js\": \"^0.5.0\"\n    },\n    \"devDependencies\": {\n        \"@types/jest\": \"^23.1.4\",\n        \"@types/mocha\": \"^5.2.4\",\n        \"@types/node\": \"^10.12.12\",\n        \"@types/retry\": \"^0.12.2\",\n        \"@typescript-eslint/eslint-plugin\": \"^2.16.0\",\n        \"@typescript-eslint/parser\": \"^2.16.0\",\n        \"babel-eslint\": \"^10.0.3\",\n        \"coveralls\": \"^3.0.9\",\n        \"dumper.js\": \"^1.3.0\",\n        \"eslint\": \"^7.1.0\",\n        \"eslint-config-alloy\": \"^3.5.0\",\n        \"eslint-plugin-prettier\": \"^3.1.2\",\n        \"husky\": \"^3.1.0\",\n        \"jest\": \"^23.3.0\",\n        \"lint-staged\": \"^9.2.5\",\n        \"mocha\": \"^5.2.0\",\n        \"power-assert\": \"^1.5.0\",\n        \"prettier\": \"^1.19.1\",\n        \"ts-jest\": \"^23.10.4\",\n        \"ts-node\": \"^10.9.1\",\n        \"tslib\": \"^1.7.1\",\n        \"typescript\": \"3.5.3\"\n    },\n    \"engines\": {\n        \"node\": \">=8.6.0\"\n    },\n    \"husky\": {\n        \"hooks\": {\n            \"pre-commit\": \"npm run build && git add . && lint-staged\"\n        }\n    },\n    \"lint-staged\": {\n        \"*.ts\": [\n            \"eslint --fix\",\n            \"git add\"\n        ]\n    }\n}\n", "\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst jsonwebtoken_1 = __importDefault(require(\"jsonwebtoken\"));\nconst utils_1 = require(\"../utils/utils\");\nconst code_1 = require(\"../const/code\");\nconst cloudbase_1 = require(\"../cloudbase\");\nconst symbol_1 = require(\"../const/symbol\");\nconst httpRequest_1 = __importDefault(require(\"../utils/httpRequest\"));\nconst checkCustomUserIdRegex = /^[a-zA-Z0-9_\\-#@~=*(){}[\\]:.,<>+]{4,32}$/;\nfunction validateUid(uid) {\n    if (typeof uid !== 'string') {\n        // console.log('debug:', { ...ERROR.INVALID_PARAM, message: 'uid must be a string' })\n        throw utils_1.E(Object.assign({}, code_1.ERROR.INVALID_PARAM, { message: 'uid must be a string' }));\n    }\n    if (!checkCustomUserIdRegex.test(uid)) {\n        throw utils_1.E(Object.assign({}, code_1.ERROR.INVALID_PARAM, { message: `Invalid uid: \"${uid}\"` }));\n    }\n}\nfunction auth(cloudbase) {\n    return {\n        getUserInfo() {\n            const { WX_OPENID, WX_APPID, TCB_UUID, TCB_CUSTOM_USER_ID, TCB_ISANONYMOUS_USER } = cloudbase_1.CloudBase.getCloudbaseContext();\n            return {\n                openId: WX_OPENID || '',\n                appId: WX_APPID || '',\n                uid: TCB_UUID || '',\n                customUserId: TCB_CUSTOM_USER_ID || '',\n                isAnonymous: TCB_ISANONYMOUS_USER === 'true' ? true : false\n            };\n        },\n        getEndUserInfo(uid, opts) {\n            const { WX_OPENID, WX_APPID, TCB_UUID, TCB_CUSTOM_USER_ID, TCB_ISANONYMOUS_USER } = cloudbase_1.CloudBase.getCloudbaseContext();\n            const defaultUserInfo = {\n                openId: WX_OPENID || '',\n                appId: WX_APPID || '',\n                uid: TCB_UUID || '',\n                customUserId: TCB_CUSTOM_USER_ID || '',\n                isAnonymous: TCB_ISANONYMOUS_USER === 'true' ? true : false\n            };\n            if (uid === undefined) {\n                return {\n                    userInfo: defaultUserInfo\n                };\n            }\n            validateUid(uid);\n            const params = {\n                action: 'auth.getUserInfoForAdmin',\n                uuid: uid\n            };\n            return httpRequest_1.default({\n                config: cloudbase.config,\n                params,\n                method: 'post',\n                opts,\n                headers: {\n                    'content-type': 'application/json'\n                }\n            }).then(res => {\n                if (res.code) {\n                    return res;\n                }\n                return {\n                    userInfo: Object.assign({}, defaultUserInfo, res.data),\n                    requestId: res.requestId\n                };\n            });\n        },\n        queryUserInfo(query, opts) {\n            const { uid, platform, platformId } = query;\n            const params = {\n                action: 'auth.getUserInfoForAdmin',\n                uuid: uid,\n                platform,\n                platformId\n            };\n            return httpRequest_1.default({\n                config: cloudbase.config,\n                params,\n                method: 'post',\n                opts,\n                headers: {\n                    'content-type': 'application/json'\n                }\n            }).then(res => {\n                if (res.code) {\n                    return res;\n                }\n                return {\n                    userInfo: Object.assign({}, res.data),\n                    requestId: res.requestId\n                };\n            });\n        },\n        async getAuthContext(context) {\n            const { TCB_UUID, LOGINTYPE, QQ_OPENID, QQ_APPID } = cloudbase_1.CloudBase.getCloudbaseContext(context);\n            const res = {\n                uid: TCB_UUID,\n                loginType: LOGINTYPE\n            };\n            if (LOGINTYPE === 'QQ-MINI') {\n                res.appId = QQ_APPID;\n                res.openId = QQ_OPENID;\n            }\n            return res;\n        },\n        getClientIP() {\n            const { TCB_SOURCE_IP } = cloudbase_1.CloudBase.getCloudbaseContext();\n            return TCB_SOURCE_IP || '';\n        },\n        createTicket: (uid, options = {}) => {\n            validateUid(uid);\n            const timestamp = new Date().getTime();\n            const { TCB_ENV, SCF_NAMESPACE } = cloudbase_1.CloudBase.getCloudbaseContext();\n            const { credentials } = cloudbase.config;\n            const { env_id } = credentials;\n            let { envName } = cloudbase.config;\n            if (!envName) {\n                throw utils_1.E(Object.assign({}, code_1.ERROR.INVALID_PARAM, { message: 'no env in config' }));\n            }\n            // 检查credentials 是否包含env\n            if (!env_id) {\n                throw utils_1.E(Object.assign({}, code_1.ERROR.INVALID_PARAM, { message: '当前私钥未包含env_id 信息， 请前往腾讯云云开发控制台，获取自定义登录最新私钥' }));\n            }\n            // 使用symbol时替换为环境变量内的env\n            if (envName === symbol_1.SYMBOL_CURRENT_ENV) {\n                envName = TCB_ENV || SCF_NAMESPACE;\n            }\n            // 检查 credentials env 和 init 指定env 是否一致\n            if (env_id && env_id !== envName) {\n                throw utils_1.E(Object.assign({}, code_1.ERROR.INVALID_PARAM, { message: '当前私钥所属环境与 init 指定环境不一致！' }));\n            }\n            const { refresh = 3600 * 1000, expire = timestamp + 7 * 24 * 60 * 60 * 1000 } = options;\n            const token = jsonwebtoken_1.default.sign({\n                alg: 'RS256',\n                env: envName,\n                iat: timestamp,\n                exp: timestamp + 10 * 60 * 1000,\n                uid,\n                refresh,\n                expire\n            }, credentials.private_key, { algorithm: 'RS256' });\n            return credentials.private_key_id + '/@@/' + token;\n        }\n    };\n}\nexports.auth = auth;\n", "\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst httpRequest_1 = __importDefault(require(\"../utils/httpRequest\"));\nconst utils_1 = require(\"../utils/utils\");\nconst code_1 = require(\"../const/code\");\nfunction validateCrossAccount(config, opts = {}) {\n    let getCrossAccountInfo = opts.getCrossAccountInfo || config.getCrossAccountInfo;\n    if (getCrossAccountInfo) {\n        throw utils_1.E(Object.assign({}, code_1.ERROR.INVALID_PARAM, { message: 'invalid config: getCrossAccountInfo' }));\n    }\n}\nasync function callWxOpenApi(cloudbase, { apiName, apiOptions, cgiName, requestData }, opts) {\n    let transformRequestData;\n    try {\n        transformRequestData = requestData ? JSON.stringify(requestData) : '';\n    }\n    catch (e) {\n        throw utils_1.E(Object.assign({}, e, { code: code_1.ERROR.INVALID_PARAM.code, message: '对象出现了循环引用' }));\n    }\n    validateCrossAccount(cloudbase.config, opts);\n    const params = {\n        action: 'wx.api',\n        apiName,\n        apiOptions,\n        cgiName,\n        requestData: transformRequestData\n    };\n    return httpRequest_1.default({\n        config: cloudbase.config,\n        params,\n        method: 'post',\n        opts,\n        headers: {\n            'content-type': 'application/json'\n        }\n    }).then(res => {\n        if (res.code) {\n            return res;\n        }\n        let result;\n        try {\n            result = JSON.parse(res.data.responseData);\n        }\n        catch (e) {\n            result = res.data.responseData;\n        }\n        return {\n            result,\n            requestId: res.requestId\n        };\n        // }\n    });\n}\nexports.callWxOpenApi = callWxOpenApi;\n/**\n * 调用wxopenAPi\n * @param {String} apiName  接口名\n * @param {Buffer} requestData\n * @return {Promise} 正常内容为buffer，报错为json {code:'', message:'', resquestId:''}\n */\nasync function callCompatibleWxOpenApi(cloudbase, { apiName, apiOptions, cgiName, requestData }, opts) {\n    validateCrossAccount(cloudbase.config, opts);\n    const params = {\n        action: 'wx.openApi',\n        apiName,\n        apiOptions,\n        cgiName,\n        requestData\n    };\n    return httpRequest_1.default({\n        config: cloudbase.config,\n        method: 'post',\n        headers: { 'content-type': 'multipart/form-data' },\n        params,\n        isFormData: true,\n        opts\n    }).then(res => res);\n}\nexports.callCompatibleWxOpenApi = callCompatibleWxOpenApi;\n/**\n * wx.wxPayApi 微信支付用\n * @param {String} apiName  接口名\n * @param {Buffer} requestData\n * @return {Promise} 正常内容为buffer，报错为json {code:'', message:'', resquestId:''}\n */\nasync function callWxPayApi(cloudbase, { apiName, apiOptions, cgiName, requestData }, opts) {\n    validateCrossAccount(cloudbase.config, opts);\n    const params = {\n        action: 'wx.wxPayApi',\n        apiName,\n        apiOptions,\n        cgiName,\n        requestData\n    };\n    return httpRequest_1.default({\n        config: cloudbase.config,\n        method: 'post',\n        headers: { 'content-type': 'multipart/form-data' },\n        params,\n        isFormData: true,\n        opts\n    });\n}\nexports.callWxPayApi = callWxPayApi;\n/**\n * wx.wxCallContainerApi\n * @param {String} apiName  接口名\n * @param {Buffer} requestData\n * @return {Promise} 正常内容为buffer，报错为json {code:'', message:'', resquestId:''}\n */\nasync function wxCallContainerApi(cloudbase, { apiName, apiOptions, cgiName, requestData }, opts) {\n    validateCrossAccount(cloudbase.config, opts);\n    const params = {\n        action: 'wx.wxCallContainerApi',\n        apiName,\n        apiOptions,\n        cgiName,\n        requestData\n    };\n    return httpRequest_1.default({\n        config: cloudbase.config,\n        method: 'post',\n        headers: { 'content-type': 'multipart/form-data' },\n        params,\n        isFormData: true,\n        opts\n    });\n}\nexports.wxCallContainerApi = wxCallContainerApi;\n", "\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst request_1 = __importDefault(require(\"request\"));\nconst fs_1 = __importDefault(require(\"fs\"));\nconst httpRequest_1 = __importDefault(require(\"../utils/httpRequest\"));\nconst xml2js_1 = require(\"xml2js\");\nconst utils_1 = require(\"../utils/utils\");\nconst code_1 = require(\"../const/code\");\nconst cloudbase_1 = require(\"../cloudbase\");\nasync function parseXML(str) {\n    return new Promise((resolve, reject) => {\n        xml2js_1.parseString(str, (err, result) => {\n            if (err) {\n                reject(err);\n            }\n            else {\n                resolve(result);\n            }\n        });\n    });\n}\nexports.parseXML = parseXML;\n/*\n * 上传文件\n * @param {string} cloudPath 上传后的文件路径\n * @param {fs.ReadStream} fileContent  上传文件的二进制流\n */\nasync function uploadFile(cloudbase, { cloudPath, fileContent }, opts) {\n    const { requestId, data: { url, token, authorization, fileId, cosFileId } } = await getUploadMetadata(cloudbase, { cloudPath }, opts);\n    const formData = {\n        Signature: authorization,\n        'x-cos-security-token': token,\n        'x-cos-meta-fileid': cosFileId,\n        key: cloudPath,\n        file: fileContent\n    };\n    let body = await new Promise((resolve, reject) => {\n        request_1.default({\n            method: 'post',\n            url,\n            formData: formData,\n            proxy: cloudbase.config.proxy\n        }, function (err, res, body) {\n            if (err) {\n                reject(err);\n            }\n            else {\n                resolve(body);\n            }\n        });\n    });\n    // 成功返回空字符串，失败返回如下格式 XML：\n    // <?xml version='1.0' encoding='utf-8' ?>\n    // <Error>\n    //     <Code>InvalidAccessKeyId</Code>\n    //     <Message>The Access Key Id you provided does not exist in our records</Message>\n    //     <Resource>/path/to/file/key.xyz</Resource>\n    //     <RequestId>NjQzZTMyYzBfODkxNGJlMDlfZjU4NF9hMjk4YTUy</RequestId>\n    //     <TraceId>OGVmYzZiMmQzYjA2OWNhODk0NTRkMTBiOWVmMDAxODc0OWRkZjk0ZDM1NmI1M2E2MTRlY2MzZDhmNmI5MWI1OTQyYWVlY2QwZTk2MDVmZDQ3MmI2Y2I4ZmI5ZmM4ODFjYmRkMmZmNzk1YjUxODZhZmZlNmNhYWUyZTQzYjdiZWY=</TraceId>\n    // </Error>\n    body = await parseXML(body);\n    if (body && body.Error) {\n        const { Code: [code], Message: [message], RequestId: [cosRequestId], TraceId: [cosTraceId] } = body.Error;\n        if (code === 'SignatureDoesNotMatch') {\n            return utils_1.processReturn(cloudbase.config.throwOnCode, Object.assign({}, code_1.ERROR.SYS_ERR, { message: `[${code}]: ${message}`, requestId: `${requestId}|${cosRequestId}|${cosTraceId}` }));\n        }\n        return utils_1.processReturn(cloudbase.config.throwOnCode, Object.assign({}, code_1.ERROR.STORAGE_REQUEST_FAIL, { message: `[${code}]: ${message}`, requestId: `${requestId}|${cosRequestId}|${cosTraceId}` }));\n    }\n    return {\n        fileID: fileId\n    };\n}\nexports.uploadFile = uploadFile;\n/**\n * 删除文件\n * @param {Array.<string>} fileList 文件id数组\n */\nasync function deleteFile(cloudbase, { fileList }, opts) {\n    if (!fileList || !Array.isArray(fileList)) {\n        return utils_1.processReturn(cloudbase.config.throwOnCode, Object.assign({}, code_1.ERROR.INVALID_PARAM, { message: 'fileList必须是非空的数组' }));\n    }\n    for (let file of fileList) {\n        if (!file || typeof file !== 'string') {\n            return utils_1.processReturn(cloudbase.config.throwOnCode, Object.assign({}, code_1.ERROR.INVALID_PARAM, { message: 'fileList的元素必须是非空的字符串' }));\n        }\n    }\n    let params = {\n        action: 'storage.batchDeleteFile',\n        fileid_list: fileList\n    };\n    return httpRequest_1.default({\n        config: cloudbase.config,\n        params,\n        method: 'post',\n        opts,\n        headers: {\n            'content-type': 'application/json'\n        }\n    }).then(res => {\n        if (res.code) {\n            return res;\n        }\n        //     throw E({ ...res })\n        // } else {\n        return {\n            fileList: res.data.delete_list,\n            requestId: res.requestId\n        };\n        // }\n    });\n}\nexports.deleteFile = deleteFile;\n/**\n * 获取文件下载链接\n * @param {Array.<Object>} fileList\n */\nasync function getTempFileURL(cloudbase, { fileList }, opts) {\n    if (!fileList || !Array.isArray(fileList)) {\n        return utils_1.processReturn(cloudbase.config.throwOnCode, Object.assign({}, code_1.ERROR.INVALID_PARAM, { message: 'fileList必须是非空的数组' }));\n    }\n    let file_list = [];\n    for (let file of fileList) {\n        if (typeof file === 'object') {\n            if (!file.hasOwnProperty('fileID') || !file.hasOwnProperty('maxAge')) {\n                return utils_1.processReturn(cloudbase.config.throwOnCode, Object.assign({}, code_1.ERROR.INVALID_PARAM, { message: 'fileList的元素如果是对象，必须是包含fileID和maxAge的对象' }));\n            }\n            file_list.push({\n                fileid: file.fileID,\n                max_age: file.maxAge\n            });\n        }\n        else if (typeof file === 'string') {\n            file_list.push({\n                fileid: file\n            });\n        }\n        else {\n            return utils_1.processReturn(cloudbase.config.throwOnCode, Object.assign({}, code_1.ERROR.INVALID_PARAM, { message: 'fileList的元素如果不是对象，则必须是字符串' }));\n        }\n    }\n    let params = {\n        action: 'storage.batchGetDownloadUrl',\n        file_list\n    };\n    // console.log(params);\n    return httpRequest_1.default({\n        config: cloudbase.config,\n        params,\n        method: 'post',\n        opts,\n        headers: {\n            'content-type': 'application/json'\n        }\n    }).then(res => {\n        if (res.code) {\n            return res;\n        }\n        // if (res.code) {\n        //     throw E({ ...res })\n        // } else {\n        return {\n            fileList: res.data.download_list,\n            requestId: res.requestId\n        };\n        // }\n    });\n}\nexports.getTempFileURL = getTempFileURL;\nasync function downloadFile(cloudbase, params, opts) {\n    let tmpUrl;\n    const { fileID, tempFilePath } = params;\n    const tmpUrlRes = await getTempFileURL(cloudbase, {\n        fileList: [\n            {\n                fileID,\n                maxAge: 600\n            }\n        ]\n    }, opts);\n    // console.log(tmpUrlRes);\n    const res = tmpUrlRes.fileList[0];\n    if (res.code !== 'SUCCESS') {\n        return utils_1.processReturn(cloudbase.config.throwOnCode, Object.assign({}, res));\n    }\n    tmpUrl = res.tempFileURL;\n    tmpUrl = encodeURI(tmpUrl);\n    let req = request_1.default({\n        url: tmpUrl,\n        encoding: null,\n        proxy: cloudbase.config.proxy\n    });\n    return new Promise((resolve, reject) => {\n        let fileContent = Buffer.alloc(0);\n        req.on('response', function (response) {\n            /* istanbul ignore else  */\n            if (response && Number(response.statusCode) === 200) {\n                if (tempFilePath) {\n                    response.pipe(fs_1.default.createWriteStream(tempFilePath));\n                }\n                else {\n                    response.on('data', data => {\n                        fileContent = Buffer.concat([fileContent, data]);\n                    });\n                }\n                response.on('end', () => {\n                    resolve({\n                        fileContent: tempFilePath ? undefined : fileContent,\n                        message: '文件下载完成'\n                    });\n                });\n            }\n            else {\n                reject(response);\n            }\n        });\n    });\n}\nexports.downloadFile = downloadFile;\nasync function getUploadMetadata(cloudbase, { cloudPath }, opts) {\n    let params = {\n        action: 'storage.getUploadMetadata',\n        path: cloudPath\n    };\n    const res = await httpRequest_1.default({\n        config: cloudbase.config,\n        params,\n        method: 'post',\n        opts,\n        headers: {\n            'content-type': 'application/json'\n        }\n    });\n    // if (res.code) {\n    //     throw E({\n    //         ...ERROR.STORAGE_REQUEST_FAIL,\n    //         message: 'get upload metadata failed: ' + res.code\n    //     })\n    // } else {\n    return res;\n    // }\n}\nexports.getUploadMetadata = getUploadMetadata;\nasync function getFileAuthority(cloudbase, { fileList }, opts) {\n    const { LOGINTYPE } = cloudbase_1.CloudBase.getCloudbaseContext();\n    if (!Array.isArray(fileList)) {\n        throw utils_1.E(Object.assign({}, code_1.ERROR.INVALID_PARAM, { message: '[node-sdk] getCosFileAuthority fileList must be a array' }));\n    }\n    if (fileList.some(file => {\n        if (!file || !file.path) {\n            return true;\n        }\n        if (['READ', 'WRITE', 'READWRITE'].indexOf(file.type) === -1) {\n            return true;\n        }\n        return false;\n    })) {\n        throw utils_1.E(Object.assign({}, code_1.ERROR.INVALID_PARAM, { message: '[node-sdk] getCosFileAuthority fileList param error' }));\n    }\n    const userInfo = cloudbase.auth().getUserInfo();\n    const { openId, uid } = userInfo;\n    if (!openId && !uid) {\n        throw utils_1.E(Object.assign({}, code_1.ERROR.INVALID_PARAM, { message: '[node-sdk] admin do not need getCosFileAuthority.' }));\n    }\n    let params = {\n        action: 'storage.getFileAuthority',\n        openId,\n        uid,\n        loginType: LOGINTYPE,\n        fileList\n    };\n    const res = await httpRequest_1.default({\n        config: cloudbase.config,\n        params,\n        method: 'post',\n        headers: {\n            'content-type': 'application/json'\n        }\n    });\n    if (res.code) {\n        /* istanbul ignore next  */\n        throw utils_1.E(Object.assign({}, res, { message: '[node-sdk] getCosFileAuthority failed: ' + res.code }));\n    }\n    else {\n        return res;\n    }\n}\nexports.getFileAuthority = getFileAuthority;\n", "\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst httpRequest_1 = __importDefault(require(\"../utils/httpRequest\"));\nconst utils_1 = require(\"../utils/utils\");\nconst code_1 = require(\"../const/code\");\nconst cloudbase_1 = require(\"../cloudbase\");\nconst reportTypes = ['mall'];\nfunction validateAnalyticsData(data) {\n    if (Object.prototype.toString.call(data).slice(8, -1) !== 'Object') {\n        return false;\n    }\n    const { report_data, report_type } = data;\n    if (reportTypes.includes(report_type) === false) {\n        return false;\n    }\n    if (Object.prototype.toString.call(report_data).slice(8, -1) !== 'Object') {\n        return false;\n    }\n    if (report_data.action_time !== undefined && !Number.isInteger(report_data.action_time)) {\n        return false;\n    }\n    if (typeof report_data.action_type !== 'string') {\n        return false;\n    }\n    return true;\n}\nasync function analytics(cloudbase, requestData) {\n    // 获取openid, wxappid\n    const { WX_OPENID, WX_APPID, } = cloudbase_1.CloudBase.getCloudbaseContext();\n    if (!validateAnalyticsData(requestData)) {\n        throw utils_1.E(Object.assign({}, code_1.ERROR.INVALID_PARAM, { message: '当前的上报数据结构不符合规范' }));\n    }\n    const action_time = requestData.report_data.action_time === undefined ? Math.floor(Date.now() / 1000) : requestData.report_data.action_time;\n    const transformRequestData = {\n        analytics_scene: requestData.report_type,\n        analytics_data: Object.assign({ openid: WX_OPENID, wechat_mini_program_appid: WX_APPID }, requestData.report_data, { action_time })\n    };\n    const params = {\n        action: 'analytics.report',\n        requestData: transformRequestData\n    };\n    return httpRequest_1.default({\n        config: cloudbase.config,\n        params,\n        method: 'post',\n        headers: {\n            'content-type': 'application/json'\n        }\n    });\n}\nexports.analytics = analytics;\n", "\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst httpRequest_1 = __importDefault(require(\"./httpRequest\"));\n/**\n * 数据库模块的通用请求方法\n *\n * <AUTHOR>\n * @internal\n */\nclass DBRequest {\n    /**\n     * 初始化\n     *\n     * @internal\n     * @param config\n     */\n    constructor(config) {\n        this.config = config;\n    }\n    /**\n     * 发送请求\n     *\n     * @param dbParams   - 数据库请求参数\n     * @param opts  - 可选配置项\n     */\n    async send(api, data, opts) {\n        const params = Object.assign({}, data, { action: api });\n        return httpRequest_1.default({\n            config: this.config,\n            params,\n            method: 'post',\n            opts,\n            headers: {\n                'content-type': 'application/json'\n            }\n        });\n    }\n}\nexports.DBRequest = DBRequest;\n", "\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst utils_1 = require(\"../utils/utils\");\nconst code_1 = require(\"../const/code\");\nconst cloudbase_1 = require(\"../cloudbase\");\n/**\n *\n *\n * @class Log\n */\nclass Log {\n    constructor() {\n        const { _SCF_TCB_LOG } = cloudbase_1.CloudBase.getCloudbaseContext();\n        this.src = 'app';\n        this.isSupportClsReport = true;\n        if (`${_SCF_TCB_LOG}` !== '1') {\n            this.isSupportClsReport = false;\n        }\n        else if (!console.__baseLog__) {\n            this.isSupportClsReport = false;\n        }\n        if (!this.isSupportClsReport) {\n            // 当前非tcb scf环境  log功能会退化为console\n            console.warn('请检查您是否在本地环境 或者 未开通高级日志功能，当前环境下无法上报cls日志，默认使用console');\n        }\n    }\n    /**\n     *\n     *\n     * @param {*} logMsg\n     * @param {*} logLevel\n     * @returns\n     * @memberof Log\n     */\n    transformMsg(logMsg) {\n        // 目前logMsg只支持字符串value且不支持多级, 加一层转换处理\n        let realMsg = {};\n        realMsg = Object.assign({}, realMsg, logMsg);\n        return realMsg;\n    }\n    /**\n     *\n     *\n     * @param {*} logMsg\n     * @param {*} logLevel\n     * @memberof Log\n     */\n    baseLog(logMsg, logLevel) {\n        // 判断当前是否属于tcb scf环境\n        if (Object.prototype.toString.call(logMsg).slice(8, -1) !== 'Object') {\n            throw utils_1.E(Object.assign({}, code_1.ERROR.INVALID_PARAM, { message: 'log msg must be an object' }));\n        }\n        const msgContent = this.transformMsg(logMsg);\n        if (this.isSupportClsReport) {\n            ;\n            console.__baseLog__(msgContent, logLevel);\n        }\n        else {\n            if (console[logLevel]) {\n                console[logLevel](msgContent);\n            }\n        }\n    }\n    /**\n     *\n     *\n     * @param {*} logMsg\n     * @memberof Log\n     */\n    log(logMsg) {\n        this.baseLog(logMsg, 'log');\n    }\n    /**\n     *\n     *\n     * @param {*} logMsg\n     * @memberof Log\n     */\n    info(logMsg) {\n        this.baseLog(logMsg, 'info');\n    }\n    /**\n     *\n     *\n     * @param {*} logMsg\n     * @memberof Log\n     */\n    error(logMsg) {\n        this.baseLog(logMsg, 'error');\n    }\n    /**\n     *\n     *\n     * @param {*} logMsg\n     * @memberof Log\n     */\n    warn(logMsg) {\n        this.baseLog(logMsg, 'warn');\n    }\n}\nexports.Log = Log;\nfunction logger() {\n    return new Log();\n}\nexports.logger = logger;\n"]}