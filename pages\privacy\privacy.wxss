/* pages/privacy/privacy.wxss */
.privacy-container {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 90%;
  max-height: 80vh;
  background-color: #ffffff;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.15);
  z-index: 1000;
}

.privacy-title {
  font-size: 36rpx;
  font-weight: bold;
  text-align: center;
  margin-bottom: 30rpx;
  color: #333;
}

.privacy-content {
  height: 60vh;
  overflow-y: auto;
  background-color: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.privacy-content text {
  font-size: 28rpx;
  line-height: 1.6;
  color: #666;
}

.privacy-actions {
  display: flex;
  justify-content: space-between;
  padding: 0 30rpx;
  margin-bottom: 30rpx;
}

.privacy-btn {
  width: 45%;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  border-radius: 40rpx;
  font-size: 32rpx;
}

.disagree {
  background-color: #f5f5f5;
  color: #999;
}

.agree {
  background-color: #4CAF50;
  color: #fff;
}