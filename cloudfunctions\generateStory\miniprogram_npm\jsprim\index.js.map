{"version": 3, "sources": ["jsprim.js"], "names": [], "mappings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file": "index.js", "sourcesContent": ["/*\n * lib/jsprim.js: utilities for primitive JavaScript types\n */\n\nvar mod_assert = require('assert-plus');\nvar mod_util = require('util');\n\nvar mod_extsprintf = require('extsprintf');\nvar mod_verror = require('verror');\nvar mod_jsonschema = require('json-schema');\n\n/*\n * Public interface\n */\nexports.deepCopy = deepCopy;\nexports.deepEqual = deepEqual;\nexports.isEmpty = isEmpty;\nexports.hasKey = hasKey;\nexports.forEachKey = forEachKey;\nexports.pluck = pluck;\nexports.flattenObject = flattenObject;\nexports.flattenIter = flattenIter;\nexports.validateJsonObject = validateJsonObjectJS;\nexports.validateJsonObjectJS = validateJsonObjectJS;\nexports.randElt = randElt;\nexports.extraProperties = extraProperties;\nexports.mergeObjects = mergeObjects;\n\nexports.startsWith = startsWith;\nexports.endsWith = endsWith;\n\nexports.parseInteger = parseInteger;\n\nexports.iso8601 = iso8601;\nexports.rfc1123 = rfc1123;\nexports.parseDateTime = parseDateTime;\n\nexports.hrtimediff = hrtimeDiff;\nexports.hrtimeDiff = hrtimeDiff;\nexports.hrtimeAccum = hrtimeAccum;\nexports.hrtimeAdd = hrtimeAdd;\nexports.hrtimeNanosec = hrtimeNanosec;\nexports.hrtimeMicrosec = hrtimeMicrosec;\nexports.hrtimeMillisec = hrtimeMillisec;\n\n\n/*\n * Deep copy an acyclic *basic* Javascript object.  This only handles basic\n * scalars (strings, numbers, booleans) and arbitrarily deep arrays and objects\n * containing these.  This does *not* handle instances of other classes.\n */\nfunction deepCopy(obj)\n{\n\tvar ret, key;\n\tvar marker = '__deepCopy';\n\n\tif (obj && obj[marker])\n\t\tthrow (new Error('attempted deep copy of cyclic object'));\n\n\tif (obj && obj.constructor == Object) {\n\t\tret = {};\n\t\tobj[marker] = true;\n\n\t\tfor (key in obj) {\n\t\t\tif (key == marker)\n\t\t\t\tcontinue;\n\n\t\t\tret[key] = deepCopy(obj[key]);\n\t\t}\n\n\t\tdelete (obj[marker]);\n\t\treturn (ret);\n\t}\n\n\tif (obj && obj.constructor == Array) {\n\t\tret = [];\n\t\tobj[marker] = true;\n\n\t\tfor (key = 0; key < obj.length; key++)\n\t\t\tret.push(deepCopy(obj[key]));\n\n\t\tdelete (obj[marker]);\n\t\treturn (ret);\n\t}\n\n\t/*\n\t * It must be a primitive type -- just return it.\n\t */\n\treturn (obj);\n}\n\nfunction deepEqual(obj1, obj2)\n{\n\tif (typeof (obj1) != typeof (obj2))\n\t\treturn (false);\n\n\tif (obj1 === null || obj2 === null || typeof (obj1) != 'object')\n\t\treturn (obj1 === obj2);\n\n\tif (obj1.constructor != obj2.constructor)\n\t\treturn (false);\n\n\tvar k;\n\tfor (k in obj1) {\n\t\tif (!obj2.hasOwnProperty(k))\n\t\t\treturn (false);\n\n\t\tif (!deepEqual(obj1[k], obj2[k]))\n\t\t\treturn (false);\n\t}\n\n\tfor (k in obj2) {\n\t\tif (!obj1.hasOwnProperty(k))\n\t\t\treturn (false);\n\t}\n\n\treturn (true);\n}\n\nfunction isEmpty(obj)\n{\n\tvar key;\n\tfor (key in obj)\n\t\treturn (false);\n\treturn (true);\n}\n\nfunction hasKey(obj, key)\n{\n\tmod_assert.equal(typeof (key), 'string');\n\treturn (Object.prototype.hasOwnProperty.call(obj, key));\n}\n\nfunction forEachKey(obj, callback)\n{\n\tfor (var key in obj) {\n\t\tif (hasKey(obj, key)) {\n\t\t\tcallback(key, obj[key]);\n\t\t}\n\t}\n}\n\nfunction pluck(obj, key)\n{\n\tmod_assert.equal(typeof (key), 'string');\n\treturn (pluckv(obj, key));\n}\n\nfunction pluckv(obj, key)\n{\n\tif (obj === null || typeof (obj) !== 'object')\n\t\treturn (undefined);\n\n\tif (obj.hasOwnProperty(key))\n\t\treturn (obj[key]);\n\n\tvar i = key.indexOf('.');\n\tif (i == -1)\n\t\treturn (undefined);\n\n\tvar key1 = key.substr(0, i);\n\tif (!obj.hasOwnProperty(key1))\n\t\treturn (undefined);\n\n\treturn (pluckv(obj[key1], key.substr(i + 1)));\n}\n\n/*\n * Invoke callback(row) for each entry in the array that would be returned by\n * flattenObject(data, depth).  This is just like flattenObject(data,\n * depth).forEach(callback), except that the intermediate array is never\n * created.\n */\nfunction flattenIter(data, depth, callback)\n{\n\tdoFlattenIter(data, depth, [], callback);\n}\n\nfunction doFlattenIter(data, depth, accum, callback)\n{\n\tvar each;\n\tvar key;\n\n\tif (depth === 0) {\n\t\teach = accum.slice(0);\n\t\teach.push(data);\n\t\tcallback(each);\n\t\treturn;\n\t}\n\n\tmod_assert.ok(data !== null);\n\tmod_assert.equal(typeof (data), 'object');\n\tmod_assert.equal(typeof (depth), 'number');\n\tmod_assert.ok(depth >= 0);\n\n\tfor (key in data) {\n\t\teach = accum.slice(0);\n\t\teach.push(key);\n\t\tdoFlattenIter(data[key], depth - 1, each, callback);\n\t}\n}\n\nfunction flattenObject(data, depth)\n{\n\tif (depth === 0)\n\t\treturn ([ data ]);\n\n\tmod_assert.ok(data !== null);\n\tmod_assert.equal(typeof (data), 'object');\n\tmod_assert.equal(typeof (depth), 'number');\n\tmod_assert.ok(depth >= 0);\n\n\tvar rv = [];\n\tvar key;\n\n\tfor (key in data) {\n\t\tflattenObject(data[key], depth - 1).forEach(function (p) {\n\t\t\trv.push([ key ].concat(p));\n\t\t});\n\t}\n\n\treturn (rv);\n}\n\nfunction startsWith(str, prefix)\n{\n\treturn (str.substr(0, prefix.length) == prefix);\n}\n\nfunction endsWith(str, suffix)\n{\n\treturn (str.substr(\n\t    str.length - suffix.length, suffix.length) == suffix);\n}\n\nfunction iso8601(d)\n{\n\tif (typeof (d) == 'number')\n\t\td = new Date(d);\n\tmod_assert.ok(d.constructor === Date);\n\treturn (mod_extsprintf.sprintf('%4d-%02d-%02dT%02d:%02d:%02d.%03dZ',\n\t    d.getUTCFullYear(), d.getUTCMonth() + 1, d.getUTCDate(),\n\t    d.getUTCHours(), d.getUTCMinutes(), d.getUTCSeconds(),\n\t    d.getUTCMilliseconds()));\n}\n\nvar RFC1123_MONTHS = [\n    'Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',\n    'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];\nvar RFC1123_DAYS = [\n    'Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];\n\nfunction rfc1123(date) {\n\treturn (mod_extsprintf.sprintf('%s, %02d %s %04d %02d:%02d:%02d GMT',\n\t    RFC1123_DAYS[date.getUTCDay()], date.getUTCDate(),\n\t    RFC1123_MONTHS[date.getUTCMonth()], date.getUTCFullYear(),\n\t    date.getUTCHours(), date.getUTCMinutes(),\n\t    date.getUTCSeconds()));\n}\n\n/*\n * Parses a date expressed as a string, as either a number of milliseconds since\n * the epoch or any string format that Date accepts, giving preference to the\n * former where these two sets overlap (e.g., small numbers).\n */\nfunction parseDateTime(str)\n{\n\t/*\n\t * This is irritatingly implicit, but significantly more concise than\n\t * alternatives.  The \"+str\" will convert a string containing only a\n\t * number directly to a Number, or NaN for other strings.  Thus, if the\n\t * conversion succeeds, we use it (this is the milliseconds-since-epoch\n\t * case).  Otherwise, we pass the string directly to the Date\n\t * constructor to parse.\n\t */\n\tvar numeric = +str;\n\tif (!isNaN(numeric)) {\n\t\treturn (new Date(numeric));\n\t} else {\n\t\treturn (new Date(str));\n\t}\n}\n\n\n/*\n * Number.*_SAFE_INTEGER isn't present before node v0.12, so we hardcode\n * the ES6 definitions here, while allowing for them to someday be higher.\n */\nvar MAX_SAFE_INTEGER = Number.MAX_SAFE_INTEGER || 9007199254740991;\nvar MIN_SAFE_INTEGER = Number.MIN_SAFE_INTEGER || -9007199254740991;\n\n\n/*\n * Default options for parseInteger().\n */\nvar PI_DEFAULTS = {\n\tbase: 10,\n\tallowSign: true,\n\tallowPrefix: false,\n\tallowTrailing: false,\n\tallowImprecise: false,\n\ttrimWhitespace: false,\n\tleadingZeroIsOctal: false\n};\n\nvar CP_0 = 0x30;\nvar CP_9 = 0x39;\n\nvar CP_A = 0x41;\nvar CP_B = 0x42;\nvar CP_O = 0x4f;\nvar CP_T = 0x54;\nvar CP_X = 0x58;\nvar CP_Z = 0x5a;\n\nvar CP_a = 0x61;\nvar CP_b = 0x62;\nvar CP_o = 0x6f;\nvar CP_t = 0x74;\nvar CP_x = 0x78;\nvar CP_z = 0x7a;\n\nvar PI_CONV_DEC = 0x30;\nvar PI_CONV_UC = 0x37;\nvar PI_CONV_LC = 0x57;\n\n\n/*\n * A stricter version of parseInt() that provides options for changing what\n * is an acceptable string (for example, disallowing trailing characters).\n */\nfunction parseInteger(str, uopts)\n{\n\tmod_assert.string(str, 'str');\n\tmod_assert.optionalObject(uopts, 'options');\n\n\tvar baseOverride = false;\n\tvar options = PI_DEFAULTS;\n\n\tif (uopts) {\n\t\tbaseOverride = hasKey(uopts, 'base');\n\t\toptions = mergeObjects(options, uopts);\n\t\tmod_assert.number(options.base, 'options.base');\n\t\tmod_assert.ok(options.base >= 2, 'options.base >= 2');\n\t\tmod_assert.ok(options.base <= 36, 'options.base <= 36');\n\t\tmod_assert.bool(options.allowSign, 'options.allowSign');\n\t\tmod_assert.bool(options.allowPrefix, 'options.allowPrefix');\n\t\tmod_assert.bool(options.allowTrailing,\n\t\t    'options.allowTrailing');\n\t\tmod_assert.bool(options.allowImprecise,\n\t\t    'options.allowImprecise');\n\t\tmod_assert.bool(options.trimWhitespace,\n\t\t    'options.trimWhitespace');\n\t\tmod_assert.bool(options.leadingZeroIsOctal,\n\t\t    'options.leadingZeroIsOctal');\n\n\t\tif (options.leadingZeroIsOctal) {\n\t\t\tmod_assert.ok(!baseOverride,\n\t\t\t    '\"base\" and \"leadingZeroIsOctal\" are ' +\n\t\t\t    'mutually exclusive');\n\t\t}\n\t}\n\n\tvar c;\n\tvar pbase = -1;\n\tvar base = options.base;\n\tvar start;\n\tvar mult = 1;\n\tvar value = 0;\n\tvar idx = 0;\n\tvar len = str.length;\n\n\t/* Trim any whitespace on the left side. */\n\tif (options.trimWhitespace) {\n\t\twhile (idx < len && isSpace(str.charCodeAt(idx))) {\n\t\t\t++idx;\n\t\t}\n\t}\n\n\t/* Check the number for a leading sign. */\n\tif (options.allowSign) {\n\t\tif (str[idx] === '-') {\n\t\t\tidx += 1;\n\t\t\tmult = -1;\n\t\t} else if (str[idx] === '+') {\n\t\t\tidx += 1;\n\t\t}\n\t}\n\n\t/* Parse the base-indicating prefix if there is one. */\n\tif (str[idx] === '0') {\n\t\tif (options.allowPrefix) {\n\t\t\tpbase = prefixToBase(str.charCodeAt(idx + 1));\n\t\t\tif (pbase !== -1 && (!baseOverride || pbase === base)) {\n\t\t\t\tbase = pbase;\n\t\t\t\tidx += 2;\n\t\t\t}\n\t\t}\n\n\t\tif (pbase === -1 && options.leadingZeroIsOctal) {\n\t\t\tbase = 8;\n\t\t}\n\t}\n\n\t/* Parse the actual digits. */\n\tfor (start = idx; idx < len; ++idx) {\n\t\tc = translateDigit(str.charCodeAt(idx));\n\t\tif (c !== -1 && c < base) {\n\t\t\tvalue *= base;\n\t\t\tvalue += c;\n\t\t} else {\n\t\t\tbreak;\n\t\t}\n\t}\n\n\t/* If we didn't parse any digits, we have an invalid number. */\n\tif (start === idx) {\n\t\treturn (new Error('invalid number: ' + JSON.stringify(str)));\n\t}\n\n\t/* Trim any whitespace on the right side. */\n\tif (options.trimWhitespace) {\n\t\twhile (idx < len && isSpace(str.charCodeAt(idx))) {\n\t\t\t++idx;\n\t\t}\n\t}\n\n\t/* Check for trailing characters. */\n\tif (idx < len && !options.allowTrailing) {\n\t\treturn (new Error('trailing characters after number: ' +\n\t\t    JSON.stringify(str.slice(idx))));\n\t}\n\n\t/* If our value is 0, we return now, to avoid returning -0. */\n\tif (value === 0) {\n\t\treturn (0);\n\t}\n\n\t/* Calculate our final value. */\n\tvar result = value * mult;\n\n\t/*\n\t * If the string represents a value that cannot be precisely represented\n\t * by JavaScript, then we want to check that:\n\t *\n\t * - We never increased the value past MAX_SAFE_INTEGER\n\t * - We don't make the result negative and below MIN_SAFE_INTEGER\n\t *\n\t * Because we only ever increment the value during parsing, there's no\n\t * chance of moving past MAX_SAFE_INTEGER and then dropping below it\n\t * again, losing precision in the process. This means that we only need\n\t * to do our checks here, at the end.\n\t */\n\tif (!options.allowImprecise &&\n\t    (value > MAX_SAFE_INTEGER || result < MIN_SAFE_INTEGER)) {\n\t\treturn (new Error('number is outside of the supported range: ' +\n\t\t    JSON.stringify(str.slice(start, idx))));\n\t}\n\n\treturn (result);\n}\n\n\n/*\n * Interpret a character code as a base-36 digit.\n */\nfunction translateDigit(d)\n{\n\tif (d >= CP_0 && d <= CP_9) {\n\t\t/* '0' to '9' -> 0 to 9 */\n\t\treturn (d - PI_CONV_DEC);\n\t} else if (d >= CP_A && d <= CP_Z) {\n\t\t/* 'A' - 'Z' -> 10 to 35 */\n\t\treturn (d - PI_CONV_UC);\n\t} else if (d >= CP_a && d <= CP_z) {\n\t\t/* 'a' - 'z' -> 10 to 35 */\n\t\treturn (d - PI_CONV_LC);\n\t} else {\n\t\t/* Invalid character code */\n\t\treturn (-1);\n\t}\n}\n\n\n/*\n * Test if a value matches the ECMAScript definition of trimmable whitespace.\n */\nfunction isSpace(c)\n{\n\treturn (c === 0x20) ||\n\t    (c >= 0x0009 && c <= 0x000d) ||\n\t    (c === 0x00a0) ||\n\t    (c === 0x1680) ||\n\t    (c === 0x180e) ||\n\t    (c >= 0x2000 && c <= 0x200a) ||\n\t    (c === 0x2028) ||\n\t    (c === 0x2029) ||\n\t    (c === 0x202f) ||\n\t    (c === 0x205f) ||\n\t    (c === 0x3000) ||\n\t    (c === 0xfeff);\n}\n\n\n/*\n * Determine which base a character indicates (e.g., 'x' indicates hex).\n */\nfunction prefixToBase(c)\n{\n\tif (c === CP_b || c === CP_B) {\n\t\t/* 0b/0B (binary) */\n\t\treturn (2);\n\t} else if (c === CP_o || c === CP_O) {\n\t\t/* 0o/0O (octal) */\n\t\treturn (8);\n\t} else if (c === CP_t || c === CP_T) {\n\t\t/* 0t/0T (decimal) */\n\t\treturn (10);\n\t} else if (c === CP_x || c === CP_X) {\n\t\t/* 0x/0X (hexadecimal) */\n\t\treturn (16);\n\t} else {\n\t\t/* Not a meaningful character */\n\t\treturn (-1);\n\t}\n}\n\n\nfunction validateJsonObjectJS(schema, input)\n{\n\tvar report = mod_jsonschema.validate(input, schema);\n\n\tif (report.errors.length === 0)\n\t\treturn (null);\n\n\t/* Currently, we only do anything useful with the first error. */\n\tvar error = report.errors[0];\n\n\t/* The failed property is given by a URI with an irrelevant prefix. */\n\tvar propname = error['property'];\n\tvar reason = error['message'].toLowerCase();\n\tvar i, j;\n\n\t/*\n\t * There's at least one case where the property error message is\n\t * confusing at best.  We work around this here.\n\t */\n\tif ((i = reason.indexOf('the property ')) != -1 &&\n\t    (j = reason.indexOf(' is not defined in the schema and the ' +\n\t    'schema does not allow additional properties')) != -1) {\n\t\ti += 'the property '.length;\n\t\tif (propname === '')\n\t\t\tpropname = reason.substr(i, j - i);\n\t\telse\n\t\t\tpropname = propname + '.' + reason.substr(i, j - i);\n\n\t\treason = 'unsupported property';\n\t}\n\n\tvar rv = new mod_verror.VError('property \"%s\": %s', propname, reason);\n\trv.jsv_details = error;\n\treturn (rv);\n}\n\nfunction randElt(arr)\n{\n\tmod_assert.ok(Array.isArray(arr) && arr.length > 0,\n\t    'randElt argument must be a non-empty array');\n\n\treturn (arr[Math.floor(Math.random() * arr.length)]);\n}\n\nfunction assertHrtime(a)\n{\n\tmod_assert.ok(a[0] >= 0 && a[1] >= 0,\n\t    'negative numbers not allowed in hrtimes');\n\tmod_assert.ok(a[1] < 1e9, 'nanoseconds column overflow');\n}\n\n/*\n * Compute the time elapsed between hrtime readings A and B, where A is later\n * than B.  hrtime readings come from Node's process.hrtime().  There is no\n * defined way to represent negative deltas, so it's illegal to diff B from A\n * where the time denoted by B is later than the time denoted by A.  If this\n * becomes valuable, we can define a representation and extend the\n * implementation to support it.\n */\nfunction hrtimeDiff(a, b)\n{\n\tassertHrtime(a);\n\tassertHrtime(b);\n\tmod_assert.ok(a[0] > b[0] || (a[0] == b[0] && a[1] >= b[1]),\n\t    'negative differences not allowed');\n\n\tvar rv = [ a[0] - b[0], 0 ];\n\n\tif (a[1] >= b[1]) {\n\t\trv[1] = a[1] - b[1];\n\t} else {\n\t\trv[0]--;\n\t\trv[1] = 1e9 - (b[1] - a[1]);\n\t}\n\n\treturn (rv);\n}\n\n/*\n * Convert a hrtime reading from the array format returned by Node's\n * process.hrtime() into a scalar number of nanoseconds.\n */\nfunction hrtimeNanosec(a)\n{\n\tassertHrtime(a);\n\n\treturn (Math.floor(a[0] * 1e9 + a[1]));\n}\n\n/*\n * Convert a hrtime reading from the array format returned by Node's\n * process.hrtime() into a scalar number of microseconds.\n */\nfunction hrtimeMicrosec(a)\n{\n\tassertHrtime(a);\n\n\treturn (Math.floor(a[0] * 1e6 + a[1] / 1e3));\n}\n\n/*\n * Convert a hrtime reading from the array format returned by Node's\n * process.hrtime() into a scalar number of milliseconds.\n */\nfunction hrtimeMillisec(a)\n{\n\tassertHrtime(a);\n\n\treturn (Math.floor(a[0] * 1e3 + a[1] / 1e6));\n}\n\n/*\n * Add two hrtime readings A and B, overwriting A with the result of the\n * addition.  This function is useful for accumulating several hrtime intervals\n * into a counter.  Returns A.\n */\nfunction hrtimeAccum(a, b)\n{\n\tassertHrtime(a);\n\tassertHrtime(b);\n\n\t/*\n\t * Accumulate the nanosecond component.\n\t */\n\ta[1] += b[1];\n\tif (a[1] >= 1e9) {\n\t\t/*\n\t\t * The nanosecond component overflowed, so carry to the seconds\n\t\t * field.\n\t\t */\n\t\ta[0]++;\n\t\ta[1] -= 1e9;\n\t}\n\n\t/*\n\t * Accumulate the seconds component.\n\t */\n\ta[0] += b[0];\n\n\treturn (a);\n}\n\n/*\n * Add two hrtime readings A and B, returning the result as a new hrtime array.\n * Does not modify either input argument.\n */\nfunction hrtimeAdd(a, b)\n{\n\tassertHrtime(a);\n\n\tvar rv = [ a[0], a[1] ];\n\n\treturn (hrtimeAccum(rv, b));\n}\n\n\n/*\n * Check an object for unexpected properties.  Accepts the object to check, and\n * an array of allowed property names (strings).  Returns an array of key names\n * that were found on the object, but did not appear in the list of allowed\n * properties.  If no properties were found, the returned array will be of\n * zero length.\n */\nfunction extraProperties(obj, allowed)\n{\n\tmod_assert.ok(typeof (obj) === 'object' && obj !== null,\n\t    'obj argument must be a non-null object');\n\tmod_assert.ok(Array.isArray(allowed),\n\t    'allowed argument must be an array of strings');\n\tfor (var i = 0; i < allowed.length; i++) {\n\t\tmod_assert.ok(typeof (allowed[i]) === 'string',\n\t\t    'allowed argument must be an array of strings');\n\t}\n\n\treturn (Object.keys(obj).filter(function (key) {\n\t\treturn (allowed.indexOf(key) === -1);\n\t}));\n}\n\n/*\n * Given three sets of properties \"provided\" (may be undefined), \"overrides\"\n * (required), and \"defaults\" (may be undefined), construct an object containing\n * the union of these sets with \"overrides\" overriding \"provided\", and\n * \"provided\" overriding \"defaults\".  None of the input objects are modified.\n */\nfunction mergeObjects(provided, overrides, defaults)\n{\n\tvar rv, k;\n\n\trv = {};\n\tif (defaults) {\n\t\tfor (k in defaults)\n\t\t\trv[k] = defaults[k];\n\t}\n\n\tif (provided) {\n\t\tfor (k in provided)\n\t\t\trv[k] = provided[k];\n\t}\n\n\tif (overrides) {\n\t\tfor (k in overrides)\n\t\t\trv[k] = overrides[k];\n\t}\n\n\treturn (rv);\n}\n"]}