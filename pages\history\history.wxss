.container {
  padding: 20rpx;
  min-height: 100vh;
  box-sizing: border-box;
  background-image: url('https://636c-cloudbase-1gahfeezccaf7d37-1354986900.tcb.qcloud.la/backgroud.png');
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center;
}

.custom-nav-bar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    height: 180rpx;
    background-color: rgba(48, 47, 51, 0.4);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 999;
    backdrop-filter: blur(10px);
    padding-bottom: 10rpx;
}
.custom-nav-bar::after {
    content: "";
    position: absolute;
    left: 0;
    right: 0;
    bottom: 0;
    top: 0;
    background: rgba(48, 47, 51, 0.4);
    z-index: -1;
    border-radius: 0 0 32rpx 32rpx;
    pointer-events: none;
}

.clear-image {
   width: 56rpx;
  height: 56rpx;
  border-radius:50%;
  overflow: hidden;
  margin-left:32rpx;
}

.nav-title {
  color:rgb(200, 200, 241);
  font-size: 36rpx;
  flex-grow: 1;
  text-align: center;
  margin-right:76rpx;
  display: none;
}

/* 导航栏中的tab样式 */
.nav-tabs {
  display: flex;
  width: 36%;
  margin: 0 auto;
  background: rgba(87, 87, 85, 0.6);
  border-radius: 50rpx;
  margin-right:236rpx;
}

.nav-tab {
  flex: 1;
  text-align: center;
  padding: 12rpx 0;
  font-size: 30rpx;
  color: rgb(211, 209, 209);
  position: relative;
  transition: all 0.3s;
  margin: 0 1rpx;
}

.nav-tab:first-child {
  border-radius: 50rpx 0 0 50rpx;
}

.nav-tab:last-child {
  border-radius: 0 50rpx 50rpx 0;
}

.nav-tab.active {
  background: rgba(174, 146, 215, 0.9);
  color: #fff;
  font-weight: 500;
}

/* Tab样式 */
.tab-container {
  display: flex;
  justify-content: space-around;
  align-items: center;
  width: 100%;
  height: 80rpx;
  background-color: rgba(48, 47, 51, 0.4);
  margin-top: 120rpx;
  position: fixed;
  z-index: 998;
  backdrop-filter: blur(5px);
}

.tab-item {
  flex: 1;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  color:rgb(211, 209, 209);
  font-size: 32rpx;
  position: relative;
  transition: all 0.3s;
}

.tab-item.active {
  color:rgb(212, 188, 250);
}

.tab-item.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 30%;
  width: 40%;
  height: 1rpx;
  background-color:rgb(212, 188, 250);
  border-radius: 1rpx;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 60vh;
  text-align: center;
}

.empty-state text {
  font-size: 42rpx;
  color: #fff;
  margin-bottom: 20rpx;
}

.empty-state .subtitle {
  font-size: 28rpx;
  color:rgb(170, 168, 168);
}

.story-section {
  margin-bottom: 30rpx;
  margin-top: 150rpx;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #fff;
  margin-left:10rpx;
}

.clear-btn {
  margin-top:2rpx;
  margin-bottom:10rpx;
  font-size: 32rpx;
  color: #fff;
  left:10%;
  position: fixed;
  left: 4%;
}

.story-list {
  margin-top:150rpx;
  display: flex;
  flex-direction: column;
  gap: 20rpx;
  padding-top: 20rpx;
  padding-bottom: 150rpx;
}

/* 内容容器样式 */
.tab-content-container {
  width: 100%;
  position: relative;
}

.tab-content {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
}

.like-btn image {
  width: 40rpx;
  height: 40rpx;
}

.story-tap-area {
  display: flex;
  flex: 1;
  width: 100%;
  height: 100%;
}

.story-item-wrapper {
  width: 100%;
  height: 230rpx;
  position: relative;
  margin-bottom: 6rpx;
  overflow: hidden;
  box-sizing: border-box;
}

.movable-container {
  width: calc(100% + 120rpx);
  height: 100%;
  position: relative;
  left: -120rpx;
  overflow: hidden;
}

.story-item {
  width: calc(100% - 120rpx);
  flex-direction: row;
  height: 100%;
  background: #fff;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
  z-index: 2;
  position: absolute;
  top: 0;
  left: 120rpx;
  box-sizing: border-box;
  transition: transform 0.25s ease-out;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.4);
  background-color:rgb(19, 18, 31);
}

.story-content-wrapper {
  width: 100%;
  height: 100%;
  line-height:1.4;
  padding: 20rpx;
  box-sizing: border-box;
  display: flex;
  flex-direction: row;
  align-items: center;
}

.story-text-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: 100%;
  padding-left: 20rpx;
}

.delete-btn {
  position: absolute;
  top: 0;
  right: 10rpx;
  width: 150rpx;
  height: 99%;
  background-color: #ff4d4f;
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 0 15rpx 15rpx 0;
  z-index: 1;
  transition: transform 0.25s ease-out;
  font-size: 30rpx;
  transform: translateX(150rpx);
}

.story-info {
  flex: 1;
  margin-right: 20rpx;
  overflow: hidden;
}

.story-image{
  width:200rpx;
  height:200rpx;
  border-radius:16rpx;
}

.story-title {
  padding:0;
  font-size: 32rpx;
  color: #fff;
  margin-bottom: 16rpx;
}

.story-content {
  padding: 0;
  font-size: 28rpx;
  color:rgb(164, 162, 162);
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.5;
  margin-bottom: 16rpx;
  word-break: break-all;
  max-height: 2.8em;
  width: 100%;
}

.story-time {
  position: relative;
  font-size: 24rpx;
  color: #999;
  display: block;
  margin-top: 8rpx;
  padding-bottom:12rpx;
  text-align: right;
}

.custom-tag {
  position: absolute;
  left: 8rpx;
  bottom: 2rpx;
  font-size: 22rpx;
  color: #ffd700;
  padding: 4rpx 16rpx;
  border-radius: 8rpx;
  background-color: rgba(255, 215, 0, 0.15);
}

.play-icon {
  width: 48rpx;
  height: 48rpx;
}

.AI-btn {
  position: fixed;
  bottom: 20rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 140rpx;
  height: 140rpx;
  background-size: cover;
  background-position: center;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 16rpx rgba(37, 6, 6, 0.2);
  transition: all 0.3s ease;
  z-index: 1001;
  margin-bottom:2rpx;
}

.AI-btn:active {
  transform: translateX(-50%) scale(0.95);
  background: rgb(198, 181, 35);
}

.AI-btn image {
  width: 140rpx;
  height: 140rpx;
  border-radius:130rpx;
}

.tab-home {
  position: fixed;
  bottom: 20rpx;
  left: 22%;
  transform: translateX(-50%);
  width: 130rpx;
  height: 130rpx;
  background-size: cover;
  background-position: center;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
  z-index: 1001;
  margin-bottom:2rpx;
}

.tab-home::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(105, 104, 104, 0.3); /* 透明黑色蒙版，数值可调整 */
  border-radius: 50%;
  z-index: 0; /* 确保覆盖图标 */
}

.tab-home:active {
  transform: translateX(-50%) scale(0.95);
  background: rgb(221, 180, 58);
}

.tab-home image {
  width: 100rpx;
  height: 100rpx;
  border-radius:80rpx;
}

.tab-history-inactive {
  background: radial-gradient(rgba(254, 245, 163, 1), rgba(222, 209, 85, 0.2));
  position: fixed;
  bottom: 20rpx;
  left: 78%;
  transform: translateX(-50%);
  width: 135rpx;
  height: 135rpx;
  background-size: cover;
  background-position: center;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
  z-index: 1001;
  margin-bottom: 2rpx;
}

/* 把 image 标签选择器改为 class 选择器 */
.tab-history-inactive .tab-image {
  width: 110rpx;
  height: 110rpx;
  border-radius: 80rpx;
}