.container {
  padding: 30rpx;
  background-image: url('https://636c-cloudbase-1gahfeezccaf7d37-1354986900.tcb.qcloud.la/backgroud.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
  background-position: center;
  background-attachment: fixed;
  height: 100vh;
  box-sizing: border-box;
}

.custom-nav-bar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    height: 180rpx;
    background-color: rgba(48, 47, 51, 0.4);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 999;
    backdrop-filter: blur(10px);
    padding-bottom: 10rpx;
}
.custom-nav-bar::after {
    content: "";
    position: absolute;
    left: 0;
    right: 0;
    bottom: 0;
    top: 0;
    background: rgba(48, 47, 51, 0.4);
    z-index: -1;
    border-radius: 0 0 32rpx 32rpx;
    pointer-events: none;
}

.back-icon {
  width: 56rpx;
  height: 56rpx;
  margin-left: 20rpx;
}

.nav-title {
  font-size: 36rpx;
  flex: 1;
  text-align: center;
  margin-right: 90rpx;
  color:rgb(255, 255, 255);
}

.section-voice {
  margin-bottom: 20rpx;
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 20rpx;
  padding: 30rpx;
  backdrop-filter: blur(10px);
  margin-top:160rpx;
}

.section {
  margin-bottom: 20rpx;
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 20rpx;
  padding: 30rpx;
  backdrop-filter: blur(10px);
}

.label {
  font-size: 29rpx;
  margin-bottom: 20rpx;
  display: block;
  color:rgb(210, 205, 205);
  font-weight: 500;
}

.options {
  display: flex;
  gap: 16rpx;
  justify-content: space-between;
}

.options button {
  flex: 1;
  margin: 0;
  padding: 20rpx 10rpx;
  background-color: rgba(255, 255, 255, 0.2);
  color: #ffffff;
  font-size: 32rpx;
  border-radius: 56rpx;
  transition: all 0.3s;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}

.active {
  background-color: #4CAF50 !important;
  color: white !important;
}

.description-box {
  border: 2rpx solid rgba(255, 255, 255, 0.2);
  padding: 30rpx;
  min-height: 300rpx;
  position: relative;
  border-radius: 16rpx;
  background-color: rgba(255, 255, 255, 0.1);
  color:rgb(208, 205, 205);
  font-size: 28rpx;
}

.description-box textarea {
  width: 100%;
  min-height: 120rpx;
  color: rgb(255, 255, 255);
  font-size: 32rpx;
  background-color: transparent;
}

.bottom-bar {
  position: fixed;
  bottom: 30rpx;
  left: 30rpx;
  right: 30rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: rgba(255, 255, 255, 0.1);
  padding: 10rpx;
  border-radius: 16rpx;
  backdrop-filter: blur(10px);
}

.action-icon {
  width: 70rpx;
  height: 80rpx;
  padding: 15rpx;
  opacity: 1;
  transition: all 0.3s ease;
  border-radius: 50%;
}

.action-icon.disabled {
  opacity: 0.5;
  pointer-events: none;
}

.action-icon:hover {
  opacity: 1;
  transform: scale(1.1);
}

.record-btn {
  flex: 1;
  margin: 0 30rpx;
  padding: 28rpx;
  border-radius: 20rpx;
  font-size: 32rpx;
  background-color: rgba(27, 133, 239, 0.8);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.record-btn.recording {
  background-color: rgba(255, 59, 48, 0.9);
  transform: scale(0.98);
}

.record-btn:active {
  background-color: rgba(255, 59, 48, 0.9);
  transform: scale(0.98);
}

.generate-btn {
  width: 100%;
  background-color: rgba(76, 175, 80, 0.8);
  color: white;
  padding: 24rpx;
  border-radius: 56rpx;
  font-size: 32rpx;
  margin-top: 40rpx;
  font-weight: 500;
  backdrop-filter: blur(10px);
}
