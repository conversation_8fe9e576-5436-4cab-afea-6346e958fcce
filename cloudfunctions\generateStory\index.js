// 云函数入口文件
const cloud = require('wx-server-sdk')
const axios = require('axios')

cloud.init({ env: cloud.DYNAMIC_CURRENT_ENV });

const db = cloud.database()

// 获取配置参数
async function getConfig() {
  const res = await db.collection('config').doc('story_config').get()
  return res.data
}

// 云函数入口函数
exports.main = async (event, context) => {
  const { storyId, description, duration, voiceType } = event
  
  if (!storyId || !description || !duration || !voiceType) {
    return {
      code: -1,
      data: null,
      message: '参数不完整'
    }
  }

  try {

    const { OPENID } = cloud.getWXContext()
    const { data: babyInfoData } = await db.collection('baby_info')
      .where({
        _openid: OPENID
      })
      .get()
    
    const gender = babyInfoData[0]?.gender || 'unknown'
    
    const response = await generateStory(description, 3, duration, OPENID, gender)
    
    const storyContent = await handleStreamingResponse(response, storyId, voiceType, OPENID)
    
    await updateStoryStatus(storyId, 'completed')

    // After story is complete, fetch the full story data to return
    const storyResult = await db.collection('stories').doc(storyId).get();
    
    return {
      code: 0,
      data: storyResult.data, // Return the full story object
      message: '故事生成成功'
    }
  } catch (error) {
    console.error('生成故事失败：', error)
    await updateStoryStatus(storyId, 'failed', error.message)
    return {
      code: -1,
      data: null,
      message: `生成故事失败: ${error.message}`
    }
  }
}

// ... (The rest of the functions remain the same)


// 故事生成API调用
const generateStory = async (text, age, duration, user, gender) => {
  const config = await getConfig()
  const API_URL = config.API_URL
  const STORY_API_KEY = config.STORY_API_KEY
  const url = `${API_URL}/workflows/run`
  const headers = {
    'Authorization': `Bearer ${STORY_API_KEY}`,
    'Content-Type': 'application/json'
  }
  
  const data = {
    user: user,
    workflow_id: 'story_generation',
    inputs: {
      text: text,
      age: age,
      long: String(duration),
      gender:gender
    },
    response_mode: 'streaming'
  }

  try {
    const response = await axios.post(url, data, { headers })
    if (!response.data.startsWith('data:')) {
      throw new Error('API返回格式错误')
    }
    return response.data
  } catch (error) {
    console.error('API调用失败：', error.response?.data || error.message)
    throw new Error('故事生成服务暂时不可用')
  }
}

// 流式响应处理（核心修复部分）
const handleStreamingResponse = async (response, storyId, voiceType, openid) => {
  return new Promise(async (resolve, reject) => {
    let storyContent = ''
    let voiceBuffer = ''
    let isWorkflowFinished = false
    const TIMEOUT = 120000 // 120秒超时，增加超时时间以确保有足够时间完成处理
    let synthesisStage = 0 // 0-初始阶段 1-首次合成后 2-二次合成后

    const timeoutTimer = setTimeout(async () => {
      if (!isWorkflowFinished) {
        console.error('处理超时，已生成内容长度:', storyContent.length)
        await finalizeProcessing(storyContent, voiceBuffer, 'timeout')
        reject(new Error('处理超时'))
      }
    }, TIMEOUT)

    const updateStory = async (content, status) => {
      try {
        const storyFields = parseStoryContent(content);
        
        await db.collection('stories').doc(storyId).update({
          data: {
            storyContent: content,
            title: storyFields.title,
            subtitle: storyFields.subtitle,
            content: storyFields.content,
            question: storyFields.question,
            answer: storyFields.answer,
            type: storyFields.type,
            status: status,
            updateTime: db.serverDate()
          }
        })
      } catch (error) {
        console.error('数据库更新失败:', error)
      }
    }
    
    const parseStoryContent = (content) => {
      const parts = content.split(/◉+/g)
        .map(part => part.trim())
        .filter(part => part.length > 0);
      
      const storyFields = {
        title: '',
        subtitle: '',
        content: '',
        question: '',
        answer: '',
        type: ''
      };
      
      if (parts.length >= 1) storyFields.title = parts[0];
      if (parts.length >= 2) storyFields.subtitle = parts[1];
      if (parts.length >= 3) storyFields.content = parts[2];
      if (parts.length >= 4) storyFields.question = parts[3];
      if (parts.length >= 5) storyFields.answer = parts[4];
      if (parts.length >= 6) storyFields.type = parts[5];
      
      return storyFields;
    }

    const finalizeProcessing = async (content, buffer, status) => {
      clearTimeout(timeoutTimer)
      try {
        if (buffer && buffer.trim()) {
          try {
            await callGenerateVoice(storyId, buffer, voiceType, openid, true);
          } catch (voiceError) {
            console.error('最终语音处理失败，但将继续保存故事内容:', voiceError)
          }
        }

        const transaction = await db.startTransaction()
        try {
          await Promise.all([
            updateStory(content, status),
            updateStoryStatus(storyId, status, '', status === 'completed' ? 'completed' : '')
          ])
          await transaction.commit()
        } catch (error) {
          await transaction.rollback()
          await db.collection('stories').doc(storyId).update({
            data: {
              status: 'failed',
              error: error.message,
              updateTime: db.serverDate()
            }
          })
          throw error
        }
      } catch (error) {
        console.error('最终处理异常:', error)
        try {
          const failedStatus = 'failed'
          await Promise.all([
            updateStory(content, failedStatus),
            updateStoryStatus(storyId, failedStatus, error.message)
          ])
        } catch (updateError) {
          console.error('更新故事状态失败:', updateError)
        }
      }
    }

    try {
      const lines = response.split('\n')
      for (const line of lines) {
        if (!line.startsWith('data:')) continue

        const message = JSON.parse(line.substring(5))

        switch (message.event) {
          case 'text_chunk':
            if (message.data?.text) {
              const textChunk = message.data.text
              storyContent += textChunk
              voiceBuffer += textChunk

              await updateStory(storyContent, 'generating')

              const sentenceCount = (voiceBuffer.match(/[。！？]/g) || []).length
              let shouldSynthesize = false

              if (synthesisStage === 0 && sentenceCount > 3) {
                shouldSynthesize = true
                synthesisStage = 1
                
                try {
                  cloud.callFunction({
                    name: 'generateImage',
                    data: {
                      storyId,
                      title: voiceBuffer,
                      openid
                    }
                    }).then(imageResult => {
                      console.log('早期图片生成请求已发送，返回结果:', JSON.stringify(imageResult))
                    }).catch(imageError => {
                      console.error('早期图片生成请求失败:', imageError)
                    });
                  }
                catch (earlyImageError) {
                  console.error('早期图片生成处理失败:', earlyImageError)
                }
              } else if (synthesisStage === 1 && sentenceCount > 5) {
                shouldSynthesize = true
                synthesisStage = 2
              }

              if (shouldSynthesize) {
                await callGenerateVoice(storyId, voiceBuffer, voiceType, openid)
                voiceBuffer = ''
              }
            }
            break

          case 'workflow_finished':
            isWorkflowFinished = true
            
            if (message.data.status === 'succeeded') {
                try {
                  await finalizeProcessing(storyContent, voiceBuffer, 'completed')
                  resolve(storyContent)
              } catch (error) {
                console.error('最终状态更新失败:', error)
                reject(new Error('故事生成成功但状态更新失败'))
              }
            } else {
              const errorMsg = message.data.error || '工作流执行失败'
              try {
                await finalizeProcessing(storyContent, voiceBuffer, 'failed')
              } catch (updateError) {
                console.error('失败状态更新错误:', updateError)
                try {
                  await Promise.all([
                    updateStory(storyContent, 'failed'),
                    updateStoryStatus(storyId, 'failed', errorMsg)
                  ])
                } catch (directUpdateError) {
                  console.error('直接更新状态也失败:', directUpdateError)
                }
              }
              reject(new Error(errorMsg))
            }
            break
        }
      }
    } catch (error) {
      await finalizeProcessing(storyContent, voiceBuffer, 'failed')
      reject(error)
    }
  })
}

// This helper function is now rewritten to call the speech API directly
const callGenerateVoice = async (storyId, text, voiceType, openid, isfinal = false) => {
  console.log(`Directly generating voice for storyId: ${storyId}, isFinal: ${isfinal}`);
  
  const config = await getConfig();
  const API_KEY = config.STORY_API_KEY; // Assuming the same API key is used
  const SPEECH_API_URL = 'https://api.siliconflow.cn/v1/audio/speech';

  if (!API_KEY) {
    throw new Error('API_KEY is not configured.');
  }

  // 1. Build the request payload
  const babyInfo = (await db.collection('baby_info').where({ _openid: openid }).get()).data[0];
  let voiceValue = '';
  if (voiceType === 'male') {
      voiceValue = 'FunAudioLLM/CosyVoice2-0.5B:benjamin';
  } else if (voiceType === 'female') {
      voiceValue = 'FunAudioLLM/CosyVoice2-0.5B:claire';
  } else if (voiceType === 'dad' && babyInfo && babyInfo.dadUri) {
      voiceValue = babyInfo.dadUri;
  } else if (voiceType === 'mom' && babyInfo && babyInfo.momUri) {
      voiceValue = babyInfo.momUri;
  } else {
      voiceValue = 'FunAudioLLM/CosyVoice2-0.5B:claire'; // Fallback
  }

  const payload = {
    input: text,
    response_format: "mp3",
    stream: false, // We are not streaming to the client, so we get the full audio at once
    voice: voiceValue,
  };

  // 2. Call the Speech API
  const speechResponse = await axios.post(SPEECH_API_URL, payload, {
    headers: {
      'Authorization': `Bearer ${API_KEY}`,
      'Content-Type': 'application/json',
    },
    responseType: 'arraybuffer', // Get the audio data as a buffer
    timeout: 120000, // 120-second timeout for the API call
  });

  if (speechResponse.status !== 200) {
    throw new Error(`Speech API returned status ${speechResponse.status}`);
  }

  // 3. Upload the audio to Cloud Storage
  const audioBuffer = Buffer.from(speechResponse.data);
  const cloudPath = `stories/${storyId}/audio_${Date.now()}.mp3`;
  
  const uploadResult = await cloud.uploadFile({
    cloudPath,
    fileContent: audioBuffer,
  });

  if (!uploadResult.fileID) {
    throw new Error('Failed to upload audio file to cloud storage.');
  }

  // 4. Update the database with the new audio segment
  const db = cloud.database();
  const _ = db.command;

  await db.collection('stories').doc(storyId).update({
    data: {
      audioSegments: _.push([uploadResult.fileID]),
      audioStatus: isfinal ? 'completed' : 'generating',
    }
  });

  console.log(`Successfully generated and saved audio segment: ${uploadResult.fileID}`);
  return { errCode: 0, fileID: uploadResult.fileID };
};

const updateStoryStatus = async (storyId, status, errorMsg = '', audioStatusToSet = '') => {
  try {
    const { data: storyData } = await db.collection('stories').doc(storyId).get()
    
    if (storyData && storyData.status === 'completed' && status === 'failed') {
      return
    }
    
    const audioStatusUpdate = {
      ...(status === 'failed' && storyData.audioStatus !== 'completed' && { audioStatus: 'failed' }),
      ...(audioStatusToSet && { audioStatus: audioStatusToSet })
    }
    
    await db.collection('stories').doc(storyId).update({
      data: {
        status: status,
        ...(errorMsg && { error: errorMsg }),
        ...audioStatusUpdate,
        updateTime: db.serverDate()
      }
    })
  }
  catch (error) {
    console.error('状态更新失败:', error)
  }
}

const updateStoryDuration = async (storyId, duration) => {
  try {
    await db.collection('stories').doc(storyId).update({
      data: {
        duration: duration,
        updateTime: db.serverDate()
      }
    })
  } catch (error) {
    console.error('时长更新失败:', error);
  }
}