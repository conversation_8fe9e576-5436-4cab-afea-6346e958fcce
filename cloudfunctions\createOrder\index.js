// 云函数入口文件
const cloud = require('wx-server-sdk');

cloud.init({ env: cloud.DYNAMIC_CURRENT_ENV });

// 引入微信支付相关依赖
const db = cloud.database();

// 云函数入口函数
exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext();
  const openid = wxContext.OPENID;
  
  // 获取请求参数
  const { amount, duration } = event;
  
  if (!amount || !duration) {
    return {
      code: -1,
      message: '参数错误'
    };
  }
  
  try {
    // 检查orders集合是否存在
    try {
      // 添加limit(1)避免全表扫描，只需要验证集合是否存在和可访问
      await db.collection('orders').limit(1).count();
    } catch (collectionError) {
      console.error('orders集合不存在', collectionError);
      return {
        code: -1,
        message: '订单集合不存在，请在云开发控制台创建orders集合'
      };
    }
    
    // 生成商户订单号，这里使用时间戳+随机数的方式
    const orderId = 'AiStory' + Date.now() + Math.floor(Math.random() * 1000);
    
    // 创建订单记录
    await db.collection('orders').add({
      data: {
        _openid: openid,
        orderId: orderId,
        amount: amount,
        duration: duration,
        status: 'NOTPAY', // 未支付
        createTime: db.serverDate()
      }
    });
    
    // 调用微信支付统一下单API
    console.log('准备调用微信支付统一下单API，商户号:', '1716160007', '用户openid:', openid);
    const res = await cloud.cloudPay.unifiedOrder({
      "body" : "AI故事陪伴会员", // 商品描述
      "outTradeNo" : orderId, // 商户订单号
      "spbillCreateIp" : "127.0.0.1", // 终端IP，这里使用本地IP
      "subMchId" : "1716160007", // 商户号，需要确认是否正确配置
      "tradeType" : "JSAPI", // 交易类型，小程序支付固定为JSAPI
      "totalFee" : amount, // 总金额，单位为分
      "envId": 'cloudbase-1gahfeezccaf7d37', // 使用固定的云环境ID，而不是动态环境
      "functionName": "payCallback", // 支付结果通知回调云函数
      "openid": openid // 用户的openid，JSAPI支付必须提供
    });
    
    // 记录支付请求参数和响应，便于调试
    console.log('支付请求参数:', {
      body: "AI故事陪伴会员",
      outTradeNo: orderId,
      totalFee: amount,
      subMchId: "1716160007",
      tradeType: "JSAPI",
      openid: openid
    });
    console.log('支付接口返回:', res);
    
    // 检查返回结果是否包含payment对象
    if (!res || !res.payment) {
      console.error('支付下单返回结果异常:', res);
      return {
        code: -1,
        message: '创建订单失败: 支付接口返回数据异常',
        orderId: orderId
      };
    }
    
    // 检查返回结果是否包含错误信息
    if (res.resultCode === 'FAIL') {
      console.error('支付下单失败，错误码:', res.errCode, '错误描述:', res.errCodeDes);
      
      // 根据错误描述提供更具体的解决方案
      let errorDetail = '';
      if (res.errCodeDes === '受理关系不存在') {
        errorDetail = `微信支付错误: 受理关系不存在，请按照以下步骤解决：\n\n1. 确认商户号配置：\n   - 确认商户号1716160007是否正确\n   - 确认商户号已完成微信支付的开通和配置\n\n2. 配置受理关系：\n   - 登录微信支付商户平台(pay.weixin.qq.com)\n   - 进入【产品中心】-【JSAPI支付】-【接入前准备】\n   - 在【关联小程序APPID】中添加当前小程序的APPID\n   - 确保小程序APPID与商户号1716160007已建立了支付授权关系\n\n3. 检查云函数配置：\n   - 确认云函数有正确的支付权限\n   - 确认云环境ID配置正确\n\n4. 检查小程序配置：\n   - 在小程序管理后台确认已添加微信支付的相关域名到安全域名列表`;
      } else {
        errorDetail = `微信支付错误: ${res.errCodeDes || '未知错误'}，请确认商户号配置是否正确，以及是否已完成微信支付的商户配置和授权`;
      }
      
      // 记录更详细的错误信息，便于排查
      console.log('支付错误详情:', {
        errorCode: res.errCode,
        errorDesc: res.errCodeDes,
        orderId: orderId,
        openid: openid,
        subMchId: "1716160007"
      });
      
      return {
        code: -1,
        message: `创建订单失败: ${res.errCodeDes || '支付参数异常，请检查商户配置'}`,
        orderId: orderId,
        detail: errorDetail
      };
    }
    
    // 检查package字段是否为空或不完整
    if (!res.payment.package || res.payment.package === 'prepay_id=' || !res.payment.package.includes('prepay_id=')) {
      console.error('支付下单返回的package字段异常:', res.payment.package);
      
      // 记录更详细的错误信息，便于排查
      console.log('支付package字段异常详情:', {
        package: res.payment.package,
        orderId: orderId,
        openid: openid,
        subMchId: "1716160007",
        returnCode: res.returnCode,
        resultCode: res.resultCode,
        errCode: res.errCode,
        errCodeDes: res.errCodeDes || '无错误描述'
      });
      
      return {
        code: -1,
        message: '创建订单失败: 支付参数异常，请检查商户配置',
        orderId: orderId,
        detail: '微信支付接口返回的prepay_id为空，这通常是由于受理关系不存在导致的。请按照以下步骤解决：\n\n1. 确认商户号配置：\n   - 确认商户号1716160007是否正确\n   - 确认商户号已完成微信支付的开通和配置\n\n2. 配置受理关系：\n   - 登录微信支付商户平台(pay.weixin.qq.com)\n   - 进入【产品中心】-【JSAPI支付】-【接入前准备】\n   - 在【关联小程序APPID】中添加当前小程序的APPID\n   - 确保小程序APPID与商户号1716160007已建立了支付授权关系\n\n3. 检查云函数配置：\n   - 确认云函数有正确的支付权限\n   - 确认云环境ID配置正确\n\n4. 检查小程序配置：\n   - 在小程序管理后台确认已添加微信支付的相关域名到安全域名列表'
      };
    }
    
    return {
      code: 0,
      message: '创建订单成功',
      payment: {
        timeStamp: res.payment.timeStamp,
        nonceStr: res.payment.nonceStr,
        package: res.payment.package,
        signType: res.payment.signType,
        paySign: res.payment.paySign
      },
      orderId: orderId
    };
  } catch (error) {
    console.error('创建订单失败', error);
    // 添加更详细的错误信息记录
    console.log('错误详情:', JSON.stringify(error));
    
    // 记录错误上下文信息，便于排查
    console.log('错误上下文信息:', {
      openid: openid,
      amount: amount,
      duration: duration,
      orderId: orderId || '未生成',
      errorName: error.name,
      errorMessage: error.message,
      errorStack: error.stack
    });
    
    // 提供更具体的错误信息和解决建议
    let errorMessage = '创建订单失败: ' + error.message;
    let errorDetail = '请按照以下步骤解决微信支付问题：\n\n';
    errorDetail += '1. 确认商户号配置：\n';
    errorDetail += '   - 确认商户号1716160007是否正确\n';
    errorDetail += '   - 确认商户号已完成微信支付的开通和配置\n\n';
    errorDetail += '2. 配置受理关系：\n';
    errorDetail += '   - 登录微信支付商户平台(pay.weixin.qq.com)\n';
    errorDetail += '   - 进入【产品中心】-【JSAPI支付】-【接入前准备】\n';
    errorDetail += '   - 在【关联小程序APPID】中添加当前小程序的APPID\n';
    errorDetail += '   - 确保小程序APPID与商户号1716160007已建立了支付授权关系\n\n';
    errorDetail += '3. 检查云函数配置：\n';
    errorDetail += '   - 确认云函数有正确的支付权限\n';
    errorDetail += '   - 确认云环境ID配置正确\n\n';
    errorDetail += '4. 检查小程序配置：\n';
    errorDetail += '   - 在小程序管理后台确认已添加微信支付的相关域名到安全域名列表\n';
    errorDetail += '   - 确认小程序已经通过微信认证（个人小程序无法使用微信支付）';
    
    return {
      code: -1,
      message: errorMessage,
      detail: errorDetail,
      error: error
    };
  }
};