// 云函数：从Base64数据上传音频文件
const cloud = require('wx-server-sdk');

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();

exports.main = async (event, context) => {
  const { storyId, voiceType, audioBase64, filename } = event;
  
  try {
    console.log(`开始处理音频上传: ${filename}, 大小: ${audioBase64.length} chars`);
    
    // 将Base64转换为Buffer
    const audioBuffer = Buffer.from(audioBase64, 'base64');
    console.log(`音频Buffer大小: ${audioBuffer.length} bytes`);
    
    // 生成云存储路径
    const cloudPath = `stories/${storyId}/${filename}`;
    
    // 上传到云存储
    const uploadResult = await cloud.uploadFile({
      cloudPath: cloudPath,
      fileContent: audioBuffer,
    });
    
    if (uploadResult.fileID) {
      console.log(`音频上传成功: ${uploadResult.fileID}`);
      
      return {
        success: true,
        fileID: uploadResult.fileID,
        cloudPath: cloudPath,
        size: audioBuffer.length
      };
    } else {
      console.error('上传失败，未返回fileID');
      return {
        success: false,
        error: '上传失败'
      };
    }
    
  } catch (error) {
    console.error('云函数上传音频失败:', error);
    return {
      success: false,
      error: error.message || '上传失败'
    };
  }
};
