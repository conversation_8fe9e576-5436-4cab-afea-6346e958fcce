/* pages/home/<USER>/

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 100%;
  height: auto; /* 或者设置为具体的高度，如果需要的话 */
  z-index: 999; /* 确保在其他内容之上 */
}

.loading-icon {
  width: 80rpx;
  height: 80rpx;
  animation: rotate 1s linear infinite;
}

.loading-text {
  margin-top: 20rpx;
  font-size: 28rpx;
  color: #999;
}

/* 确保旋转动画已定义，如果之前没有的话 */
@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}
page {
  width: 100%;
  min-height: 100vh;
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center;
  background-attachment: scroll;
}

.custom-nav-bar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    height: 180rpx;
    background-color: rgba(48, 47, 51, 0.4);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 999;
    backdrop-filter: blur(10px);
    padding-bottom: 10rpx;
}
.custom-nav-bar::after {
    content: "";
    position: absolute;
    left: 0;
    right: 0;
    bottom: 0;
    top: 0;
    background: rgba(48, 47, 51, 0.4);
    z-index: -1;
    border-radius: 0 0 32rpx 32rpx;
    pointer-events: none;
}

.vip-image {
  width: 50rpx;
  height: 40rpx;
  overflow: hidden;
  margin-left:30rpx;
}

.avatar-wrapper {
  width: 76rpx;
  height: 76rpx;
  border-radius:50%;
  overflow: hidden;
  margin-left:36rpx;
}

.avatar-wrapper image {
  width: 100%;
  height: 100%;
}

.nav-title {
    color:rgb(200, 200, 241);
    font-size: 36rpx;
    flex-grow: 1;
    text-align: center;
    margin-right:196rpx;
}
.container {
  padding: 160rpx 20rpx 20rpx;
  background-color: #f5f5f5;
  background-image: url('https://636c-cloudbase-1gahfeezccaf7d37-1354986900.tcb.qcloud.la/backgroud.png');
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center;
  min-height: 100vh;
  min-height: 100vh;
  height: 100%;
}

.baby-info {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
  padding: 20rpx;
  background-color: #fff;
  border-radius: 16rpx;
}

.baby-avatar {
  width: 70rpx;
  height: 70rpx;
  border-radius: 50%;
  margin-right: 20rpx;
}

.baby-name {
  font-size: 32rpx;
  color: #333;
  font-weight: bold;
}
.recommend-title {
  margin-top:20px;
  padding-left: 10rpx;
  padding-bottom:10rpx;
  margin-bottom: 4rpx;
}

.recommend-swiper {
  height: 420rpx;
  border-radius: 32rpx;
}

.recommend-swiper-item {
  padding: 0 10rpx;
  box-sizing: border-box;
}

.recommend-card {
  border-radius: 20rpx;
  padding:20rpx;
  box-shadow: 0 8rpx 20rpx rgba(231, 229, 162, 0.2);
  background: linear-gradient(135deg, rgba(80, 59, 115, 0.95), rgba(45, 15, 80, 0.9));
  backdrop-filter: blur(10px);
  transition: transform 0.4s ease, box-shadow 0.4s ease;
  overflow: hidden;
  position: relative;
  margin-bottom:30rpx;
}

.recommend-swiper-item {
  padding: 0 10rpx;
  box-sizing: border-box;
  overflow: hidden;
  border-radius: 32rpx;
}

.recommend-card:hover {
  transform: translateY(-4rpx);
  box-shadow: 0 12rpx 24rpx rgba(0, 0, 0, 0.25);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8rpx;
}

.title-container {
  display: flex;
  align-items: center;
  gap: 10rpx;
}

.card-title {
  font-size: 32rpx;
  color:rgb(212, 188, 250);
}

.vip-btn {
  border-radius: 20px;
  background-color:rgb(222, 110, 19);
  padding: 6px 16px;
  margin: 2px 10rpx 20rpx 10rpx;
  display: flex;
  align-items: center;
  color: rgb(252, 252, 252);
  font-size: 28rpx;
}


.refresh-btn {
  border-radius: 20px;
  padding: 4px 8px;
  display: flex;
  align-items: center;
  color: rgb(218, 102, 8);
  font-size: 28rpx;
}

.refresh-btn image {
  width: 38rpx;
  height: 38rpx;
  margin-right: 4rpx;
}

.refresh-btn.loading {
  color: #999;
}

.refresh-btn.loading image {
  animation: rotate 1s linear infinite;
  transform-origin: center center;
  will-change: transform;
}

/* 添加旋转动画关键帧 */
@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.loading image {
  animation: rotate 1s linear infinite;
  transform-origin: center center;
  will-change: transform;
  width: 40rpx;
  height: 40rpx;
}

.generating {
  font-size: 28rpx;
  color: #999;
}

.story-title {
  font-size: 35rpx;
  color:rgb(252, 252, 252);
  margin-bottom: 30rpx;
  text-align: center;
  display: block;
}

.story-content {
  margin-top:10rpx;
  display: flex;
  gap: 20rpx;
  width: 100%;
  margin-bottom:10rpx;
}

.story-image {
  width: 300rpx;
  height: 300rpx;
  border-radius: 16rpx;
  flex-shrink: 0;
}

.content-right {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  padding: 0 10rpx;
  max-width: calc(100% - 320rpx);
  box-sizing: border-box;
}

.story-preview {
  font-size: 28rpx;
  color: rgba(177, 177, 177, 0.9);
  line-height: 1.6;
  display: block;
  margin-bottom: 20rpx;
  width: 100%;
  box-sizing: border-box;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
}

.listen-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(255, 255, 255, 0.3);
  color: #fff;
  border-radius: 40rpx;
  padding: 12rpx 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  width: fit-content;
  max-width: 100%;
  backdrop-filter: blur(10px);
  font-size: 30rpx;
  font-weight: normal;
}

.listen-btn.playing {
  background-color: #E8F5E9;
}

.listen-btn image {
  width: 36rpx;
  height: 36rpx;
  margin-right: 12rpx;
}


.section-title {
  font-size: 32rpx;
  color:rgb(62, 203, 242);
  margin-bottom: 20rpx;
  padding: 0 10rpx;
}

.section {
  position: sticky;
  top: 170rpx;
  z-index: 999;
  background: rgba(103, 102, 103, 0.2);
  backdrop-filter: blur(10px);
  border-radius: 16rpx;
  height:80rpx;
  display: flex;
  align-items: center;
}

.category-grid {
  margin-top: 30rpx;
  white-space: nowrap;
  border-radius: 32rpx;
  overflow: hidden;
  display: flex;
  align-items: center;
  height: 80rpx;
}

/* 隐藏WebKit浏览器的滚动条 */
.category-grid::-webkit-scrollbar {
  display: none;
  width: 0;
  height: 0;
}

.category-class.active {
  background: linear-gradient(135deg, rgba(80, 59, 115, 0.95), rgba(201, 79, 169, 0.9));
  color: rgb(59, 10, 82);
  box-shadow: 0 4rpx 12rpx rgba(219, 112, 130, 0.2);
  padding: 10rpx 25rpx;
}

.category-item text {
  font-size: 28rpx;
  color: #fff;
  display: block;
  padding:20rpx;
}

.category-class {
  display: inline-block;
  padding: 10rpx 25rpx;
  background-color:rgb(32, 6, 49);
  border-radius: 30rpx;
  font-size: 28rpx;
  color: #666;
  text-align: center;
  transition: all 0.3s ease;
  margin: 0 10rpx;
}

.category-class:active {
  transform: scale(0.95);
  background: linear-gradient(135deg, rgba(225, 79, 108, 0.8), rgba(109, 85, 214, 0.9));
}


.story-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap:16rpx;
  padding: 6rpx;
  padding-bottom: 150rpx;
  box-sizing: border-box;
}

.story-card {
  background-color:rgb(31, 15, 57);
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
}

.story-list-image {
  width: calc(100% - 20rpx);
  height: calc(100% - 20rpx);
  aspect-ratio: 1;
  object-fit: cover;
  margin: 10rpx;
  border-radius: 10rpx;
}

.story-info {
  padding: 10rpx 16rpx 20rpx 16rpx;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.story-list-title {
  font-size: 30rpx;
  color:rgb(255, 247, 247);
  margin-bottom: 8rpx;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-align: center;
}

.story-subtitle {
  font-size: 22rpx;
  color:rgb(176, 174, 174);
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-align: center;
}

.category-class text {
  font-size: 28rpx;
  color: #fff;
  display: block;
}

.custom-story {
  margin-top: 40rpx;
  padding: 0 20rpx;
}

.custom-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #ff6b1a, #e45503);
  color: #fff;
  border-radius: 32rpx;
  padding: 24rpx;
  border: none;
  width: 100%;
  box-shadow: 0 4rpx 12rpx rgba(228, 85, 3, 0.3);
  transition: all 0.3s ease
}

.custom-btn image {
  width: 40rpx;
  height: 40rpx;
  margin-right: 12rpx;
}

.custom-btn text {
  font-size: 30rpx;
}

.AI-btn {
  position: fixed;
  bottom: 20rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 140rpx;
  height: 140rpx;
  background-size: cover;
  background-position: center;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
  z-index: 1001;
  margin-bottom:2rpx;
}

.AI-btn:active {
  transform: translateX(-50%) scale(0.95);
  background: rgb(198, 181, 35);
}

.AI-btn image {
  width: 140rpx;
  height: 140rpx;
  border-radius:130rpx;
}

.tab-home {
  background: radial-gradient(rgba(254, 245, 163, 1), rgba(222, 209, 85, 0.2));
  position: fixed;
  bottom: 20rpx;
  left: 22%;
  transform: translateX(-50%);
  width: 135rpx;
  height: 135rpx;
  background-size: cover;
  background-position: center;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
  z-index: 1001;
  margin-bottom:2rpx;
}

.tab-home:active {
  transform: translateX(-50%) scale(0.95);
  background: rgb(221, 180, 58);
}

.tab-home image {
  width: 110rpx;
  height: 110rpx;
  border-radius:80rpx;
}

.tab-history-inactive {
  position: fixed;
  bottom: 20rpx;
  left: 78%;
  transform: translateX(-50%);
  width: 130rpx;
  height: 130rpx;
  background-size: cover;
  background-position: center;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
  z-index: 1001;
  margin-bottom:2rpx;
}
.tab-history-inactive:active {
  transform: translateX(-50%) scale(0.95);
  background: rgb(221, 180, 58);
}

.tab-history-inactive::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(105, 104, 104, 0.3); /* 透明黑色蒙版，数值可调整 */
  border-radius: 50%;
  z-index: 0; /* 确保覆盖图标 */
}

/* 添加CSS过渡效果 */
.movable-view {
  transition: transform 0.3s cubic-bezier(0.1, 0.57, 0.1, 1);
}
.delete-btn {
  transition: transform 0.3s cubic-bezier(0.1, 0.57, 0.1, 1);
}
/* 优化movable-area样式 */
.movable-container {
  width: calc(100% + 120rpx);
  height: 100%;
  position: relative;
  left: -120rpx;
}

.tab-history-inactive image {
  width: 100rpx;
  height: 100rpx;
  border-radius:80rpx;
}

/* 年龄选择弹窗样式 */
.age-selector-fullscreen {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  z-index: 1002;
  box-sizing: border-box;
}

.features-section {
  background: rgb(252, 249, 249);
  padding: 20rpx;
  padding-top: 0; /* Resetting top padding as it's now inline */
  flex: 1;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch; /* For momentum scrolling on iOS */
}

.custom-nav-bar-feature {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    background-color: rgb(252, 249, 249);
    z-index: 1003;
}

.nav-bar-content-feature {
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
}

.features-title {
  font-size: 36rpx; /* 增大标题字号 */
  font-weight: bold;
  color: rgb(224, 146, 37);
  text-align: center;
  flex: 1;
}

.age-selection-section {
  background: linear-gradient(135deg, rgb(55, 40, 85), rgb(30, 10, 55));
  padding: 40rpx;
  flex-shrink: 0;
}

.features-title {
  font-size: 36rpx; /* 增大标题字号 */
  font-weight: bold;
  color: rgb(224, 146, 37);
  margin-bottom: 40rpx;
  text-align: center;
  flex-shrink: 0; /* 防止标题被压缩 */
}

.age-selector-modal {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  color: #fff;
  overflow: hidden; /* 隐藏溢出 */
  flex: 1; /* 占据剩余空间 */
}

.benefits-list {
  margin-bottom: 40rpx;
  text-align: left;
  width: 100%;
  padding: 0 20rpx;
  box-sizing: border-box;
}

/* 隐藏滚动条 */
.benefits-list::-webkit-scrollbar {
  display: none;
  width: 0;
  height: 0;
}

.benefit-item {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx; /* 增加列表项间距 */
}

.benefit-icon {
  width: 160rpx;
  height: 160rpx;
  margin-right: 20rpx;
  flex-shrink: 0;
  border-radius: 20rpx;
}

.benefit-content {
  display: flex;
  flex-direction: column;
}

.benefit-name {
  font-size: 32rpx; /* 调整字体大小 */
  font-weight: bold;
  color: rgb(224, 146, 37);
}

.benefit-desc {
  font-size: 26rpx; /* 调整字体大小 */
  color: rgb(113, 99, 66);
  line-height: 1.5; /* 增加行高 */
}

.age-selection-container {
  width: 100%;
  flex-shrink: 0; /* 防止此部分被压缩 */
}

.modal-title {
  font-size: 36rpx;
  font-weight: bold;
  color: rgb(212, 188, 250);
  margin-bottom: 16rpx;
  text-align: center;
}

.modal-subtitle {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.7);
  margin-bottom: 40rpx;
  text-align: center;
}

.age-options {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20rpx;
  margin-bottom: 40rpx;
}

.age-option {
  background-color: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  padding: 20rpx 5rpx;
  border-radius: 16rpx;
  font-size: 26rpx;
  color: #fff;
  transition: all 0.3s ease;
  text-align: center;
}

.age-option.selected {
  background-color: rgba(212, 188, 250, 0.8);
  border-color: rgb(212, 188, 250);
  color: rgb(45, 15, 80);
  font-weight: bold;
}

.confirm-btn {
  background: linear-gradient(135deg, rgba(225, 79, 108, 0.8), rgba(109, 85, 214, 0.9));
  color: #fff;
  border-radius: 40rpx;
  padding: 20rpx;
  font-size: 32rpx;
  border: none;
  width: 100%;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.2);
}
.login-link {
  display: block;
  margin-top:30rpx;
  font-size:32rpx;
  color: rgba(255, 255, 255, 0.6);
  text-decoration: underline;
  text-align: center;
}

