// 权限管理模块
const db = wx.cloud.database();
const _ = db.command;

// 权限类型常量
const PERMISSION_TYPES = {
  LISTEN_STORY: 'listen_story',       // 听故事（今日推荐、分类故事）
  GENERATE_STORY: 'generate_story',   // 生成故事
  FAVORITE: 'favorite',               // 收藏功能
  LISTEN_HISTORY: 'listen_history',   // 听收藏/历史故事
  VIEW_ANSWER: 'view_answer'          // 查看答案
};

// 非会员限制
const FREE_LIMITS = {
  [PERMISSION_TYPES.LISTEN_STORY]: 2,     // 每天只能听2个故事
  [PERMISSION_TYPES.GENERATE_STORY]: 1,   // 每天只能生成1次故事
  [PERMISSION_TYPES.FAVORITE]: 3,         // 最多只能收藏3个
  [PERMISSION_TYPES.VIEW_ANSWER]: 1       // 总共只能查看1次答案
};

// 检查用户是否为会员
const checkIsVip = async () => {
  try {
    const openid = wx.getStorageSync('openid');
    if (!openid) return false;

    const res = await db.collection('users').where({
      _openid: openid
    }).get();

    if (res.data.length === 0) return false;

    const user = res.data[0];
    if (!user.vipExpireDate) return false;

    const currentDate = new Date();
    const expireDate = new Date(user.vipExpireDate);
    
    // 将当前日期和到期日期都设置为当天的0点，只比较日期而不比较具体时间
    currentDate.setHours(0, 0, 0, 0);
    expireDate.setHours(0, 0, 0, 0);
    
    return expireDate >= currentDate;
  } catch (err) {
    console.error('检查会员状态失败', err);
    return false;
  }
};

// 检查并创建集合
const ensureCollection = async (collectionName) => {
  try {
    // 尝试查询集合，如果不存在会抛出异常
    await db.collection(collectionName).limit(1).get();
    return true;
  } catch (err) {
    // 如果是集合不存在的错误
    if (err.errCode === -502005 || (err.errMsg && err.errMsg.includes('collection not exists'))) {
      console.log(`集合 ${collectionName} 不存在，尝试创建...`);
      try {
        // 调用云函数创建集合
        const result = await wx.cloud.callFunction({
          name: 'createCollection',
          data: { collectionName }
        });
        console.log(`创建集合结果:`, result);
        return true;
      } catch (createErr) {
        console.error(`创建集合 ${collectionName} 失败`, createErr);
        return false;
      }
    }
    console.error(`检查集合 ${collectionName} 失败`, err);
    return false;
  }
};

// 获取用户使用记录
const getUserUsageRecord = async () => {
  try {
    const openid = wx.getStorageSync('openid');
    if (!openid) {
      throw new Error('未获取到用户openid');
    }

    // 确保集合存在
    const collectionExists = await ensureCollection('userUsage');
    if (!collectionExists) {
      // 如果集合不存在且无法创建，返回默认记录
      console.log('userUsage集合不存在或无法创建，使用默认记录');
      const today = new Date().toISOString().split('T')[0];
      return {
        _id: 'default',
        _openid: openid,
        [PERMISSION_TYPES.LISTEN_STORY]: { [today]: 0 },
        [PERMISSION_TYPES.GENERATE_STORY]: { [today]: 0 },
        [PERMISSION_TYPES.LISTEN_HISTORY]: { [today]: 0 },
        [PERMISSION_TYPES.FAVORITE]: 0,
        [PERMISSION_TYPES.VIEW_ANSWER]: 0
      };
    }

    // 检查是否存在使用记录
    try {
      const res = await db.collection('userUsage').where({
        _openid: openid
      }).get();

      if (res.data.length === 0) {
        // 创建新的使用记录
        const today = new Date().toISOString().split('T')[0];
        const newRecord = {
          // 各种权限的使用次数，按日期记录
          [PERMISSION_TYPES.LISTEN_STORY]: { [today]: 0 },
          [PERMISSION_TYPES.GENERATE_STORY]: { [today]: 0 },
          [PERMISSION_TYPES.LISTEN_HISTORY]: { [today]: 0 },
          // 收藏的故事数量
          [PERMISSION_TYPES.FAVORITE]: 0,
          // 查看答案的总次数
          [PERMISSION_TYPES.VIEW_ANSWER]: 0,
          createTime: db.serverDate(),
          updateTime: db.serverDate()
        };

        try {
          const addRes = await db.collection('userUsage').add({
            data: newRecord
          });
          newRecord._id = addRes._id;
          return newRecord;
        } catch (addErr) {
          console.error('创建使用记录失败', addErr);
          // 创建失败时返回默认记录
          newRecord._id = 'default';
          return newRecord;
        }
      }

      return res.data[0];
    } catch (queryErr) {
      console.error('查询使用记录失败', queryErr);
      // 查询失败时返回默认记录
      const today = new Date().toISOString().split('T')[0];
      return {
        _id: 'default',
        _openid: openid,
        [PERMISSION_TYPES.LISTEN_STORY]: { [today]: 0 },
        [PERMISSION_TYPES.GENERATE_STORY]: { [today]: 0 },
        [PERMISSION_TYPES.LISTEN_HISTORY]: { [today]: 0 },
        [PERMISSION_TYPES.FAVORITE]: 0,
        [PERMISSION_TYPES.VIEW_ANSWER]: 0
      };
    }
  } catch (err) {
    console.error('获取使用记录失败', err);
    // 出现任何错误时返回默认记录
    const today = new Date().toISOString().split('T')[0];
    return {
      _id: 'default',
      _openid: wx.getStorageSync('openid') || 'unknown',
      [PERMISSION_TYPES.LISTEN_STORY]: { [today]: 0 },
      [PERMISSION_TYPES.GENERATE_STORY]: { [today]: 0 },
      [PERMISSION_TYPES.LISTEN_HISTORY]: { [today]: 0 },
      [PERMISSION_TYPES.FAVORITE]: 0,
      [PERMISSION_TYPES.VIEW_ANSWER]: 0
    };
  }
};

// 更新使用记录
const updateUsageRecord = async (type, increment = true) => {
  try {
    const record = await getUserUsageRecord();
    const today = new Date().toISOString().split('T')[0];
    const updateData = {};

    switch (type) {
      case PERMISSION_TYPES.LISTEN_STORY:
      case PERMISSION_TYPES.GENERATE_STORY:
      case PERMISSION_TYPES.LISTEN_HISTORY:
        // 按日期记录使用次数
        const currentCount = (record[type] && record[type][today]) || 0;
        updateData[`${type}.${today}`] = increment ? currentCount + 1 : currentCount;
        break;
      case PERMISSION_TYPES.FAVORITE:
      case PERMISSION_TYPES.VIEW_ANSWER:
        // 直接记录总次数
        if (typeof record[type] !== 'number') {
          updateData[type] = increment ? 1 : 0;
        } else {
          updateData[type] = increment ? record[type] + 1 : record[type];
        }
        break;
      default:
        throw new Error('无效的权限类型');
    }

    updateData.updateTime = new Date();

    // 如果是默认记录，不进行实际更新，直接返回成功
    if (record._id === 'default') {
      console.log('使用默认记录，跳过数据库更新');
      return true;
    }

    try {
      await db.collection('userUsage').doc(record._id).update({
        data: updateData
      });
      return true;
    } catch (updateErr) {
      console.error('更新使用记录失败', updateErr);
      // 更新失败时不抛出异常，返回成功
      console.log('更新失败但继续执行，避免影响用户体验');
      return true;
    }
  } catch (err) {
    console.error('更新使用记录失败', err);
    // 出现任何错误时不抛出异常，返回成功
    return true;
  }
};

// 检查权限
const checkPermission = async (type) => {
  try {
    // 使用Object.keys().map()替代Object.values()以提高兼容性
    const permissionValues = Object.keys(PERMISSION_TYPES).map(key => PERMISSION_TYPES[key]);
    if (!permissionValues.includes(type)) {
      console.warn('无效的权限类型:', type);
      // 无效权限类型时默认允许访问
      return { hasPermission: true };
    }

    // 检查是否为会员
    const isVip = await checkIsVip();
    if (isVip) return { hasPermission: true };

    // 非会员，检查使用次数
    const record = await getUserUsageRecord();
    const today = new Date().toISOString().split('T')[0];

    let message = '';
    let hasPermission = true;

    switch (type) {
      case PERMISSION_TYPES.LISTEN_STORY:
      case PERMISSION_TYPES.GENERATE_STORY:
      case PERMISSION_TYPES.LISTEN_HISTORY:
        // 按日期检查使用次数
        const todayCount = (record[type] && record[type][today]) || 0;
        if (todayCount >= FREE_LIMITS[type]) {
          hasPermission = false;
          const actionType = {
            [PERMISSION_TYPES.LISTEN_STORY]: '听',
            [PERMISSION_TYPES.GENERATE_STORY]: '生成',
            [PERMISSION_TYPES.LISTEN_HISTORY]: '听'
          }[type];
          message = `非会员每天只能${actionType}${FREE_LIMITS[type]}个故事，开通会员即可无限畅听！`;
        }
        break;
      case PERMISSION_TYPES.FAVORITE:
        // 检查收藏总数
        if (record[type] >= FREE_LIMITS[type]) {
          hasPermission = false;
          message = `非会员最多只能收藏${FREE_LIMITS[type]}个故事，开通会员享受无限收藏特权！`;
        }
        break;
      case PERMISSION_TYPES.VIEW_ANSWER:
        // 检查查看答案总次数
        if (record[type] >= FREE_LIMITS[type]) {
          hasPermission = false;
          message = `非会员只能查看${FREE_LIMITS[type]}次答案，开通会员解锁所有答案！`;
        }
        break;
    }

    return { hasPermission, message };
  } catch (err) {
    console.error('检查权限失败', err);
    // 出现任何错误时默认允许访问，避免因为数据库问题而阻止用户使用应用
    return { 
      hasPermission: true,
      message: '权限检查暂时不可用，已临时授权访问'
    };
  }
};

// 显示会员提示弹窗
const showVipModal = (message) => {
  wx.showModal({
    title: '开通会员',
    content: message || '开通会员，享受更多权益',
    confirmText: '立即开通',
    success: (res) => {
      if (res.confirm) {
        wx.navigateTo({
          url: '/pages/vip/vip'
        });
      }
    }
  });
};

// 导出模块
module.exports = {
  PERMISSION_TYPES,
  FREE_LIMITS,
  checkIsVip,
  checkPermission,
  updateUsageRecord,
  showVipModal
};