.story-container {
  min-height: 100vh;
  background-size: cover;
  background-repeat: no-repeat;
  background-image: url('https://636c-cloudbase-1gahfeezccaf7d37-1354986900.tcb.qcloud.la/backgroud.png');
  background-position: center;
  /* 移除background-attachment属性 */
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  padding: 1rpx 10rpx 300rpx;
  position: relative;
}

.story-title {
  color: rgb(210, 188, 248);
  font-size: 42rpx;
  text-align: left;
  font-weight: bold;
}

.story-subtitle {
  color: rgb(159, 157, 157);
  font-size: 33rpx;
  line-height: 1.6;
  display: block;
  margin-bottom:20rpx;
}

/* 故事配图样式 */
.story-image-container {
  width: 100%;
  margin: 20rpx 0 30rpx;
  border-radius: 32rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.3);
}

.story-image {
  width: 100%;
  display: block;
}

/* 默认图片容器样式 */
.default-container-style {
  width: 50%;
  height: 300rpx;
  margin: 20rpx auto;
  overflow: hidden;
  position: relative;
}

.default-image-style {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.default-container-style::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(128, 128, 128, 0.5);
  animation: compressEffect 2s ease-in-out infinite;
}

@keyframes compressEffect {
  0% {
    transform: scaleY(0);
    transform-origin: top;
  }
  50% {
    transform: scaleY(1);
    transform-origin: top;
  }
  51% {
    transform: scaleY(1);
    transform-origin: bottom;
  }
  100% {
    transform: scaleY(0);
    transform-origin: bottom;
  }
}

.story-content {
  flex: 1;
  background: rgba(25, 1, 42, 0.45);
  border-radius: 20rpx;
  padding: 30rpx;
  margin: 60rpx auto 40rpx;
  width: 90%;
}

.story-body {
  color: rgb(202, 201, 201);
  font-size: 36rpx;
  line-height: 1.6;
  display: block;
  margin-bottom: 40rpx;
}

/* 问题部分样式 */
.story-question {
  margin-top: 30rpx;
  padding: 20rpx;
  background: rgba(50, 10, 80, 0.6);
  border-radius: 16rpx;
  border-left: 8rpx solid rgb(210, 188, 248);
}

.question-title {
  color: rgb(210, 188, 248);
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.question-text {
  color: rgb(230, 230, 230);
  font-size: 32rpx;
  line-height: 1.5;
  display: block;
}

.player-bar {
  background: rgba(255, 255, 255, 0.15);
  border-radius: 20rpx 20rpx 0 0;
  padding: 10rpx;
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  backdrop-filter: blur(10px);
  z-index: 100;
  min-height: 180rpx; /* 确保有最小高度 */
}

.progress-bar {
  width: 100%;
  margin-bottom: 10rpx;
}

.audio-generating-hint {
  color: #ffffff;
  font-size: 28rpx;
  text-align: center;
  padding: 20rpx 0;
  opacity: 0.9;
}

.time-info {
  display: flex;
  justify-content: space-between;
  color: #ffffff;
  font-size: 24rpx;
  margin-left: 40rpx;
  margin-top: -10rpx;
  margin-right: 40rpx;
}

.control-buttons {
  position: relative;
  display: flex;
  justify-content: space-around;
  align-items: center;
  padding: 10px 0;
  width: 100%;
  z-index: 1;
  display: flex;
  justify-content: space-around;
  align-items: center;
  margin-top: 20rpx;
}

.speed-btn {
  width: 32px;
  height: 32px;
}

.speed-setting-popup {
  position: absolute;
  bottom: 100%;
  left: 0;
  right: 0;
  background-color: rgba(34, 6, 57, 0.95);
  border-top-left-radius: 16px;
  border-top-right-radius: 16px;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.3);
  padding: 10px;
  z-index: 2;
}

.speed-options {
  display: flex;
  justify-content: space-around;
  align-items: center;
}

.speed-option {
  padding: 15rpx 36rpx;
  border-radius: 28px;
  font-size: 28rpx;
  color: rgb(210, 188, 248);
  background-color: rgba(122, 65, 166, 0.6);
  transition: all 0.3s ease;
}

.speed-option.active {
  color: rgb(83, 34, 120);
  background-color: rgb(197, 183, 223);
}


.back-btn, .play-btn,.like-btn, .answer-btn {
  width: 70rpx;
  height: 70rpx;
}

.play-btn {
  width: 110rpx;
  height: 110rpx;
}

/* 补充loading动画样式 */
.loading-hint {
  color: #ffffff;
  font-size: 38rpx;
  text-align: center;
  marging-top:136rpx;
  padding: 40rpx 0;
  opacity: 0.8;
}

.loading-icon {
  transform-origin: center center;
  animation: rotate 1s linear infinite;
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  50% { transform: rotate(180deg); }
  to { transform: rotate(360deg); }
}

.loading-hint {
  color: #ffffff;
  font-size: 32rpx;
  text-align: center;
  padding: 40rpx 0;
  opacity: 0.8;
}

.loading-icon {
  width: 110rpx;
  height: 110rpx;
  animation: rotate 1s linear infinite;
}