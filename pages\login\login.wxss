.container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  padding: 40rpx;
  height: 100vh;
  background-image: url('https://636c-cloudbase-1gahfeezccaf7d37-1354986900.tcb.qcloud.la/backgroud.png');
  background-size: 100% 100%;
  background-attachment: fixed;
  background-position: center;
  background-repeat: no-repeat;
}

.logo {
  width: 360rpx;
  height: 360rpx;
  margin-top: 180rpx;
  border-radius: 44rpx;
}

.title {
  font-size: 42rpx;
  font-weight: bold;
  color:rgb(225, 218, 218);
  margin-bottom: 20rpx;
  margin-top:30rpx;
}

.subtitle {
  font-size: 30rpx;
  color:rgb(186, 186, 186);
  margin-bottom: 60rpx;
}

.login-btn {
  width: 80%;
  height: 88rpx;
  line-height: 88rpx;
  background: #4CAF50;
  color: #ffffff;
  border-radius: 44rpx;
  font-size: 32rpx;
  font-weight: 500;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
}

.login-btn::after {
  border: none;
}

.login-btn[loading] {
  background: #636463;
  opacity: 0.7;
}

.custom-nav-bar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    background-color: transparent;
    z-index: 999;
}

.nav-bar-content {
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
}

.back-button {
    position: absolute;
    left: 20rpx;
    top: 0;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    padding-right: 20rpx;
}

.back-icon {
  width: 56rpx;
  height: 56rpx;
}
