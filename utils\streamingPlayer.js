// utils/streamingStoryPlayer.js
// Handles real-time, streaming playback of stories using HTTP Chunked Transfer.

const fileSystemManager = wx.getFileSystemManager();
let innerAudioContext = null;
let requestTask = null;
let tempFilePath = '';

// Main entry point to start the streaming playback
export async function streamAndPlay(page, storyData, voiceType, ticket) {
  try {
    page.setData({ isLoading: true, audioStatus: 'connecting' });

    if (!ticket) {
      throw new Error("Streaming ticket is missing.");
    } 

    const fullText = constructFullText(storyData);
    if (!fullText) throw new Error('Story content is empty.');
    
    const requestPayload = await buildRequestPayload(fullText, voiceType);

    initiateHttpStream(page, ticket, requestPayload, storyData);

  } catch (error) {
    console.error('Error setting up streaming story:', error);
    page.setData({ isLoading: false, audioStatus: 'failed' });
    wx.showToast({ title: '播放失败，请重试', icon: 'none' });
  }
}

function constructFullText(storyData) {
  const { title, subtitle, content, question } = storyData;
  let text = `${title || ''} ${subtitle || ''} ${content || ''}`;
  if (question && question.trim().length > 0) {
    text += ` ${question.trim()}`;
  }
  // Clean the text to be API-safe
  return text.replace(/[\n\r\t◉]+/g, ' ').replace(/ +/g, ' ').trim();
}

async function buildRequestPayload(text, voiceType) {
    const babyInfo = wx.getStorageSync('babyInfo');
    let voiceParamForAPI = ''; // This will be the value for the 'voice' parameter

    const BASE_MODEL_PATH = 'FunAudioLLM/CosyVoice2-0.5B';

    switch (voiceType) {
        case 'auntie':
        case 'female':
            voiceParamForAPI = `${BASE_MODEL_PATH}:claire`;
            break;
        case 'uncle':
        case 'male':
            voiceParamForAPI = `${BASE_MODEL_PATH}:benjamin`;
            break;
        case 'mom':
            // For mom, we will use the default claire voice for now.
            // If custom voice is needed, it might require a different API call or integration method.
            voiceParamForAPI = `${BASE_MODEL_PATH}:claire`;
            break;
        case 'dad':
            // For dad, we will use the default benjamin voice for now.
            // If custom voice is needed, it might require a different API call or integration method.
            voiceParamForAPI = `${BASE_MODEL_PATH}:benjamin`;
            break;
        default:
            voiceParamForAPI = `${BASE_MODEL_PATH}:claire`; // Default fallback
            break;
    }

    const payload = {
        model: BASE_MODEL_PATH,
        input: text,
        response_format: "mp3",
        sample_rate: 32000,
        stream: true,
        speed: 1,
        gain: 0,
        voice: voiceParamForAPI,
    };

    // Removed voice_uri logic as it's not in the provided curl example.
    // If custom voice is critical, please check SiliconFlow's specific documentation for it.

    return payload;
}

function initiateHttpStream(page, ticket, payload, storyData) {
  cleanup(); // Clean up previous sessions
  innerAudioContext = wx.createInnerAudioContext();
  tempFilePath = `${wx.env.USER_DATA_PATH}/${Date.now()}.mp3`;
  let isFirstChunk = true;

  requestTask = wx.request({
    url: ticket.url,
    method: 'POST',
    header: {
      'Authorization': `Bearer ${ticket.apiKey}`,
      'Content-Type': 'application/json',
    },
    data: payload,
    responseType: 'arraybuffer',
    enableChunked: true,
    success: (res) => {
      console.log('HTTP stream finished successfully.');
      // The stream is complete, now save the final file to the cloud
      saveAudioInBackground(page, storyData._id, tempFilePath);
    },
    fail: (err) => {
      console.error('HTTP stream request failed:', err);
      page.setData({ isLoading: false, audioStatus: 'failed' });
    }
  });

  requestTask.onChunkReceived((res) => {
    fileSystemManager.appendFileSync(tempFilePath, res.data, 'binary');
    if (isFirstChunk) {
      isFirstChunk = false;
      page.setData({ isLoading: false, audioStatus: 'playing' });
      innerAudioContext.src = tempFilePath;
      innerAudioContext.play();
    }
  });

  // Setup Audio Player Listeners
  innerAudioContext.onPlay(() => page.setData({ isPlaying: true }));
  innerAudioContext.onPause(() => page.setData({ isPlaying: false }));
  innerAudioContext.onEnded(() => page.setData({ isPlaying: false }));
  innerAudioContext.onTimeUpdate(() => {
    if (innerAudioContext.duration) {
      page.setData({
        currentTime: innerAudioContext.currentTime,
        duration: innerAudioContext.duration,
        progress: (innerAudioContext.currentTime / innerAudioContext.duration) * 100,
      });
    }
  });
}

async function saveAudioInBackground(page, storyId, filePath) {
  try {
    console.log(`Uploading completed audio file to cloud storage...`);
    const uploadResult = await wx.cloud.uploadFile({
      cloudPath: `stories/${storyId}/${Date.now()}.mp3`,
      filePath: filePath,
    });

    if (uploadResult.fileID) {
      console.log(`File uploaded successfully. FileID: ${uploadResult.fileID}`);
      await wx.cloud.callFunction({
        name: 'saveStoryAudio',
        data: { storyId: storyId, fileID: uploadResult.fileID },
      });
    }
  } catch (error) {
    console.error('Failed to save audio in background:', error);
  }
}

// --- Public Control Functions ---
export function play() {
  if (innerAudioContext) innerAudioContext.play();
}

export function pause() {
  if (innerAudioContext) innerAudioContext.pause();
}

export function seek(value) {
    if (innerAudioContext && innerAudioContext.duration) {
        const position = value / 100 * innerAudioContext.duration;
        innerAudioContext.seek(position);
    }
}

export function cleanup() {
  if (requestTask) {
    requestTask.abort();
    requestTask = null;
  }
  if (innerAudioContext) {
    innerAudioContext.stop();
    innerAudioContext = null;
  }
  if (tempFilePath) {
    fileSystemManager.unlink({
        filePath: tempFilePath,
        fail: (err) => console.log("Failed to unlink temp file", err)
    });
    tempFilePath = '';
  }
}
