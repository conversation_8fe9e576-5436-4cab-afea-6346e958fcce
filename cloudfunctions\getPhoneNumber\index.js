// 云函数入口文件
const cloud = require('wx-server-sdk')

cloud.init({ env: cloud.DYNAMIC_CURRENT_ENV });

// 云函数入口函数
exports.main = async (event, context) => {
  try {
    // 获取微信用户的openid
    const wxContext = cloud.getWXContext()
    
    // 使用新版本API获取手机号
    const res = await cloud.openapi.phonenumber.getPhoneNumber({
      code: event.code
    })

    // 返回手机号信息
    return {
      success: true,
      phoneNumber: res.phoneInfo.phoneNumber,
      errMsg: res.errMsg
    }
  } catch (err) {
    console.error('[getPhoneNumber] 错误：', err)
    return {
      success: false,
      error: err.message || err.errMsg || '获取手机号失败'
    }
  }
}
