<view class="babyinfo-container">
  <view class="custom-nav-bar" style="padding-top: {{menuTop}}px; height: {{menuHeight}}px;">
    <view class="nav-title">请设置宝贝信息</view>
  </view>
  <button class="avatar-wrapper" open-type="chooseAvatar" bindchooseavatar="onChooseAvatar">
    <image class="avatar" src="{{avatarUrl || '/images/default_avatar.png'}}" mode="aspectFill"></image>
  </button>

  <view class="gender-selection">
    <view class="gender-btn {{gender === 'boy' ? 'active' : ''}}" bindtap="onGenderSelect" data-gender="boy">
      <view class="gender-icon boy-icon">♂</view>
      <text class="gender-text">男孩</text>
    </view>
    <view class="gender-btn {{gender === 'girl' ? 'active' : ''}}" bindtap="onGenderSelect" data-gender="girl">
      <view class="gender-icon girl-icon">♀</view>
      <text class="gender-text">女孩</text>
    </view>
  </view>

  <input type="nickname" class="nickname-input" placeholder="请输入昵称" value="{{nickname}}" bindchange="onNicknameChange" />

  <view class="form-container">
    <view class="form-item">
      <picker mode="date" value="{{birthday}}" start="2000-01-01" end="{{today}}"  bindchange="onBirthdayChange">
        <view class="picker-wrapper">
          <view class="picker {{birthday ? '' : 'placeholder'}}">
            {{birthday || '请选择宝贝的生日'}}
          </view>
        </view>
      </picker>
    </view>
  </view>

  <view class="btn-container">
    <button class="next-btn {{canSubmit ? '' : 'disabled'}}" bindtap="onSubmit">下一步</button>
  </view>
</view>