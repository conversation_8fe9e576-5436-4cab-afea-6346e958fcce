App({
  onLaunch: function() {
    // 初始化云开发环境
    if (!wx.cloud) {
      console.error('请使用 2.2.3 或以上的基础库以使用云能力');
    } else {
      wx.cloud.init({
        env: 'cloudbase-1gahfeezccaf7d37', // 替换为你的云开发环境ID
        traceUser: true
      });
    }
    // 适配自定义导航栏参数
    const systemInfo = wx.getSystemInfoSync();
    const menuButtonInfo = wx.getMenuButtonBoundingClientRect();
    this.globalData = {
      navBarHeight: systemInfo.statusBarHeight + 44, // 备用
      menuTop: menuButtonInfo.top,
      menuHeight: menuButtonInfo.height,
      menuRight: systemInfo.screenWidth - menuButtonInfo.right,
    }
    wx.setInnerAudioOption({
      obeyMuteSwitch: false
    })
  }
});
