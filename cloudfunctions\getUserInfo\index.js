// 云函数入口文件
const cloud = require('wx-server-sdk')

cloud.init({ env: cloud.DYNAMIC_CURRENT_ENV });

const db = cloud.database()

// 云函数入口函数
exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext()
  const openid = wxContext.OPENID

  try {
    // 查询用户信息
    const userResult = await db.collection('users').where({
      _openid: openid
    }).get()
    
    // 如果用户不存在，创建一个新的用户记录
    if (!userResult.data || userResult.data.length === 0) {
      // 创建新用户记录
      const newUser = {
        _openid: openid,
        createTime: db.serverDate(),
        updateTime: db.serverDate()
        // vipExpireDate字段不设置，保持为空
      }
      
      const addResult = await db.collection('users').add({
        data: newUser
      })
      
      console.log('已为新用户创建users记录', addResult)
      
      // 返回新创建的用户信息
      return {
        code: 0,
        data: newUser,
        message: '用户信息创建成功'
      }
    }
    
    // 返回查询到的用户信息
    return {
      code: 0,
      data: userResult.data[0],
      message: '获取成功'
    }
  } catch (error) {
    console.error('获取用户信息失败：', error)
    return {
      code: -1,
      data: null,
      message: '获取失败: ' + error.message
    }
  }
}