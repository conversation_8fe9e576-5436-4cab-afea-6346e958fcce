<view class="container">
   <view class="custom-nav-bar" style="padding-top: {{menuTop}}px; height: {{menuHeight}}px;">
    <image class="back-icon" src="/images/back.png" mode="aspectFit" bindtap="onBack" />
    <view class="nav-title">会员中心</view>
  </view>

  <!-- 会员状态区域 -->
  <view class="vip-expired">
    <text class="vip-text-expired" wx:if="{{vipStatus === 'never'}}">您还不是会员！立即开通会员，即刻畅享所有权益！</text>
    <text class="vip-text-expired" wx:elif="{{vipStatus === 'expired'}}">您当前会员已到期：{{vipExpireDate}}。请立即续费，继续畅享会员权益！</text>
    <text class="vip-text" wx:elif="{{vipStatus === 'active'}}">你当前是VIP会员，会员有效期至: {{vipExpireDate}}</text>
  </view>
  <view class="vip-cards">
    <view class="vip-card {{selectedPlan === 'yearly' ? 'selected' : ''}}" bindtap="selectPlan" data-plan="yearly">
      <view class="price">0.26元 / 天</view>
      <view class="duration">1年 ( ¥98 )</view>
    </view>
    <view class="vip-card {{selectedPlan === 'monthly' ? 'selected' : ''}}" bindtap="selectPlan" data-plan="monthly">
      <view class="price">0.53元 / 天</view>
      <view class="duration">1个月 ( ¥16 )</view>
    </view>
  </view>

  <!-- 开通按钮 -->
  <button class="vip-btn" bindtap="handlePayment" wx:if="{{!isIOS}}">
    <text>{{buttonText}}</text>
  </button>
  <view class="vip-btn-ios" wx:else>
    <text class="ios-notice-line1">由于相关规范，iOS暂不支持购买会员。</text>
    <text class="ios-notice-line2">（说明：已在安卓手机购买的会员权益，可享会员权益！）</text>
  </view>

  <!-- 会员权益 -->
  <view class="benefits-section">
    <view class="benefits-title">会员享受所有权益</view>
    <view class="benefits-list">
      <view class="benefit-item">
        <image class="benefit-icon" src="cloud://cloudbase-1gahfeezccaf7d37.636c-cloudbase-1gahfeezccaf7d37-1354986900/images/app/quanyi1.png" mode="aspectFit"></image>
        <view class="benefit-content">
          <text class="benefit-name">畅享个性化定制故事</text>
          <text class="benefit-desc">故事听腻了咋办？只需说出您想听的故事，AI随时随地为您生成您想听的故事。生成的故事还会根据孩子的年龄、喜好自动匹配。</text>
        </view>
      </view>
      <view class="benefit-item">
        <image class="benefit-icon" src="cloud://cloudbase-1gahfeezccaf7d37.636c-cloudbase-1gahfeezccaf7d37-1354986900/images/app/quanyi2.png" mode="aspectFit"></image>
        <view class="benefit-content">
          <text class="benefit-name">畅享人声复刻讲故事</text>
          <text class="benefit-desc">您没时间给孩子讲故事咋办？只需录音，即可复刻您的声音，给孩子讲故事，让孩子感受您的温暖。</text>
        </view>
      </view>
      <view class="benefit-item">
        <image class="benefit-icon" src="cloud://cloudbase-1gahfeezccaf7d37.636c-cloudbase-1gahfeezccaf7d37-1354986900/images/app/quanyi3.png" mode="aspectFit"></image>
        <view class="benefit-content">
          <text class="benefit-name">分龄故事推荐</text>
          <text class="benefit-desc">给孩子听什么合适？AI将给不同年龄，自动生成适合孩子年龄段的故事类型和内容。每个故事都设有问题？让孩子真正学到知识。</text>
        </view>
      </view>
      <view class="benefit-item">
        <image class="benefit-icon" src="cloud://cloudbase-1gahfeezccaf7d37.636c-cloudbase-1gahfeezccaf7d37-1354986900/images/app/quanyi4.png" mode="aspectFit"></image>
        <view class="benefit-content">
          <text class="benefit-name">故事内容以学习为主</text>
          <text class="benefit-desc">还在担心孩子听不好的故事内容吗？推荐的故事内容，全部以提升孩子的知识学习、语言表达、思维能力以及社交能力为准。</text>
        </view>
      </view>
      <view class="benefit-item">
        <image class="benefit-icon" src="cloud://cloudbase-1gahfeezccaf7d37.636c-cloudbase-1gahfeezccaf7d37-1354986900/images/app/quanyi5.png" mode="aspectFit"></image>
        <view class="benefit-content">
          <text class="benefit-name">丰富实用功能</text>
          <text class="benefit-desc">收藏、多种声音设置、个性化推荐等丰富实用功能等您来体验！</text>
        </view>
      </view>
    </view>
  </view>
</view>