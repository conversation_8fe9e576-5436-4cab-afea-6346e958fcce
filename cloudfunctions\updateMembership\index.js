const cloud = require('wx-server-sdk');
cloud.init({ env: cloud.DYNAMIC_CURRENT_ENV });
const db = cloud.database();

exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext();
  const openid = wxContext.OPENID;
  const { days } = event;

  if (!days) {
    return { code: -1, message: '参数错误' };
  }

  try {
    const userRes = await db.collection('users').where({ _openid: openid }).get();
    const now = Date.now();
    let newExpire;

    if (userRes.data.length === 0) {
      newExpire = new Date(now + days * 24 * 60 * 60 * 1000);
      await db.collection('users').add({
        data: {
          _openid: openid,
          vipExpireDate: newExpire,
          createTime: db.serverDate()
        }
      });
    } else {
      const user = userRes.data[0];
      const oldExpire = new Date(user.vipExpireDate);
      const baseTime = (user.vipExpireDate && oldExpire > new Date(now)) ? oldExpire.getTime() : now;
      newExpire = new Date(baseTime + days * 24 * 60 * 60 * 1000);

      await db.collection('users').doc(user._id).update({
        data: {
          vipExpireDate: newExpire
        }
      });
    }

    return { code: 0, message: '更新会员信息成功' };
  } catch (error) {
    console.error('更新会员失败:', error);
    return {
      code: -1,
      message: '更新失败：' + error.message
    };
  }
};
