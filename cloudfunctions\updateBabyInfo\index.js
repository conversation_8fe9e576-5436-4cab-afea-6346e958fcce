// 云函数入口文件
const cloud = require('wx-server-sdk')

cloud.init({ env: cloud.DYNAMIC_CURRENT_ENV });

// 云函数入口函数
exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext()
  const db = cloud.database()
  
  try {
    // 更新宝宝信息
    // 过滤掉不需要更新的字段
    const { _id, _openid, createTime, ...updateData } = event
    
    const result = await db.collection('baby_info').where({
      _openid: wxContext.OPENID
    }).update({
      data: {
        ...updateData,
        updateTime: db.serverDate()
      }
    })

    return {
      code: 0,
      message: '更新成功',
      data: result
    }
  } catch (err) {
    return {
      code: -1,
      message: '更新失败',
      error: err
    }
  }
}