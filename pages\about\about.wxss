/* pages/about/about.wxss */
.container {
  padding: 30rpx;
  height: 100vh;
  box-sizing: border-box;
  background-image: url('https://636c-cloudbase-1gahfeezccaf7d37-1354986900.tcb.qcloud.la/backgroud.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
  background-position: center;
  background-attachment: fixed;
}

.custom-nav-bar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    height: 180rpx;
    background-color: rgba(48, 47, 51, 0.4);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 999;
    backdrop-filter: blur(10px);
    padding-bottom: 10rpx;
}
.custom-nav-bar::after {
    content: "";
    position: absolute;
    left: 0;
    right: 0;
    bottom: 0;
    top: 0;
    background: rgba(48, 47, 51, 0.4);
    z-index: -1;
    border-radius: 0 0 32rpx 32rpx;
    pointer-events: none;
}

.nav-title {
    color:rgb(200, 200, 241);
    font-size: 36rpx;
    flex-grow: 1;
    text-align: center;
    margin-right:100rpx;
}

.back-icon {
  width: 56rpx;
  height: 56rpx;
  margin-left: 20rpx;
}

.about-text {
  margin-top:160rpx;
  color:#ffffff;
  line-height:1.6;
  font-size:36rpx;
}