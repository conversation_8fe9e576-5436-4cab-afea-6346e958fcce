// pages/babyinfo/babyinfo.js
Page({
  data: {
    menuTop: 0,
    menuHeight: 0,
    avatarUrl: '',
    nickname: '',
    gender: 'boy',
    birthday: '',
    today: '',
    canSubmit: false
  },

  onLoad(options) {
    // 设置今天日期作为日期选择器的最大值
    const today = new Date();
    const year = today.getFullYear();
    const month = String(today.getMonth() + 1).padStart(2, '0');
    const day = String(today.getDate()).padStart(2, '0');
    const app = getApp();
    this.setData({
      today: `${year}-${month}-${day}`,
      menuTop: app.globalData.menuTop,
      menuHeight: app.globalData.menuHeight,
    });

    // 获取已有的宝贝信息
    const babyInfo = wx.getStorageSync('babyInfo');
    if (babyInfo) {
      this.setData({
        avatarUrl: babyInfo.avatarUrl || '',
        nickname: babyInfo.nickname || '',
        gender: babyInfo.gender || '',
        birthday: babyInfo.birthday || ''
      }, this.checkFormValid);
    }
  },

  // 选择头像事件
  onChooseAvatar(e) {
    const avatarUrl = e.detail.avatarUrl;
    // 上传头像到云存储
    wx.cloud.uploadFile({
      cloudPath: `avatars/${Date.now()}.jpg`,
      filePath: avatarUrl,
      success: res => {
        const fileID = res.fileID;
        this.setData({
          avatarUrl: fileID
        }, this.checkFormValid);
      },
      fail: err => {
        console.error('上传头像失败', err);
        wx.showToast({
          title: '上传头像失败',
          icon: 'none'
        });
      }
    });
  },

  // 昵称输入事件
  onNicknameChange(e) {
    this.setData({
      nickname: e.detail.value
    }, this.checkFormValid);
  },

  // 性别选择事件
  onGenderSelect(e) {
    const gender = e.currentTarget.dataset.gender;
    this.setData({
      gender: gender
    }, this.checkFormValid);
  },

  // 生日选择事件
  onBirthdayChange(e) {
    this.setData({
      birthday: e.detail.value
    }, this.checkFormValid);
  },

  // 检查表单是否有效
  checkFormValid() {
    const { nickname, gender, birthday } = this.data;
    const isValid = nickname.trim() !== '' && gender !== '' && birthday !== '';
    this.setData({
      canSubmit: isValid
    });
  },

  // 提交表单
  onSubmit() {
    if (!this.data.canSubmit) {
      wx.showToast({
        title: '请完善宝贝信息',
        icon: 'none',
        duration: 2000
      });
      return;
    }

    const { avatarUrl, nickname, gender, birthday } = this.data;
    
    // 如果用户没有设置头像，使用默认头像
    const defaultAvatarUrl = '/images/default_avatar.png';
    
    // 保存宝宝信息
    const babyInfo = {
      avatarUrl: avatarUrl || defaultAvatarUrl,
      nickname,
      gender,
      birthday
    };

    // 保存到本地存储
    wx.setStorageSync('babyInfo', babyInfo);
    
    // 保存到云数据库
    wx.cloud.callFunction({
      name: 'saveBabyInfo',
      data: {
        nickname: babyInfo.nickname,
        gender: babyInfo.gender,
        birthday: babyInfo.birthday,
        avatarUrl: babyInfo.avatarUrl, // 已经在babyInfo中处理了默认头像
        voiceType: '', // 初始设置为空，在声音设置页面再更新
        voiceUrl: '',
        momVoiceUrl: '',
        dadVoiceUrl: ''
      },
      success: () => {
        // 跳转到声音设置页面
        wx.navigateTo({
          url: '/pages/voicesetting/voicesetting'
        });
      },
      fail: err => {
        console.error('保存失败', err);
        wx.showToast({
          title: '保存失败，请重试',
          icon: 'none'
        });
      }
    });
  }
})