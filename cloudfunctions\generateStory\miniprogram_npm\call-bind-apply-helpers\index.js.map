{"version": 3, "sources": ["index.js", "functionCall.js", "actualApply.js", "functionApply.js", "reflectApply.js"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;AELA,ADGA,ADGA;AELA,ADGA,ADGA;AELA,AFMA;AELA,ACHA,AHSA;AELA,ACHA,AHSA;AELA,ACHA,AHSA;AELA,ACHA,AHSA,AIZA;AFOA,ACHA,AHSA,AIZA;AFOA,AFMA,AIZA;AFOA,AFMA,AIZA;AFOA,AENA", "file": "index.js", "sourcesContent": ["\n\nvar bind = require('function-bind');\nvar $TypeError = require('es-errors/type');\n\nvar $call = require('./functionCall');\nvar $actualApply = require('./actualApply');\n\n/** @type {(args: [Function, thisArg?: unknown, ...args: unknown[]]) => Function} TODO FIXME, find a way to use import('.') */\nmodule.exports = function callBindBasic(args) {\n\tif (args.length < 1 || typeof args[0] !== 'function') {\n\t\tthrow new $TypeError('a function is required');\n\t}\n\treturn $actualApply(bind, $call, args);\n};\n", "\n\n/** @type {import('./functionCall')} */\nmodule.exports = Function.prototype.call;\n", "\n\nvar bind = require('function-bind');\n\nvar $apply = require('./functionApply');\nvar $call = require('./functionCall');\nvar $reflectApply = require('./reflectApply');\n\n/** @type {import('./actualApply')} */\nmodule.exports = $reflectApply || bind.call($call, $apply);\n", "\n\n/** @type {import('./functionApply')} */\nmodule.exports = Function.prototype.apply;\n", "\n\n/** @type {import('./reflectApply')} */\nmodule.exports = typeof Reflect !== 'undefined' && Reflect && Reflect.apply;\n"]}