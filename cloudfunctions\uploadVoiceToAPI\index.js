// 云函数入口文件
const cloud = require('wx-server-sdk')
const axios = require('axios')
const FormData = require('form-data')
const fs = require('fs')

cloud.init({ env: cloud.DYNAMIC_CURRENT_ENV });

const db = cloud.database()
async function getConfig() {
  const res = await db.collection('config').doc('story_config').get()
  return res.data
}
// 云函数入口函数
exports.main = async (event, context) => {
  const { filePath, voiceType, openid, text } = event
  const wxContext = cloud.getWXContext()
  const user = wxContext.OPENID || openid
  
  console.log('接收到的参数:', event)
  
  try {
    let audioBuffer;
    
    // 从数据库获取用户的录音URL
    let actualFilePath = filePath;
    
    // 如果提供了voiceType，尝试从数据库获取对应的录音URL
    if (voiceType) {
      try {
        const userInfo = await db.collection('baby_info').where({ _openid: user }).get()
        
        if (userInfo.data && userInfo.data.length > 0) {
          const babyInfo = userInfo.data[0]
          
          // 根据voiceType获取对应的录音URL
          if (voiceType === 'mom' && babyInfo.momVoiceUrl) {
            actualFilePath = babyInfo.momVoiceUrl
            console.log('使用数据库中的妈妈录音URL:', actualFilePath)
          } else if (voiceType === 'dad' && babyInfo.dadVoiceUrl) {
            actualFilePath = babyInfo.dadVoiceUrl
            console.log('使用数据库中的爸爸录音URL:', actualFilePath)
          }
        }
      } catch (dbError) {
        console.error('从数据库获取录音URL失败:', dbError)
        // 继续使用传入的filePath
      }
    }
    
    // 处理不同类型的文件路径
    if (actualFilePath.startsWith('cloud://') || (actualFilePath.startsWith('https://') && actualFilePath.indexOf('tcb.qcloud.la') > -1)) {
      // 从云存储下载录音文件
      console.log('开始从云存储下载录音文件:', actualFilePath)
      const result = await cloud.downloadFile({
        fileID: actualFilePath
      })
      
      if (!result || !result.fileContent) {
        throw new Error('下载录音文件失败')
      }
      
      audioBuffer = result.fileContent;
      console.log('云存储录音文件下载成功，文件大小:', audioBuffer.length, 'bytes')
      
    } else if (actualFilePath.startsWith('wxfile://') || actualFilePath.startsWith('http://tmp/')) {
      // 处理微信小程序临时文件
      console.log('检测到微信小程序临时文件，需要先上传到云存储:', actualFilePath)
      
      // 返回明确的错误信息和解决方案
      return {
        success: false,
        error: 'TEMP_FILE_NOT_SUPPORTED',
        message: '微信小程序临时文件无法在云函数中直接访问',
        solution: '请在小程序端先将录音文件上传到云存储，然后传入云存储fileID',
        tempFilePath: actualFilePath
      }
      
    } else if (actualFilePath.startsWith('http://') || actualFilePath.startsWith('https://')) {
      // 处理普通HTTP/HTTPS URL
      console.log('开始从HTTP链接下载音频文件:', actualFilePath)
      
      try {
        const audioResponse = await axios.get(actualFilePath, { 
          responseType: 'arraybuffer',
          timeout: 30000 // 30秒超时
        })
        audioBuffer = Buffer.from(audioResponse.data);
        console.log('HTTP URL音频文件下载成功，文件大小:', audioBuffer.length, 'bytes')
      } catch (httpError) {
        console.error('从HTTP链接下载文件失败:', httpError)
        throw new Error('无法从提供的链接下载音频文件: ' + httpError.message)
      }
      
    } else {
      // 不支持的文件路径格式
      console.error('收到不支持的文件路径格式:', actualFilePath)
      throw new Error('不支持的文件路径格式。支持的格式：cloud://fileID 或 https://链接')
    }
    
    // 验证音频缓冲区
    if (!audioBuffer || audioBuffer.length === 0) {
      throw new Error('音频文件为空或下载失败')
    }
    
    // 准备上传到API
    const apiUrl = 'https://api.siliconflow.cn/v1/uploads/audio/voice'
    
    // 创建FormData
    const formData = new FormData()
    formData.append('model', 'FunAudioLLM/CosyVoice2-0.5B')
    formData.append('customName', user)
    formData.append('text', text || '亲爱的宝贝！今天我来给你讲一个有趣的故事。从前，有一只小狐狸发现了一个神奇的蘑菇，蘑菇告诉它："我是魔法蘑菇，来一起冒险吧！"小狐狸勇敢地说："好呀！"')
    
    // 修复：正确添加音频文件到FormData
    // 使用 Readable 流来包装 Buffer
    const { Readable } = require('stream')
    const audioStream = new Readable()
    audioStream.push(audioBuffer)
    audioStream.push(null) // 表示流结束
    
    formData.append('file', audioStream, {
      filename: `${voiceType || 'audio'}_recording.mp3`,
      contentType: 'audio/mpeg',
      knownLength: audioBuffer.length
    })
    
    console.log('开始上传录音文件到API，文件大小:', audioBuffer.length, 'bytes')
    const config = await getConfig()
    const SILICONFLOW_API_KEY = config.SILICONFLOW_API_KEY
    // 发送请求到API
    const response = await axios.post(apiUrl, formData, {
      
      headers: {
        'Authorization': `Bearer ${SILICONFLOW_API_KEY}`,
        ...formData.getHeaders()
      },
      maxContentLength: Infinity,
      maxBodyLength: Infinity,
      timeout: 60000 // 60秒超时
    })
    
    console.log('API响应状态:', response.status)
    console.log('API响应数据:', response.data)
    
    if (response.status === 200 && response.data && response.data.uri) {
      // 更新数据库中的uri字段
      try {
        const field = voiceType === 'mom' ? 'momUri' : 'dadUri'
        const updateResult = await db.collection('baby_info').where({
          _openid: user
        }).update({
          data: {
            [field]: response.data.uri,
            [`${field}UpdateTime`]: new Date()
          }
        })
        
        console.log(`成功更新${voiceType}的uri到数据库:`, updateResult)
      } catch (dbError) {
        console.error('更新数据库失败:', dbError)
        // 继续返回结果，不中断流程
      }
      
      return {
        success: true,
        uri: response.data.uri,
        message: '录音上传成功',
        fileSize: audioBuffer.length
      }
    } else {
      console.error('API响应格式不正确:', response.status, response.data)
      throw new Error(`API响应异常: ${response.status} - ${JSON.stringify(response.data)}`)
    }
    
  } catch (error) {
    console.error('上传录音到API失败:', error)
    
    // 提供更详细的错误信息
    let errorMessage = error.message || '上传录音到API失败'
    let errorType = 'UNKNOWN_ERROR'
    
    if (error.code === 'ECONNREFUSED' || error.code === 'ENOTFOUND') {
      errorType = 'NETWORK_ERROR'
      errorMessage = '网络连接失败，请检查网络或稍后重试'
    } else if (error.response) {
      errorType = 'API_ERROR'
      errorMessage = `API错误: ${error.response.status} - ${error.response.data || error.response.statusText}`
    } else if (error.message.includes('timeout')) {
      errorType = 'TIMEOUT_ERROR'
      errorMessage = '请求超时，请稍后重试'
    }
    
    return {
      success: false,
      error: errorMessage,
      errorType: errorType,
      message: '录音上传失败'
    }
  }
}