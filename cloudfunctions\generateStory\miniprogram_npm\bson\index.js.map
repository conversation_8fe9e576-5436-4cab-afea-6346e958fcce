{"version": 3, "sources": ["bson.js", "binary.js", "ensure_buffer.js", "error.js", "parser/utils.js", "utils/global.js", "uuid_utils.js", "constants.js", "code.js", "db_ref.js", "decimal128.js", "long.js", "double.js", "extended_json.js", "int_32.js", "max_key.js", "min_key.js", "objectid.js", "regexp.js", "symbol.js", "timestamp.js", "map.js", "parser/calculate_size.js", "parser/deserializer.js", "validate_utf8.js", "parser/serializer.js"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;ACFA,ADGA;AC<PERSON>,ADGA;ACFA,ADGA;ACFA,ADGA,AENA;ADIA,ADGA,AENA;ADIA,ADGA,AENA;ADIA,ADGA,AENA,ACHA;AFOA,ADGA,AENA,ACHA;AFOA,ADGA,AENA,ACHA;AFOA,ADGA,AENA,ACHA,ACHA;AHUA,ADGA,AENA,ACHA,ACHA;AHUA,ADGA,AENA,ACHA,ACHA;AHUA,ADGA,AENA,ACHA,ACHA,ACHA;AJaA,ADGA,AENA,ACHA,ACHA,ACHA;AJaA,ADGA,AENA,ACHA,ACHA,ACHA;AJaA,ADGA,AENA,ACHA,ACHA,ACHA,ACHA;ALgBA,ADGA,AENA,ACHA,ACHA,ACHA,ACHA;ALgBA,ADGA,AENA,ACHA,ACHA,ACHA,ACHA;ALgBA,ADGA,AOrBA,ALeA,ACHA,ACHA,ACHA,ACHA;ALgBA,ADGA,AOrBA,ALeA,ACHA,ACHA,ACHA,ACHA;ALgBA,ADGA,AOrBA,ALeA,ACHA,ACHA,ACHA,ACHA;ALgBA,ADGA,AQxBA,ADGA,ALeA,ACHA,ACHA,ACHA,ACHA;ALgBA,ADGA,AQxBA,ADGA,ALeA,ACHA,ACHA,ACHA,ACHA;ALgBA,ADGA,AQxBA,ADGA,ALeA,ACHA,ACHA,ACHA,ACHA;ALgBA,ADGA,AQxBA,ADGA,AENA,APqBA,ACHA,ACHA,ACHA,ACHA;ALgBA,ADGA,AQxBA,ADGA,AENA,APqBA,ACHA,ACHA,ACHA,ACHA;ALgBA,ADGA,AQxBA,ADGA,AENA,APqBA,ACHA,ACHA,ACHA,ACHA;ALgBA,ADGA,AQxBA,ADGA,AENA,ACHA,ARwBA,ACHA,ACHA,ACHA,ACHA;ALgBA,ADGA,AQxBA,ADGA,AENA,ACHA,APqBA,ACHA,ACHA,ACHA;ALgBA,ADGA,AQxBA,ADGA,AENA,ACHA,APqBA,ACHA,ACHA,ACHA;ALgBA,ADGA,AQxBA,ADGA,AENA,ACHA,APqBA,AQxBA,APqBA,AENA;ALgBA,ADGA,AQxBA,ADGA,AENA,ACHA,APqBA,AQxBA,APqBA,AENA;ALgBA,ADGA,AQxBA,ADGA,AENA,ACHA,APqBA,AQxBA,APqBA,AENA;ALgBA,ADGA,AQxBA,ADGA,AENA,ACHA,AENA,AT2BA,AQxBA,APqBA,AENA;ALgBA,ADGA,AQxBA,ADGA,AENA,ACHA,AENA,AT2BA,AQxBA,APqBA,AENA;ALgBA,ADGA,AQxBA,ADGA,AENA,ACHA,AENA,AT2BA,AQxBA,APqBA,AENA;ALgBA,ADGA,AQxBA,ADGA,AENA,ACHA,AENA,AT2BA,AU9BA,AFMA,APqBA,AENA;ALgBA,ADGA,AQxBA,ADGA,AENA,ACHA,AENA,AT2BA,AU9BA,AFMA,APqBA,AENA;ALgBA,ADGA,AQxBA,ADGA,AENA,ACHA,AENA,AT2BA,AU9BA,AFMA,APqBA,AENA;ALgBA,ADGA,AQxBA,ADGA,AENA,ACHA,AENA,AT2BA,AU9BA,ACHA,AHSA,APqBA,AENA;ALgBA,ADGA,AQxBA,ADGA,AENA,ACHA,AENA,AT2BA,AU9BA,ACHA,AHSA,APqBA,AENA;ALgBA,ADGA,AQxBA,ADGA,AENA,ACHA,AENA,AT2BA,AU9BA,ACHA,AHSA,APqBA,AENA;ALgBA,ADGA,AQxBA,ADGA,AENA,ACHA,AENA,AT2BA,AU9BA,ACHA,AHSA,AIZA,AXiCA,AENA;ALgBA,ADGA,AQxBA,ADGA,AENA,ACHA,AENA,AT2BA,AU9BA,ACHA,AHSA,AIZA,AXiCA,AENA;ALgBA,ADGA,AQxBA,ADGA,AENA,ACHA,AENA,AT2BA,AU9BA,ACHA,AHSA,AIZA,AXiCA,AENA;ALgBA,ADGA,AQxBA,ADGA,AENA,ACHA,AENA,AT2BA,AU9BA,ACHA,AHSA,AIZA,ACHA,AZoCA,AENA;ALgBA,ADGA,AQxBA,ADGA,AENA,ACHA,AENA,AT2BA,AU9BA,ACHA,AHSA,AIZA,ACHA,AZoCA,AENA;ALgBA,ADGA,AQxBA,ADGA,AENA,ACHA,AENA,AT2BA,AU9BA,ACHA,AHSA,AIZA,ACHA,AZoCA,AENA;ALgBA,ADGA,AQxBA,ADGA,AENA,ACHA,AENA,AT2BA,AU9BA,ACHA,AHSA,AIZA,ACHA,ACHA,AbuCA,AENA;ALgBA,ADGA,AQxBA,ADGA,AENA,ACHA,AENA,AT2BA,AU9BA,ACHA,AHSA,AIZA,ACHA,ACHA,AbuCA,AENA;ALgBA,ADGA,AQxBA,ADGA,AENA,ACHA,AENA,AT2BA,AU9BA,ACHA,AHSA,AIZA,ACHA,ACHA,AbuCA;AHUA,ADGA,AQxBA,ADGA,AENA,ACHA,AENA,AT2BA,AU9BA,ACHA,AHSA,AIZA,ACHA,ACHA,AbuCA,Ac1CA;AjBoDA,ADGA,AQxBA,ADGA,AENA,ACHA,AENA,AT2BA,AU9BA,ACHA,AHSA,AIZA,ACHA,ACHA,AbuCA,Ac1CA;AjBoDA,ADGA,AQxBA,ADGA,AENA,ACHA,AENA,AT2BA,AU9BA,ACHA,AHSA,AIZA,ACHA,ACHA,AbuCA,Ac1CA;AjBoDA,ADGA,AQxBA,ADGA,AENA,ACHA,AENA,AT2BA,AU9BA,ACHA,AHSA,AIZA,ACHA,ACHA,AbuCA,Ac1CA,ACHA;AlBuDA,ADGA,AQxBA,ADGA,AENA,ACHA,AENA,AT2BA,AU9BA,ACHA,AHSA,AIZA,ACHA,ACHA,AbuCA,Ac1CA,ACHA;AlBuDA,ADGA,AQxBA,ADGA,AENA,ACHA,AENA,AT2BA,AU9BA,ACHA,AHSA,AIZA,ACHA,ACHA,AbuCA,Ac1CA,ACHA;AlBuDA,ADGA,AQxBA,ADGA,AENA,ACHA,AENA,AT2BA,AU9BA,ACHA,AHSA,AIZA,ACHA,ACHA,AbuCA,Ac1CA,ACHA,ACHA;AnB0DA,ADGA,AQxBA,ADGA,AENA,ACHA,AENA,AT2BA,AU9BA,ACHA,AHSA,AIZA,ACHA,ACHA,AbuCA,Ac1CA,ACHA,ACHA;AnB0DA,ADGA,AQxBA,ADGA,AENA,ACHA,AENA,AT2BA,AU9BA,ACHA,AHSA,AIZA,ACHA,ACHA,AbuCA,Ac1CA,ACHA,ACHA;AnB0DA,ADGA,AQxBA,ADGA,AENA,ACHA,AENA,AT2BA,AU9BA,ACHA,AHSA,AU9BA,ANkBA,ACHA,ACHA,AbuCA,Ac1CA,ACHA,ACHA;AnB0DA,ADGA,AQxBA,ADGA,AENA,ACHA,AENA,ACHA,ACHA,AHSA,AU9BA,ANkBA,ACHA,ACHA,AbuCA,Ac1CA,ACHA,ACHA;AnB0DA,ADGA,AQxBA,ADGA,AENA,ACHA,AENA,ACHA,ACHA,AHSA,AU9BA,ANkBA,ACHA,ACHA,AbuCA,Ac1CA,ACHA,ACHA;AnB0DA,ADGA,AQxBA,ADGA,AENA,ACHA,AENA,ACHA,ACHA,AHSA,AU9BA,ANkBA,ACHA,ACHA,AKfA,AlBsDA,Ac1CA,ACHA,ACHA;AnB0DA,ADGA,AQxBA,ADGA,AENA,ACHA,AENA,ACHA,ACHA,AHSA,AU9BA,ANkBA,ACHA,ACHA,AKfA,AlBsDA,Ac1CA,ACHA,ACHA;AnB0DA,ADGA,AQxBA,ADGA,AENA,ACHA,AENA,ACHA,ACHA,AHSA,AU9BA,ANkBA,ACHA,ACHA,AKfA,AlBsDA,Ac1CA,ACHA,ACHA;AnB0DA,ADGA,AQxBA,ADGA,AENA,ACHA,AENA,ACHA,ACHA,AHSA,AU9BA,ANkBA,ACHA,ACHA,AKfA,ACHA,AnByDA,Ac1CA,ACHA,ACHA;AnB0DA,ADGA,AOrBA,AENA,ACHA,AENA,ACHA,ACHA,AHSA,AU9BA,ANkBA,ACHA,ACHA,AKfA,ACHA,AnByDA,Ac1CA,ACHA,ACHA;AnB0DA,ADGA,AOrBA,AENA,ACHA,AENA,ACHA,ACHA,AHSA,AU9BA,ANkBA,ACHA,ACHA,AKfA,ACHA,AnByDA,Ac1CA,ACHA,ACHA;AnB0DA,ADGA,AOrBA,AENA,ACHA,AENA,ACHA,ACHA,AHSA,AU9BA,ANkBA,ACHA,ACHA,AKfA,ACHA,AnByDA,Ac1CA,ACHA,ACHA,AIZA;AvBsEA,ADGA,AOrBA,AENA,ACHA,AENA,ACHA,ACHA,AHSA,AU9BA,ANkBA,ACHA,ACHA,AKfA,ACHA,AnByDA,Ac1CA,ACHA,ACHA,AIZA;AvBsEA,ADGA,AOrBA,AENA,ACHA,AENA,ACHA,ACHA,AHSA,AU9BA,ANkBA,ACHA,ACHA,AKfA,ACHA,AnByDA,Ac1CA,ACHA,ACHA,AIZA;AvBsEA,ADGA,AOrBA,AENA,ACHA,AENA,ACHA,ACHA,AHSA,AU9BA,ANkBA,ACHA,ACHA,AKfA,ACHA,AENA,ArB+DA,Ac1CA,ACHA,ACHA,AIZA;AvBsEA,ADGA,AOrBA,AENA,ACHA,AENA,ACHA,ACHA,AHSA,AU9BA,ANkBA,ACHA,ACHA,AKfA,ACHA,AENA,ArB+DA,Ac1CA,ACHA,ACHA,AIZA;AvBsEA,ADGA,AOrBA,AENA,ACHA,AENA,ACHA,ACHA,AHSA,AU9BA,ANkBA,ACHA,ACHA,AKfA,ACHA,AENA,ArB+DA,Ac1CA,ACHA,ACHA,AIZA;AvBsEA,ADGA,AOrBA,AENA,ACHA,AENA,ACHA,ACHA,AHSA,AU9BA,ALeA,ACHA,AKfA,ACHA,AENA,ArB+DA,Ac1CA,ACHA,ACHA,AIZA;AvBsEA,ADGA,AOrBA,AENA,ACHA,AENA,ACHA,ACHA,AHSA,AU9BA,ALeA,ACHA,AKfA,ACHA,AENA,ArB+DA,Ac1CA,ACHA,ACHA,AIZA;AvBsEA,ADGA,AOrBA,AENA,ACHA,AENA,ACHA,ACHA,AHSA,AU9BA,ALeA,ACHA,AKfA,ACHA,AENA,ArB+DA,Ac1CA,ACHA,ACHA,AIZA;AvBsEA,ADGA,AOrBA,AENA,ACHA,AENA,ACHA,ACHA,AHSA,AU9BA,AJYA,AKfA,ACHA,AENA,ArB+DA,Ac1CA,ACHA,ACHA,AIZA;AvBsEA,ADGA,AOrBA,AENA,ACHA,AENA,ACHA,ACHA,AHSA,AU9BA,AJYA,AKfA,ACHA,AENA,ArB+DA,Ac1CA,ACHA,ACHA,AIZA;AvBsEA,ADGA,AOrBA,AENA,ACHA,AENA,ACHA,ACHA,AHSA,AU9BA,AJYA,AKfA,ACHA,AENA,ArB+DA,Ac1CA,ACHA,ACHA,AIZA;AvBsEA,ADGA,AOrBA,AENA,ACHA,AENA,ACHA,ACHA,AHSA,AU9BA,AJYA,AKfA,ACHA,AENA,ArB+DA,Ac1CA,ACHA,ACHA,AIZA;AvBsEA,ADGA,AOrBA,AENA,ACHA,AENA,ACHA,ACHA,AHSA,AU9BA,AJYA,AKfA,ACHA,AENA,ArB+DA,Ac1CA,ACHA,ACHA,AIZA;AvBsEA,ADGA,AOrBA,AENA,ACHA,AENA,ACHA,ACHA,AHSA,AU9BA,AJYA,AKfA,ACHA,AENA,ArB+DA,Ac1CA,ACHA,ACHA,AIZA;AvBsEA,ADGA,AOrBA,AENA,ACHA,AENA,ACHA,ACHA,AHSA,AU9BA,AJYA,AKfA,ACHA,AENA,ArB+DA,Ac1CA,ACHA,ACHA,AIZA;AvBsEA,ADGA,AOrBA,AENA,ACHA,AENA,ACHA,ACHA,AHSA,AU9BA,AJYA,AKfA,ACHA,AENA,ArB+DA,Ac1CA,ACHA,ACHA,AIZA;AvBsEA,ADGA,AOrBA,AENA,ACHA,AENA,ACHA,ACHA,AHSA,AU9BA,AJYA,AKfA,ACHA,AENA,ArB+DA,Ac1CA,ACHA,ACHA,AIZA;AvBsEA,ADGA,AOrBA,AENA,ACHA,AENA,ACHA,ACHA,AHSA,AU9BA,AJYA,AKfA,ACHA,AENA,ArB+DA,Ac1CA,ACHA,ACHA,AIZA;AvBsEA,ADGA,AOrBA,AENA,ACHA,AENA,ACHA,ACHA,AHSA,AU9BA,AJYA,AKfA,ACHA,AENA,ArB+DA,Ac1CA,ACHA,ACHA,AIZA;AvBsEA,ADGA,AOrBA,AENA,ACHA,AENA,ACHA,ACHA,AHSA,AU9BA,AJYA,AKfA,ACHA,AENA,ArB+DA,Ac1CA,ACHA,ACHA,AIZA;AvBsEA,ADGA,AOrBA,AENA,ACHA,AENA,ACHA,ACHA,AHSA,AU9BA,AJYA,AKfA,ACHA,AENA,ArB+DA,Ac1CA,ACHA,ACHA,AIZA;AvBsEA,ADGA,AOrBA,AENA,ACHA,AENA,ACHA,ACHA,AHSA,AU9BA,AJYA,AKfA,ACHA,AENA,ArB+DA,Ac1CA,ACHA,ACHA,AIZA;AvBsEA,ADGA,AOrBA,AENA,ACHA,AENA,ACHA,ACHA,AHSA,AU9BA,AJYA,AKfA,ACHA,AENA,ArB+DA,Ac1CA,ACHA,ACHA,AIZA;AvBsEA,ADGA,AOrBA,AENA,ACHA,AENA,ACHA,ACHA,AHSA,AU9BA,AJYA,AKfA,ACHA,AENA,ArB+DA,Ac1CA,ACHA,ACHA,AIZA;AvBsEA,ADGA,AOrBA,AENA,ACHA,AENA,ACHA,ACHA,AHSA,AU9BA,AJYA,AKfA,ACHA,AENA,ArB+DA,Ac1CA,ACHA,ACHA,AIZA;AvBsEA,ADGA,AOrBA,AENA,ACHA,AENA,ACHA,ACHA,AHSA,AU9BA,AJYA,AKfA,ACHA,AENA,ArB+DA,Ac1CA,ACHA,ACHA,AIZA;AvBsEA,ADGA,AOrBA,AENA,ACHA,AENA,ACHA,ACHA,AHSA,AU9BA,AJYA,AKfA,ACHA,AENA,ArB+DA,Ac1CA,ACHA,ACHA,AIZA;AvBsEA,ADGA,AOrBA,AENA,ACHA,AENA,ACHA,AFMA,AU9BA,AJYA,AKfA,ACHA,AENA,ArB+DA,Ac1CA,ACHA,ACHA,AIZA;AvBsEA,ADGA,AOrBA,AENA,ACHA,AENA,ACHA,AFMA,AU9BA,AJYA,AKfA,ACHA,AENA,ArB+DA,Ac1CA,ACHA,ACHA,AIZA;AvBsEA,ADGA,AOrBA,AENA,ACHA,AENA,ACHA,AFMA,AU9BA,AJYA,AKfA,ACHA,AENA,ArB+DA,Ac1CA,ACHA,ACHA,AIZA;AvBsEA,ADGA,AS3BA,ACHA,AENA,ACHA,AFMA,AU9BA,AJYA,AKfA,ACHA,AENA,ArB+DA,Ac1CA,ACHA,ACHA,AIZA;AvBsEA,ADGA,AS3BA,ACHA,AGTA,AFMA,AU9BA,AJYA,AKfA,ACHA,AENA,ArB+DA,Ac1CA,ACHA,ACHA,AIZA;AvBsEA,ADGA,AS3BA,ACHA,AGTA,AFMA,AU9BA,AJYA,AKfA,ACHA,AENA,ArB+DA,Ac1CA,AENA,AIZA;AvBsEA,ADGA,AS3BA,ACHA,AGTA,AFMA,AU9BA,AJYA,AKfA,ACHA,AENA,ArB+DA,Ac1CA,AENA,AIZA;AvBsEA,ADGA,AS3BA,ACHA,AGTA,AFMA,AU9BA,AJYA,AKfA,ACHA,AENA,ArB+DA,Ac1CA,AENA,AIZA;AvBsEA,ADGA,AS3BA,ACHA,AGTA,AFMA,AU9BA,AJYA,AKfA,ACHA,AENA,ArB+DA,Ac1CA,AENA,AIZA;AvBsEA,ADGA,AS3BA,ACHA,AGTA,AFMA,AU9BA,AJYA,AKfA,ACHA,AENA,ArB+DA,Ac1CA,AENA,AIZA;AvBsEA,ADGA,AS3BA,ACHA,AGTA,AFMA,AU9BA,AJYA,AKfA,ACHA,AENA,ArB+DA,Ac1CA,AENA,AIZA;AvBsEA,ADGA,AS3BA,ACHA,AGTA,AFMA,AU9BA,AJYA,AKfA,ACHA,AENA,ArB+DA,Ac1CA,AENA,AIZA;AvBsEA,ADGA,AS3BA,ACHA,AGTA,AFMA,AU9BA,AJYA,AKfA,ACHA,AENA,ArB+DA,Ac1CA,AENA,AIZA;AvBsEA,ADGA,AS3BA,ACHA,AGTA,AFMA,AU9BA,AJYA,AKfA,ACHA,AENA,ArB+DA,Ac1CA,AENA,AIZA;AvBsEA,ADGA,AS3BA,ACHA,AGTA,AFMA,AU9BA,AJYA,AKfA,ACHA,AENA,ArB+DA,Ac1CA,AENA,AIZA;AvBsEA,ADGA,AS3BA,ACHA,AGTA,AFMA,AU9BA,AJYA,AKfA,ACHA,AENA,ArB+DA,Ac1CA,AENA,AIZA;AvBsEA,ADGA,AS3BA,ACHA,AGTA,AFMA,AU9BA,AJYA,AKfA,ACHA,AENA,ArB+DA,Ac1CA,AENA,AIZA;AvBsEA,ADGA,AS3BA,ACHA,AGTA,AFMA,AU9BA,AJYA,AKfA,ACHA,AENA,ArB+DA,Ac1CA,AENA,AIZA;AvBsEA,ADGA,AS3BA,ACHA,AGTA,AFMA,AU9BA,AJYA,AKfA,ACHA,AENA,ArB+DA,Ac1CA,AENA,AIZA;AvBsEA,ADGA,AS3BA,ACHA,AGTA,AFMA,AU9BA,AJYA,AKfA,ACHA,AENA,ArB+DA,Ac1CA,AENA;AnB0DA,ADGA,AS3BA,ACHA,AGTA,AFMA,AU9BA,AJYA,AKfA,ACHA,AENA,ArB+DA,Ac1CA,AENA;AnB0DA,ADGA,AS3BA,ACHA,AGTA,AFMA,AU9BA,AJYA,AKfA,ACHA,AENA,ArB+DA,Ac1CA,AENA;AnB0DA,ADGA,AS3BA,ACHA,AGTA,AFMA,AU9BA,AJYA,AKfA,ACHA,AENA,ArB+DA,Ac1CA,AENA;AnB0DA,ADGA,AS3BA,ACHA,AGTA,AFMA,AU9BA,AJYA,AKfA,ACHA,AENA,ArB+DA,Ac1CA,AENA;AnB0DA,ADGA,AU9BA,AGTA,AFMA,AU9BA,AJYA,AKfA,ACHA,AENA,ArB+DA,Ac1CA,AENA;AnB0DA,ADGA,AU9BA,AGTA,AFMA,AU9BA,AJYA,AKfA,ACHA,AENA,ArB+DA,Ac1CA,AENA;AnB0DA,ADGA,AU9BA,AGTA,AFMA,AU9BA,AJYA,AKfA,ACHA,AENA,ArB+DA,Ac1CA,AENA;AnB0DA,ADGA,AU9BA,AGTA,AFMA,AU9BA,AJYA,AKfA,ACHA,AENA,APqBA,AENA;AnB0DA,ADGA,AU9BA,AGTA,AFMA,AU9BA,AJYA,AKfA,ACHA,AENA,ALeA;AnB0DA,ADGA,AU9BA,AGTA,AFMA,AU9BA,AJYA,AKfA,ACHA,AENA,ALeA;AnB0DA,ADGA,AU9BA,AGTA,AFMA,AU9BA,AJYA,AKfA,ACHA,AENA,ALeA;AnB0DA,ADGA,AU9BA,AGTA,AFMA,AU9BA,AJYA,AKfA,ACHA,AENA,ALeA;AnB0DA,ADGA,AU9BA,AGTA,AFMA,AU9BA,AJYA,AKfA,ACHA,AENA,ALeA;AnB0DA,ADGA,AU9BA,AGTA,AFMA,AU9BA,AJYA,AKfA,ACHA,AENA,ALeA;AnB0DA,ADGA,AU9BA,AGTA,AFMA,AU9BA,AJYA,AKfA,ACHA,AENA,ALeA;AnB0DA,ADGA,AU9BA,AGTA,AFMA,AU9BA,AJYA,AKfA,ACHA,AENA,ALeA;AnB0DA,ADGA,AU9BA,AGTA,AFMA,AU9BA,AJYA,AKfA,ACHA,AENA,ALeA;AnB0DA,ADGA,AU9BA,AGTA,AFMA,AU9BA,AJYA,AKfA,ACHA,AENA,ALeA;AnB0DA,ADGA,AU9BA,AGTA,AFMA,AU9BA,AJYA,AKfA,ACHA,AENA,ALeA;AnB0DA,ADGA,AU9BA,AGTA,AFMA,AU9BA,AJYA,AKfA,ACHA,AENA,ALeA;AnB0DA,ADGA,AU9BA,AGTA,AFMA,AU9BA,AJYA,AKfA,ACHA,AENA,ALeA;AnB0DA,ADGA,AU9BA,AGTA,AFMA,AU9BA,AJYA,AKfA,ACHA,AENA,ALeA;AnB0DA,ADGA,AU9BA,AGTA,AFMA,AU9BA,AJYA,AKfA,ACHA,AENA,ALeA;AnB0DA,ADGA,AU9BA,AGTA,AFMA,AU9BA,AJYA,AKfA,ACHA,AENA,ALeA;AnB0DA,ADGA,AU9BA,AGTA,AFMA,AU9BA,AJYA,AKfA,ACHA,AENA,ALeA;AnB0DA,ADGA,AU9BA,AGTA,AFMA,AU9BA,AJYA,AKfA,ACHA,AENA,ALeA;AnB0DA,ADGA,AU9BA,AGTA,AFMA,AU9BA,AJYA,AKfA,ACHA,AENA,ALeA;AnB0DA,ADGA,AU9BA,AGTA,AFMA,AU9BA,AJYA,AKfA,ACHA,AENA,ALeA;AnB0DA,ADGA,AU9BA,AGTA,AFMA,AU9BA,AJYA,AKfA,ACHA,AENA,ALeA;AnB0DA,ADGA,AU9BA,AGTA,AFMA,AU9BA,AJYA,AKfA,ACHA,AENA,ALeA;AnB0DA,ADGA,AU9BA,AGTA,AFMA,AU9BA,AJYA,AKfA,ACHA,AENA,ALeA;AnB0DA,ADGA,AU9BA,AGTA,AFMA,AU9BA,AJYA,AKfA,ACHA,AENA,ALeA;AnB0DA,ADGA,AU9BA,AGTA,AFMA,AU9BA,AJYA,AKfA,ACHA,AENA,ALeA;AnB0DA,ADGA,AU9BA,AGTA,AFMA,AU9BA,AJYA,AKfA,ACHA,AENA,ALeA;AnB0DA,ADGA,AU9BA,AGTA,AFMA,AU9BA,AJYA,AKfA,ACHA,AENA,ALeA;AnB0DA,ADGA,AU9BA,AGTA,AFMA,AU9BA,AJYA,AKfA,ACHA,AENA,ALeA;AnB0DA,ADGA,AU9BA,AGTA,AFMA,AU9BA,AJYA,AKfA,ACHA,AENA,ALeA;AnB0DA,ADGA,AU9BA,AGTA,AFMA,AU9BA,AJYA,AKfA,ACHA,AENA,ALeA;AnB0DA,ADGA,AU9BA,AGTA,AFMA,AU9BA,AJYA,AKfA,ACHA,AENA,ALeA;AnB0DA,ADGA,AU9BA,AGTA,AFMA,AU9BA,AJYA,AKfA,ACHA,AENA,ALeA;AnB0DA,ADGA,AU9BA,AGTA,AFMA,AU9BA,AJYA,AKfA,ACHA,AENA,ALeA;AnB0DA,ADGA,AU9BA,AGTA,AFMA,AU9BA,AJYA,AKfA,ACHA,AENA,ALeA;AnB0DA,ADGA,AU9BA,AGTA,AFMA,AU9BA,AJYA,AKfA,ACHA,AENA;AxByEA,ADGA,AU9BA,AGTA,AFMA,AU9BA,AJYA,AKfA,ACHA,AENA;AxByEA,ADGA,AU9BA,AGTA,AFMA,AU9BA,AJYA,AKfA,ACHA,AENA;AxByEA,ADGA,AU9BA,AGTA,AFMA,AU9BA,AJYA,AKfA,ACHA,AENA;AxByEA,ADGA,AU9BA,AGTA,AFMA,AU9BA,AJYA,AKfA,ACHA,AENA;AxByEA,ADGA,AU9BA,AGTA,AFMA,AU9BA,AJYA,AKfA,ACHA,AENA;AxByEA,ADGA,AU9BA,AGTA,AFMA,AU9BA,AJYA,AKfA,ACHA,AENA;AxByEA,ADGA,AU9BA,AGTA,AFMA,AU9BA,AJYA,AKfA,ACHA,AENA;AxByEA,ADGA,AU9BA,AGTA,AFMA,AU9BA,AJYA,AKfA,ACHA,AENA;AxByEA,ADGA,AU9BA,AGTA,AFMA,AU9BA,AJYA,AKfA,ACHA,AENA;AxByEA,ADGA,AU9BA,AGTA,AFMA,AU9BA,AJYA,AKfA,ACHA,AENA;AxByEA,ADGA,AU9BA,AGTA,AFMA,AU9BA,AJYA,AKfA,ACHA,AENA;AxByEA,ADGA,AU9BA,AGTA,AFMA,AU9BA,AJYA,AKfA,ACHA,AENA;AxByEA,ADGA,AU9BA,AGTA,AFMA,AU9BA,AJYA,AKfA,ACHA,AENA;AxByEA,ADGA,AU9BA,AGTA,AFMA,AU9BA,AJYA,AKfA,ACHA,AENA;AxByEA,ADGA,AU9BA,AGTA,AFMA,AU9BA,AJYA,AKfA,ACHA,AENA;AxByEA,ADGA,AU9BA,AGTA,AFMA,AU9BA,AJYA,AKfA,ACHA,AENA;AxByEA,ADGA,AU9BA,AGTA,AFMA,AU9BA,AJYA,AKfA,ACHA,AENA;AxByEA,ADGA,AU9BA,AGTA,AFMA,AU9BA,AJYA,AKfA,ACHA,AENA;AxByEA,ADGA,AU9BA,AGTA,AFMA,AU9BA,AJYA,AKfA,ACHA,AENA;AxByEA,ADGA,AU9BA,AGTA,AFMA,AU9BA,AJYA,AKfA,ACHA,AENA;AxByEA,ADGA,AU9BA,AGTA,AFMA,AU9BA,AJYA,AKfA,ACHA,AENA;AxByEA,ADGA,AU9BA,AGTA,AFMA,AU9BA,AJYA,AKfA,ACHA,AENA;AxByEA,ADGA,AU9BA,AGTA,AFMA,AU9BA,AJYA,AKfA,ACHA,AENA;AxByEA,ADGA,AU9BA,AGTA,AFMA,AMlBA,AKfA,ACHA,AENA;AxByEA,ADGA,AU9BA,AGTA,AFMA,AMlBA,AKfA,ACHA,AENA;AxByEA,ADGA,AU9BA,AGTA,AFMA,AMlBA,AKfA,ACHA,AENA;AxByEA,ADGA,AU9BA,AGTA,AFMA,AMlBA,AKfA,ACHA,AENA;AxByEA,ADGA,AU9BA,AGTA,AFMA,AMlBA,AKfA,ACHA,AENA;AxByEA,ADGA,AU9BA,AGTA,AFMA,AMlBA,AKfA,ACHA,AENA;AxByEA,ADGA,AU9BA,AGTA,AFMA,AMlBA,AKfA,ACHA,AENA;AxByEA,ADGA,AU9BA,AGTA,AFMA,AMlBA,AKfA,ACHA,AENA;AxByEA,ADGA,AU9BA,AGTA,AFMA,AMlBA,AKfA,ACHA,AENA;AxByEA,ADGA,AU9BA,AGTA,AFMA,AMlBA,AKfA,ACHA,AENA;AxByEA,ADGA,AU9BA,AGTA,AFMA,AMlBA,AKfA,ACHA,AENA;AxByEA,ADGA,AU9BA,AGTA,AFMA,AMlBA,AKfA,ACHA,AENA;AxByEA,ADGA,AU9BA,AGTA,AFMA,AMlBA,AKfA,ACHA,AENA;AxByEA,ADGA,AU9BA,AGTA,AFMA,AMlBA,AKfA,ACHA,AENA;AxByEA,ADGA,AU9BA,AGTA,AFMA,AMlBA,AKfA,ACHA,AENA;AxByEA,ADGA,AU9BA,AGTA,AFMA,AMlBA,AKfA,ACHA,AENA;AxByEA,ADGA,AU9BA,AGTA,AFMA,AMlBA,AKfA,ACHA,AENA;AxByEA,ADGA,AU9BA,AGTA,AFMA,AMlBA,AKfA,ACHA,AENA;AxByEA,ADGA,AU9BA,AGTA,AFMA,AMlBA,AKfA,ACHA,AENA;AxByEA,ADGA,AU9BA,AGTA,AFMA,AMlBA,AKfA,ACHA,AENA;AxByEA,ADGA,AU9BA,AGTA,AFMA,AMlBA,AKfA,ACHA,AENA;AxByEA,ADGA,AU9BA,AGTA,AFMA,AMlBA,AKfA,ACHA,AENA;AxByEA,ADGA,AU9BA,AGTA,AFMA,AMlBA,AKfA,ACHA,AENA;AxByEA,ADGA,AU9BA,AGTA,AFMA,AMlBA,AKfA,ACHA,AENA;AxByEA,ADGA,AU9BA,AGTA,AFMA,AMlBA,AKfA,ACHA,AENA;AxByEA,ADGA,AU9BA,AGTA,AFMA,AMlBA,AKfA,ACHA,AENA;AxByEA,ADGA,AU9BA,AGTA,AFMA,AMlBA,AKfA,ACHA,AENA;AxByEA,ADGA,AU9BA,AGTA,AFMA,AMlBA,AKfA,ACHA,AENA;AxByEA,ADGA,AU9BA,AGTA,AFMA,AMlBA,AKfA,ACHA,AENA;AxByEA,ADGA,AU9BA,AGTA,AFMA,AMlBA,AKfA,ACHA,AENA;AxByEA,ADGA,AU9BA,AGTA,AFMA,AMlBA,AKfA,ACHA,AENA;AxByEA,ADGA,AU9BA,AGTA,AFMA,AMlBA,AKfA,ACHA,AENA;AxByEA,ADGA,AU9BA,AGTA,AFMA,AMlBA,AKfA,ACHA,AENA;AxByEA,ADGA,AU9BA,AGTA,AFMA,AMlBA,AKfA,ACHA,AENA;AxByEA,ADGA,AU9BA,AGTA,AFMA,AMlBA,AKfA,ACHA,AENA;AxByEA,ADGA,AU9BA,AGTA,AFMA,AMlBA,AKfA,ACHA,AENA;AxByEA,ADGA,AU9BA,AGTA,AFMA,AMlBA,AKfA,ACHA,AENA;AxByEA,ADGA,AU9BA,AGTA,AFMA,AMlBA,AKfA,ACHA,AENA;AxByEA,ADGA,AU9BA,AGTA,AFMA,AMlBA,AKfA,ACHA,AENA;AxByEA,ADGA,AU9BA,AGTA,AFMA,AMlBA,AKfA,ACHA,AENA;AxByEA,ADGA,AU9BA,AGTA,AFMA,AMlBA,AKfA,ACHA,AENA;AxByEA,ADGA,AU9BA,AGTA,AFMA,AMlBA,AKfA,ACHA,AENA;AxByEA,ADGA,AU9BA,AGTA,AFMA,AMlBA,AKfA,ACHA,AENA;AxByEA,ADGA,AU9BA,AGTA,AFMA,AMlBA,AKfA,ACHA,AENA;AxByEA,ADGA,AU9BA,AGTA,AFMA,AMlBA,AKfA,ACHA,AENA;AxByEA,ADGA,AU9BA,AGTA,AFMA,AMlBA,AKfA,ACHA,AENA;AxByEA,ADGA,AU9BA,AGTA,AFMA,AMlBA,AKfA,ACHA,AENA;AxByEA,ADGA,AU9BA,AGTA,AFMA,AMlBA,AKfA,ACHA,AENA;AxByEA,ADGA,AU9BA,AGTA,AFMA,AMlBA,AKfA,ACHA,AENA;AxByEA,ADGA,AU9BA,AGTA,AFMA,AMlBA,AKfA,ACHA,AENA;AxByEA,ADGA,AU9BA,AGTA,AFMA,AMlBA,AKfA,ACHA,AENA;AxByEA,ADGA,AU9BA,AGTA,AFMA,AMlBA,AKfA,ACHA,AENA;AxByEA,ADGA,AU9BA,AGTA,AFMA,AMlBA,AKfA,ACHA,AENA;AxByEA,ADGA,AU9BA,AGTA,AFMA,AMlBA,AKfA,ACHA,AENA;AxByEA,ADGA,AU9BA,AGTA,AFMA,AMlBA,AKfA,ACHA,AENA;AxByEA,ADGA,AU9BA,AGTA,AFMA,AMlBA,AKfA,ACHA,AENA;AxByEA,ADGA,AU9BA,AGTA,AFMA,AMlBA,AKfA,ACHA,AENA;AxByEA,ADGA,AU9BA,AGTA,AFMA,AMlBA,AKfA,ACHA,AENA;AxByEA,ADGA,AU9BA,AGTA,AFMA,AMlBA,AKfA,ACHA,AENA;AxByEA,ADGA,AU9BA,AGTA,AFMA,AMlBA,AKfA,ACHA,AENA;AxByEA,ADGA,AU9BA,AGTA,AFMA,AMlBA,AKfA,ACHA,AENA;AxByEA,ADGA,AU9BA,AGTA,AFMA,AMlBA,AKfA,ACHA,AENA;AxByEA,ADGA,AU9BA,AGTA,AFMA,AMlBA,AKfA,ACHA,AENA;AxByEA,ADGA,AU9BA,AGTA,AFMA,AMlBA,AKfA,ACHA,AENA;AxByEA,ADGA,AU9BA,AGTA,AFMA,AMlBA,AKfA,ACHA,AENA;AxByEA,AS3BA,AGTA,AFMA,AMlBA,AKfA,ACHA,AENA;AxByEA,AS3BA,AGTA,AFMA,AMlBA,AKfA,ACHA,AENA;AxByEA,AS3BA,AGTA,AFMA,AMlBA,AKfA,ACHA,AENA;AxByEA,AS3BA,AGTA,AFMA,AMlBA,AKfA,ACHA,AENA;AxByEA,AS3BA,AGTA,AFMA,AMlBA,AKfA,ACHA,AENA;AxByEA,AS3BA,AGTA,AFMA,AMlBA,AKfA,ACHA,AENA;AxByEA,AS3BA,AGTA,AFMA,AMlBA,AKfA,ACHA,AENA;AxByEA,AS3BA,AGTA,AFMA,AMlBA,AKfA,ACHA,AENA;AxByEA,AS3BA,AGTA,AFMA,AMlBA,AKfA,ACHA,AENA;AxByEA,AS3BA,AGTA,AFMA,AMlBA,AMlBA,AENA;AxByEA,AS3BA,AGTA,AFMA,AMlBA,AMlBA,AENA;AxByEA,AS3BA,AGTA,AFMA,AMlBA,AMlBA,AENA;AxByEA,AS3BA,AGTA,AFMA,AMlBA,AMlBA,AENA;AxByEA,AS3BA,AGTA,AFMA,AMlBA,AMlBA,AENA;AxByEA,AS3BA,AGTA,AFMA,AMlBA,AMlBA,AENA;AxByEA,AS3BA,AGTA,AFMA,AMlBA,AMlBA,AENA;AxByEA,AS3BA,AGTA,AFMA,AMlBA,AMlBA,AENA;AxByEA,AS3BA,AGTA,AFMA,AMlBA,AMlBA,AENA;AxByEA,AS3BA,AGTA,AFMA,AMlBA,AMlBA,AENA;AxByEA,AS3BA,AGTA,AFMA,AMlBA,AMlBA,AENA;AxByEA,AS3BA,AGTA,AFMA,AMlBA,AMlBA,AENA;AxByEA,AS3BA,AGTA,AFMA,AMlBA,AMlBA,AENA;AxByEA,AS3BA,AGTA,AFMA,AMlBA,AMlBA,AENA;AxByEA,AS3BA,AGTA,AFMA,AMlBA,AMlBA,AENA;AxByEA,AS3BA,AGTA,AFMA,AMlBA,AMlBA,AENA;AxByEA,AS3BA,AGTA,AFMA,AMlBA,AMlBA,AENA;AxByEA,AS3BA,AGTA,AFMA,AMlBA,AMlBA,AENA;AxByEA,AS3BA,AGTA,AFMA,AMlBA,AMlBA,AENA;AxByEA,AS3BA,AGTA,AFMA,AMlBA,AMlBA,AENA;AxByEA,AS3BA,AGTA,AFMA,AMlBA,AMlBA,AENA;AxByEA,AS3BA,AGTA,AFMA,AMlBA,AMlBA,AENA;AxByEA,AS3BA,AGTA,AFMA,AMlBA,AMlBA,AENA;AxByEA,AS3BA,AGTA,AFMA,AMlBA,AMlBA,AENA;AxByEA,AS3BA,AGTA,AFMA,AMlBA,AMlBA,AENA;AxByEA,AS3BA,AGTA,AFMA,AMlBA,AMlBA,AENA;AxByEA,AS3BA,AGTA,AFMA,AMlBA,AMlBA,AENA;AxByEA,AS3BA,AGTA,AFMA,AMlBA,AMlBA,AENA;AxByEA,AS3BA,AGTA,AFMA,AMlBA,AMlBA,AENA;AxByEA,AS3BA,AGTA,AFMA,AMlBA,AMlBA,AENA;AxByEA,AS3BA,AGTA,AFMA,AMlBA,AMlBA,AENA;AxByEA,AS3BA,AGTA,AFMA,AMlBA,AMlBA,AENA;AxByEA,AS3BA,AGTA,AFMA,AMlBA,AMlBA,AENA;AxByEA,AS3BA,AGTA,AFMA,AMlBA,AMlBA,AENA;AxByEA,AS3BA,AGTA,AFMA,AMlBA,AMlBA,AENA;AxByEA,AS3BA,AGTA,AFMA,AMlBA,AMlBA,AENA;AxByEA,AS3BA,AGTA,AFMA,AMlBA,AMlBA,AENA;AxByEA,AS3BA,AGTA,AFMA,AMlBA,AMlBA,AENA;AxByEA,AS3BA,AGTA,AFMA,AMlBA,AMlBA,AENA;AxByEA,AS3BA,AGTA,AFMA,AMlBA,AMlBA,AENA;AxByEA,AS3BA,AGTA,AFMA,AMlBA,AMlBA,AENA;AxByEA,AS3BA,AGTA,AFMA,AMlBA,AMlBA,AENA;AxByEA,AS3BA,AGTA,AFMA,AMlBA,AMlBA,AENA;AxByEA,AS3BA,AGTA,AFMA,AMlBA,AMlBA,AENA;AxByEA,AS3BA,AGTA,AFMA,AMlBA,AMlBA,AENA;AxByEA,AS3BA,AGTA,AFMA,AMlBA,AMlBA,AENA;AxByEA,AS3BA,AGTA,AFMA,AMlBA,AMlBA,AENA;AxByEA,AS3BA,AGTA,AFMA,AMlBA,AMlBA,AENA;AxByEA,AS3BA,AGTA,AFMA,AMlBA,AMlBA,AENA;AxByEA,AS3BA,AGTA,AFMA,AMlBA,AMlBA,AENA;AxByEA,AS3BA,AGTA,AFMA,AMlBA,AMlBA,AENA;AxByEA,AS3BA,AGTA,AFMA,AMlBA,AMlBA,AENA;AxByEA,AS3BA,AGTA,AFMA,AMlBA,AMlBA,AENA;AxByEA,AS3BA,AGTA,AFMA,AMlBA,AMlBA,AENA;AxByEA,AS3BA,AGTA,AFMA,AMlBA,AMlBA,AENA;AxByEA,AS3BA,AGTA,AFMA,AMlBA,AMlBA,AENA;AxByEA,AS3BA,AGTA,AFMA,AMlBA,AMlBA,AENA;AxByEA,AS3BA,AGTA,AFMA,AMlBA,AMlBA,AENA;AxByEA,AS3BA,AGTA,AFMA,AMlBA,AMlBA,AENA;AxByEA,AS3BA,AGTA,AFMA,AMlBA,AMlBA,AENA;AxByEA,AS3BA,AGTA,AFMA,AMlBA,AMlBA,AENA;AxByEA,AS3BA,AGTA,AFMA,AMlBA,AMlBA,AENA;AxByEA,AS3BA,AGTA,AFMA,AMlBA,AMlBA,AENA;AxByEA,AS3BA,AGTA,AFMA,AMlBA,AMlBA,AENA;AxByEA,AS3BA,AGTA,AFMA,AMlBA,AMlBA,AENA;AxByEA,AS3BA,AGTA,AFMA,AMlBA,AMlBA,AENA;AxByEA,AS3BA,AGTA,AFMA,AMlBA,AMlBA,AENA;AxByEA,AS3BA,AGTA,AFMA,AMlBA,AMlBA,AENA;AxByEA,AS3BA,AGTA,AFMA,AMlBA,AMlBA,AENA;AxByEA,AS3BA,AGTA,AFMA,AMlBA,AMlBA,AENA;AxByEA,AS3BA,AGTA,AFMA,AMlBA,AMlBA,AENA;AxByEA,AS3BA,AGTA,AFMA,AMlBA,AMlBA,AENA;AxByEA,AS3BA,AGTA,AFMA,AMlBA,AMlBA,AENA;AxByEA,AS3BA,AGTA,AFMA,AMlBA,AMlBA,AENA;AxByEA,AS3BA,AGTA,AFMA,AMlBA,AMlBA,AENA;AxByEA,AS3BA,AGTA,AFMA,AMlBA,AMlBA,AENA;AxByEA,AS3BA,AGTA,AFMA,AMlBA,AMlBA,AENA;AxByEA,AS3BA,AGTA,AFMA,AMlBA,AMlBA,AENA;AxByEA,AS3BA,AGTA,AFMA,AMlBA,AMlBA,AENA;AxByEA,AS3BA,AGTA,AFMA,AMlBA,AMlBA,AENA;AxByEA,AS3BA,AGTA,AFMA,AMlBA,AMlBA,AENA;AxByEA,AS3BA,AGTA,AFMA,AMlBA,AMlBA,AENA;AxByEA,AS3BA,AGTA,AFMA,AMlBA,AMlBA,AENA;AxByEA,AS3BA,AGTA,AFMA,AMlBA,AMlBA,AENA;AxByEA,AS3BA,AGTA,AFMA,AMlBA,AMlBA,AENA;AxByEA,AS3BA,AGTA,AFMA,AMlBA,AMlBA,AENA;AxByEA,AS3BA,AGTA,AFMA,AMlBA,AMlBA,AENA;AxByEA,AS3BA,AGTA,AFMA,AMlBA,AMlBA,AENA;AxByEA,AS3BA,AGTA,AFMA,AMlBA,AMlBA,AENA;AxByEA,AS3BA,AGTA,AFMA,AMlBA,AMlBA,AENA;AxByEA,AS3BA,AGTA,AFMA,AYpCA,AENA;AxByEA,AS3BA,AGTA,AFMA,AYpCA,AENA;AxByEA,AS3BA,AGTA,AFMA,AYpCA,AENA;AxByEA,AS3BA,AGTA,AFMA,AYpCA,AENA;AxByEA,AS3BA,AGTA,AFMA,AYpCA,AENA;AxByEA,AS3BA,AGTA,AFMA,AYpCA,AENA;AxByEA,AS3BA,AGTA,AFMA,AYpCA,AENA;AxByEA,AS3BA,AGTA,AFMA,AYpCA,AENA;AxByEA,AS3BA,AGTA,AFMA,AYpCA,AENA;AxByEA,AS3BA,AGTA,AFMA,AYpCA,AENA;AxByEA,AS3BA,AGTA,AFMA,AYpCA,AENA;AxByEA,AS3BA,AGTA,AFMA,AYpCA,AENA;AxByEA,AS3BA,AGTA,AFMA,AYpCA,AENA;AxByEA,AS3BA,AGTA,AFMA,AYpCA,AENA;AxByEA,AS3BA,AGTA,AFMA,AYpCA,AENA;AxByEA,AS3BA,AGTA,AFMA,AYpCA,AENA;AxByEA,AS3BA,AGTA,AFMA,AYpCA,AENA;AxByEA,AS3BA,AGTA,AFMA,AYpCA,AENA;AxByEA,AS3BA,AGTA,AFMA,AYpCA,AENA;AxByEA,AS3BA,AGTA,AFMA,AYpCA,AENA;AxByEA,AS3BA,AGTA,AFMA,AYpCA,AENA;AxByEA,AS3BA,AGTA,AFMA,AYpCA,AENA;AxByEA,AS3BA,AGTA,AFMA,AYpCA,AENA;AxByEA,AS3BA,AGTA,AFMA,AYpCA,AENA;AxByEA,AS3BA,AGTA,AFMA,AYpCA,AENA;AxByEA,AS3BA,AGTA,AFMA,AYpCA,AENA;AxByEA,AS3BA,AGTA,AFMA,AYpCA,AENA;AxByEA,AS3BA,AGTA,AFMA,AYpCA,AENA;AxByEA,AS3BA,AGTA,AFMA,AYpCA,AENA;AxByEA,AS3BA,AGTA,AFMA,AYpCA,AENA;AxByEA,AS3BA,AGTA,AFMA,AYpCA,AENA;AxByEA,AS3BA,AGTA,AFMA,AYpCA,AENA;AxByEA,AS3BA,AGTA,AFMA,AYpCA,AENA;AxByEA,AS3BA,AGTA,AFMA,AYpCA,AENA;AxByEA,AS3BA,AGTA,AFMA,AYpCA,AENA;AxByEA,AS3BA,AGTA,AFMA,AYpCA,AENA;AxByEA,AS3BA,AGTA,AFMA,AYpCA,AENA;AxByEA,AS3BA,AGTA,AFMA,AYpCA,AENA;AxByEA,AS3BA,AGTA,AFMA,AYpCA,AENA;AxByEA,AS3BA,AGTA,AFMA,AYpCA,AENA;AxByEA,AS3BA,AGTA,AFMA,AYpCA,AENA;AxByEA,AS3BA,AGTA,AFMA,AYpCA,AENA;AxByEA,AS3BA,AGTA,AFMA,AYpCA,AENA;AxByEA,AS3BA,AGTA,AFMA,AYpCA,AENA;AxByEA,AS3BA,AGTA,AFMA,AYpCA,AENA;AxByEA,AS3BA,AGTA,AFMA,AYpCA,AENA;AxByEA,AS3BA,AGTA,AFMA,AYpCA,AENA;AxByEA,AS3BA,AGTA,AFMA,AYpCA,AENA;AxByEA,AS3BA,AGTA,AFMA,AYpCA,AENA;AxByEA,AS3BA,AGTA,AFMA,AYpCA,AENA;AxByEA,AS3BA,AGTA,AFMA,AYpCA,AENA;AxByEA,AS3BA,AGTA,AFMA,AYpCA,AENA;AxByEA,AS3BA,AGTA,AFMA,AYpCA,AENA;AxByEA,AS3BA,AGTA,AFMA,AYpCA,AENA;AxByEA,AS3BA,AGTA,AFMA,AYpCA,AENA;AxByEA,AS3BA,AGTA,AFMA,AYpCA,AENA;AxByEA,AS3BA,AGTA,AFMA,AYpCA,AENA;AxByEA,AS3BA,AGTA,AFMA,AYpCA,AENA;AxByEA,AS3BA,AGTA,AFMA,AYpCA,AENA;AxByEA,AS3BA,AGTA,AFMA,AYpCA,AENA;AxByEA,AS3BA,AGTA,AFMA,AYpCA,AENA;AxByEA,AS3BA,AGTA,AFMA,AYpCA,AENA;AxByEA,AS3BA,AGTA,AFMA,AYpCA,AENA;AxByEA,AS3BA,AGTA,AFMA,AYpCA,AENA;AxByEA,AS3BA,AGTA,AFMA,AYpCA,AENA;AxByEA,AS3BA,AGTA,AFMA,AYpCA,AENA;AxByEA,AS3BA,AGTA,AFMA,AYpCA,AENA;AxByEA,AS3BA,AGTA,AFMA,AYpCA,AENA;AxByEA,AS3BA,AGTA,AFMA,AYpCA,AENA;AxByEA,AS3BA,AGTA,AFMA,AYpCA,AENA;AxByEA,AS3BA,AGTA,AFMA,AYpCA,AENA;AxByEA,AS3BA,AGTA,AFMA,AYpCA,AENA;AxByEA,AS3BA,AGTA,AFMA,AYpCA,AENA;AxByEA,AS3BA,AGTA,AFMA,AYpCA,AENA;AxByEA,AS3BA,AGTA,AFMA,AYpCA,AENA;AxByEA,AS3BA,AGTA,AFMA,AYpCA,AENA;AxByEA,AS3BA,AGTA,AFMA,AYpCA,AENA;AxByEA,AS3BA,AGTA,AFMA,AYpCA,AENA;AxByEA,AS3BA,AGTA,AFMA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Af8CA,ACHA,AYpCA,AENA;Ad2CA,AYpCA,AENA;Ad2CA,AYpCA,AENA;Ad2CA,AYpCA,AENA;Ad2CA,AYpCA,AENA;Ad2CA,AYpCA,AENA;Ad2CA,AYpCA,AENA;Ad2CA,AYpCA,AENA;Ad2CA,AYpCA,AENA;Ad2CA,AYpCA,AENA;Ad2CA,AYpCA,AENA;Ad2CA,AYpCA,AENA;Ad2CA,AYpCA,AENA;Ad2CA,AYpCA,AENA;Ad2CA,AYpCA,AENA;Ad2CA,AYpCA,AENA;Ad2CA,AYpCA,AENA;Ad2CA,AYpCA,AENA;Ad2CA,AYpCA,AENA;Ad2CA,AYpCA,AENA;Ad2CA,AYpCA,AENA;Ad2CA,AYpCA,AENA;Ad2CA,AYpCA,AENA;Ad2CA,AYpCA,AENA;Ad2CA,AYpCA,AENA;Ad2CA,AYpCA,AENA;Ad2CA,AYpCA,AENA;Ad2CA,AYpCA,AENA;Ad2CA,AYpCA,AENA;Ad2CA,AYpCA,AENA;Ad2CA,AYpCA,AENA;Ad2CA,AYpCA,AENA;Ad2CA,AYpCA,AENA;Ad2CA,AYpCA,AENA;Ad2CA,AYpCA,AENA;Ad2CA,AYpCA,AENA;Ad2CA,Ac1CA;Ad2CA,Ac1CA;Ad2CA,Ac1CA;Ad2CA,Ac1CA;Ad2CA,Ac1CA;Ad2CA,Ac1CA;Ad2CA,Ac1CA;Ad2CA,Ac1CA;Ad2CA,Ac1CA;Ad2CA,Ac1CA;Ad2CA,Ac1CA;Ad2CA,Ac1CA;Ad2CA,Ac1CA;Ad2CA,Ac1CA;Ad2CA,Ac1CA;Ad2CA,Ac1CA;Ad2CA,Ac1CA;Ad2CA,Ac1CA;Ad2CA,Ac1CA;Ad2CA,Ac1CA;Ad2CA,Ac1CA;Ad2CA,Ac1CA;Ad2CA,Ac1CA;Ad2CA,Ac1CA;Ad2CA,Ac1CA;Ad2CA,Ac1CA;Ad2CA,Ac1CA;Ad2CA,Ac1CA;Ad2CA,Ac1CA;Ad2CA,Ac1CA;Ad2CA,Ac1CA;Ad2CA,Ac1CA;Ad2CA,Ac1CA;Ad2CA,Ac1CA;Ad2CA,Ac1CA;Ad2CA,Ac1CA;Ad2CA,Ac1CA;Ad2CA,Ac1CA;Ad2CA,Ac1CA;Ad2CA,Ac1CA;Ad2CA,Ac1CA;Ad2CA,Ac1CA;Ad2CA,Ac1CA;Ad2CA,Ac1CA;Ad2CA,Ac1CA;Ad2CA,Ac1CA;Ad2CA,Ac1CA;Ad2CA,Ac1CA;Ad2CA,Ac1CA;Ad2CA,Ac1CA;Ad2CA,Ac1CA;Ad2CA,Ac1CA;Ad2CA,Ac1CA;Ad2CA,Ac1CA;Ad2CA,Ac1CA;Ad2CA,Ac1CA;Ad2CA,Ac1CA;Ad2CA,Ac1CA;Ad2CA,Ac1CA;Ad2CA,Ac1CA;Ad2CA,Ac1CA;Ad2CA,Ac1CA;Ad2CA,Ac1CA;Ad2CA,Ac1CA;Ad2CA,Ac1CA;Ad2CA,Ac1CA;Ad2CA,Ac1CA;Ad2CA,Ac1CA;Ad2CA,Ac1CA;Ad2CA,Ac1CA;Ad2CA,Ac1CA;Ad2CA,Ac1CA;Ad2CA,Ac1CA;Ad2CA,Ac1CA;Ad2CA,Ac1CA;Ad2CA,Ac1CA;Ad2CA,Ac1CA;Ad2CA,Ac1CA;Ad2CA,Ac1CA;Ad2CA,Ac1CA;Ad2CA,Ac1CA;Ad2CA,Ac1CA;Ad2CA,Ac1CA;Ad2CA,Ac1CA;Ad2CA,Ac1CA;Ad2CA,Ac1CA;Ad2CA,Ac1CA;Ad2CA,Ac1CA;Ad2CA,Ac1CA;Ad2CA,Ac1CA;Ad2CA,Ac1CA;Ad2CA,Ac1CA;Ad2CA,Ac1CA;Ad2CA,Ac1CA;Ad2CA,Ac1CA;Ad2CA,Ac1CA;Ad2CA,Ac1CA;Ad2CA,Ac1CA;Ad2CA,Ac1CA;Ad2CA,Ac1CA;Ad2CA,Ac1CA;Ad2CA,Ac1CA;Ad2CA,Ac1CA;Ad2CA,Ac1CA;Ad2CA,Ac1CA;Ad2CA,Ac1CA;Ad2CA,Ac1CA;Ad2CA,Ac1CA;Ad2CA,Ac1CA;Ad2CA,Ac1CA;Ad2CA,Ac1CA;Ad2CA,Ac1CA;Ad2CA,Ac1CA;Ad2CA,Ac1CA;Ad2CA,Ac1CA;Ad2CA,Ac1CA;Ad2CA,Ac1CA;Ad2CA,Ac1CA;Ad2CA,Ac1CA;Ad2CA,Ac1CA;Ad2CA,Ac1CA;Ad2CA,Ac1CA;Ad2CA,Ac1CA;Ad2CA,Ac1CA;Ad2CA,Ac1CA;Ad2CA,Ac1CA;Ad2CA,Ac1CA;Ad2CA,Ac1CA;Ad2CA,Ac1CA;Ad2CA,Ac1CA;Ad2CA,Ac1CA;Ad2CA,Ac1CA;Ad2CA,Ac1CA;Ad2CA,Ac1CA;Ad2CA,Ac1CA;Ad2CA,Ac1CA;Ad2CA,Ac1CA;Ad2CA,Ac1CA;Ad2CA,Ac1CA;Ad2CA,Ac1CA;Ad2CA,Ac1CA;Ad2CA,Ac1CA;Ad2CA,Ac1CA;Ad2CA,Ac1CA;Ad2CA,Ac1CA;Ad2CA,Ac1CA;Ad2CA,Ac1CA;Ad2CA,Ac1CA;Ad2CA,Ac1CA;Ad2CA,Ac1CA;Ad2CA,Ac1CA;Ad2CA,Ac1CA;Ad2CA,Ac1CA;Ad2CA,Ac1CA;Ad2CA,Ac1CA;Ad2CA,Ac1CA;Ad2CA,Ac1CA;Ad2CA,Ac1CA;Ad2CA,Ac1CA;Ad2CA,Ac1CA;Ad2CA,Ac1CA;Ad2CA,Ac1CA;Ad2CA,Ac1CA;Ad2CA,Ac1CA;Ad2CA,Ac1CA;Ad2CA,Ac1CA;Ad2CA,Ac1CA;Ad2CA,Ac1CA;Ad2CA,Ac1CA;Ad2CA,Ac1CA;Ad2CA,Ac1CA;Ad2CA,Ac1CA;Ad2CA,Ac1CA;Ad2CA,Ac1CA;Ad2CA,Ac1CA;Ad2CA,Ac1CA;Ad2CA,Ac1CA;Ad2CA,Ac1CA;Ad2CA,Ac1CA;Ad2CA,Ac1CA;Ad2CA,Ac1CA;Ad2CA,Ac1CA;Ad2CA,Ac1CA;Ad2CA,Ac1CA;Ad2CA,Ac1CA;Ad2CA,Ac1CA;Ad2CA,Ac1CA;Ad2CA,Ac1CA;Ad2CA,Ac1CA;Ad2CA,Ac1CA;Ad2CA,Ac1CA;Ad2CA,Ac1CA;Ad2CA,Ac1CA;Ad2CA,Ac1CA;Ad2CA,Ac1CA;Ad2CA,Ac1CA;Ad2CA,Ac1CA;Ad2CA,Ac1CA;Ad2CA,Ac1CA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.js", "sourcesContent": ["\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.BSONRegExp = exports.MaxKey = exports.MinKey = exports.Int32 = exports.Double = exports.Timestamp = exports.Long = exports.UUID = exports.ObjectId = exports.Binary = exports.DBRef = exports.BSONSymbol = exports.Map = exports.Code = exports.LongWithoutOverridesClass = exports.EJSON = exports.BSON_INT64_MIN = exports.BSON_INT64_MAX = exports.BSON_INT32_MIN = exports.BSON_INT32_MAX = exports.BSON_DATA_UNDEFINED = exports.BSON_DATA_TIMESTAMP = exports.BSON_DATA_SYMBOL = exports.BSON_DATA_STRING = exports.BSON_DATA_REGEXP = exports.BSON_DATA_OID = exports.BSON_DATA_OBJECT = exports.BSON_DATA_NUMBER = exports.BSON_DATA_NULL = exports.BSON_DATA_MIN_KEY = exports.BSON_DATA_MAX_KEY = exports.BSON_DATA_LONG = exports.BSON_DATA_INT = exports.BSON_DATA_DECIMAL128 = exports.BSON_DATA_DBPOINTER = exports.BSON_DATA_DATE = exports.BSON_DATA_CODE_W_SCOPE = exports.BSON_DATA_CODE = exports.BSON_DATA_BOOLEAN = exports.BSON_DATA_BINARY = exports.BSON_DATA_ARRAY = exports.BSON_BINARY_SUBTYPE_COLUMN = exports.BSON_BINARY_SUBTYPE_ENCRYPTED = exports.BSON_BINARY_SUBTYPE_UUID_NEW = exports.BSON_BINARY_SUBTYPE_UUID = exports.BSON_BINARY_SUBTYPE_USER_DEFINED = exports.BSON_BINARY_SUBTYPE_MD5 = exports.BSON_BINARY_SUBTYPE_FUNCTION = exports.BSON_BINARY_SUBTYPE_DEFAULT = exports.BSON_BINARY_SUBTYPE_BYTE_ARRAY = void 0;\nexports.deserializeStream = exports.calculateObjectSize = exports.deserialize = exports.serializeWithBufferAndIndex = exports.serialize = exports.setInternalBufferSize = exports.BSONTypeError = exports.BSONError = exports.ObjectID = exports.Decimal128 = void 0;\nvar buffer_1 = require(\"buffer\");\nvar binary_1 = require(\"./binary\");\nObject.defineProperty(exports, \"Binary\", { enumerable: true, get: function () { return binary_1.Binary; } });\nObject.defineProperty(exports, \"UUID\", { enumerable: true, get: function () { return binary_1.UUID; } });\nvar code_1 = require(\"./code\");\nObject.defineProperty(exports, \"Code\", { enumerable: true, get: function () { return code_1.Code; } });\nvar db_ref_1 = require(\"./db_ref\");\nObject.defineProperty(exports, \"DBRef\", { enumerable: true, get: function () { return db_ref_1.DBRef; } });\nvar decimal128_1 = require(\"./decimal128\");\nObject.defineProperty(exports, \"Decimal128\", { enumerable: true, get: function () { return decimal128_1.Decimal128; } });\nvar double_1 = require(\"./double\");\nObject.defineProperty(exports, \"Double\", { enumerable: true, get: function () { return double_1.Double; } });\nvar ensure_buffer_1 = require(\"./ensure_buffer\");\nvar extended_json_1 = require(\"./extended_json\");\nvar int_32_1 = require(\"./int_32\");\nObject.defineProperty(exports, \"Int32\", { enumerable: true, get: function () { return int_32_1.Int32; } });\nvar long_1 = require(\"./long\");\nObject.defineProperty(exports, \"Long\", { enumerable: true, get: function () { return long_1.Long; } });\nvar map_1 = require(\"./map\");\nObject.defineProperty(exports, \"Map\", { enumerable: true, get: function () { return map_1.Map; } });\nvar max_key_1 = require(\"./max_key\");\nObject.defineProperty(exports, \"MaxKey\", { enumerable: true, get: function () { return max_key_1.MaxKey; } });\nvar min_key_1 = require(\"./min_key\");\nObject.defineProperty(exports, \"MinKey\", { enumerable: true, get: function () { return min_key_1.MinKey; } });\nvar objectid_1 = require(\"./objectid\");\nObject.defineProperty(exports, \"ObjectId\", { enumerable: true, get: function () { return objectid_1.ObjectId; } });\nObject.defineProperty(exports, \"ObjectID\", { enumerable: true, get: function () { return objectid_1.ObjectId; } });\nvar error_1 = require(\"./error\");\nvar calculate_size_1 = require(\"./parser/calculate_size\");\n// Parts of the parser\nvar deserializer_1 = require(\"./parser/deserializer\");\nvar serializer_1 = require(\"./parser/serializer\");\nvar regexp_1 = require(\"./regexp\");\nObject.defineProperty(exports, \"BSONRegExp\", { enumerable: true, get: function () { return regexp_1.BSONRegExp; } });\nvar symbol_1 = require(\"./symbol\");\nObject.defineProperty(exports, \"BSONSymbol\", { enumerable: true, get: function () { return symbol_1.BSONSymbol; } });\nvar timestamp_1 = require(\"./timestamp\");\nObject.defineProperty(exports, \"Timestamp\", { enumerable: true, get: function () { return timestamp_1.Timestamp; } });\nvar constants_1 = require(\"./constants\");\nObject.defineProperty(exports, \"BSON_BINARY_SUBTYPE_BYTE_ARRAY\", { enumerable: true, get: function () { return constants_1.BSON_BINARY_SUBTYPE_BYTE_ARRAY; } });\nObject.defineProperty(exports, \"BSON_BINARY_SUBTYPE_DEFAULT\", { enumerable: true, get: function () { return constants_1.BSON_BINARY_SUBTYPE_DEFAULT; } });\nObject.defineProperty(exports, \"BSON_BINARY_SUBTYPE_FUNCTION\", { enumerable: true, get: function () { return constants_1.BSON_BINARY_SUBTYPE_FUNCTION; } });\nObject.defineProperty(exports, \"BSON_BINARY_SUBTYPE_MD5\", { enumerable: true, get: function () { return constants_1.BSON_BINARY_SUBTYPE_MD5; } });\nObject.defineProperty(exports, \"BSON_BINARY_SUBTYPE_USER_DEFINED\", { enumerable: true, get: function () { return constants_1.BSON_BINARY_SUBTYPE_USER_DEFINED; } });\nObject.defineProperty(exports, \"BSON_BINARY_SUBTYPE_UUID\", { enumerable: true, get: function () { return constants_1.BSON_BINARY_SUBTYPE_UUID; } });\nObject.defineProperty(exports, \"BSON_BINARY_SUBTYPE_UUID_NEW\", { enumerable: true, get: function () { return constants_1.BSON_BINARY_SUBTYPE_UUID_NEW; } });\nObject.defineProperty(exports, \"BSON_BINARY_SUBTYPE_ENCRYPTED\", { enumerable: true, get: function () { return constants_1.BSON_BINARY_SUBTYPE_ENCRYPTED; } });\nObject.defineProperty(exports, \"BSON_BINARY_SUBTYPE_COLUMN\", { enumerable: true, get: function () { return constants_1.BSON_BINARY_SUBTYPE_COLUMN; } });\nObject.defineProperty(exports, \"BSON_DATA_ARRAY\", { enumerable: true, get: function () { return constants_1.BSON_DATA_ARRAY; } });\nObject.defineProperty(exports, \"BSON_DATA_BINARY\", { enumerable: true, get: function () { return constants_1.BSON_DATA_BINARY; } });\nObject.defineProperty(exports, \"BSON_DATA_BOOLEAN\", { enumerable: true, get: function () { return constants_1.BSON_DATA_BOOLEAN; } });\nObject.defineProperty(exports, \"BSON_DATA_CODE\", { enumerable: true, get: function () { return constants_1.BSON_DATA_CODE; } });\nObject.defineProperty(exports, \"BSON_DATA_CODE_W_SCOPE\", { enumerable: true, get: function () { return constants_1.BSON_DATA_CODE_W_SCOPE; } });\nObject.defineProperty(exports, \"BSON_DATA_DATE\", { enumerable: true, get: function () { return constants_1.BSON_DATA_DATE; } });\nObject.defineProperty(exports, \"BSON_DATA_DBPOINTER\", { enumerable: true, get: function () { return constants_1.BSON_DATA_DBPOINTER; } });\nObject.defineProperty(exports, \"BSON_DATA_DECIMAL128\", { enumerable: true, get: function () { return constants_1.BSON_DATA_DECIMAL128; } });\nObject.defineProperty(exports, \"BSON_DATA_INT\", { enumerable: true, get: function () { return constants_1.BSON_DATA_INT; } });\nObject.defineProperty(exports, \"BSON_DATA_LONG\", { enumerable: true, get: function () { return constants_1.BSON_DATA_LONG; } });\nObject.defineProperty(exports, \"BSON_DATA_MAX_KEY\", { enumerable: true, get: function () { return constants_1.BSON_DATA_MAX_KEY; } });\nObject.defineProperty(exports, \"BSON_DATA_MIN_KEY\", { enumerable: true, get: function () { return constants_1.BSON_DATA_MIN_KEY; } });\nObject.defineProperty(exports, \"BSON_DATA_NULL\", { enumerable: true, get: function () { return constants_1.BSON_DATA_NULL; } });\nObject.defineProperty(exports, \"BSON_DATA_NUMBER\", { enumerable: true, get: function () { return constants_1.BSON_DATA_NUMBER; } });\nObject.defineProperty(exports, \"BSON_DATA_OBJECT\", { enumerable: true, get: function () { return constants_1.BSON_DATA_OBJECT; } });\nObject.defineProperty(exports, \"BSON_DATA_OID\", { enumerable: true, get: function () { return constants_1.BSON_DATA_OID; } });\nObject.defineProperty(exports, \"BSON_DATA_REGEXP\", { enumerable: true, get: function () { return constants_1.BSON_DATA_REGEXP; } });\nObject.defineProperty(exports, \"BSON_DATA_STRING\", { enumerable: true, get: function () { return constants_1.BSON_DATA_STRING; } });\nObject.defineProperty(exports, \"BSON_DATA_SYMBOL\", { enumerable: true, get: function () { return constants_1.BSON_DATA_SYMBOL; } });\nObject.defineProperty(exports, \"BSON_DATA_TIMESTAMP\", { enumerable: true, get: function () { return constants_1.BSON_DATA_TIMESTAMP; } });\nObject.defineProperty(exports, \"BSON_DATA_UNDEFINED\", { enumerable: true, get: function () { return constants_1.BSON_DATA_UNDEFINED; } });\nObject.defineProperty(exports, \"BSON_INT32_MAX\", { enumerable: true, get: function () { return constants_1.BSON_INT32_MAX; } });\nObject.defineProperty(exports, \"BSON_INT32_MIN\", { enumerable: true, get: function () { return constants_1.BSON_INT32_MIN; } });\nObject.defineProperty(exports, \"BSON_INT64_MAX\", { enumerable: true, get: function () { return constants_1.BSON_INT64_MAX; } });\nObject.defineProperty(exports, \"BSON_INT64_MIN\", { enumerable: true, get: function () { return constants_1.BSON_INT64_MIN; } });\nvar extended_json_2 = require(\"./extended_json\");\nObject.defineProperty(exports, \"EJSON\", { enumerable: true, get: function () { return extended_json_2.EJSON; } });\nvar timestamp_2 = require(\"./timestamp\");\nObject.defineProperty(exports, \"LongWithoutOverridesClass\", { enumerable: true, get: function () { return timestamp_2.LongWithoutOverridesClass; } });\nvar error_2 = require(\"./error\");\nObject.defineProperty(exports, \"BSONError\", { enumerable: true, get: function () { return error_2.BSONError; } });\nObject.defineProperty(exports, \"BSONTypeError\", { enumerable: true, get: function () { return error_2.BSONTypeError; } });\n/** @internal */\n// Default Max Size\nvar MAXSIZE = 1024 * 1024 * 17;\n// Current Internal Temporary Serialization Buffer\nvar buffer = buffer_1.Buffer.alloc(MAXSIZE);\n/**\n * Sets the size of the internal serialization buffer.\n *\n * @param size - The desired size for the internal serialization buffer\n * @public\n */\nfunction setInternalBufferSize(size) {\n    // Resize the internal serialization buffer if needed\n    if (buffer.length < size) {\n        buffer = buffer_1.Buffer.alloc(size);\n    }\n}\nexports.setInternalBufferSize = setInternalBufferSize;\n/**\n * Serialize a Javascript object.\n *\n * @param object - the Javascript object to serialize.\n * @returns Buffer object containing the serialized object.\n * @public\n */\nfunction serialize(object, options) {\n    if (options === void 0) { options = {}; }\n    // Unpack the options\n    var checkKeys = typeof options.checkKeys === 'boolean' ? options.checkKeys : false;\n    var serializeFunctions = typeof options.serializeFunctions === 'boolean' ? options.serializeFunctions : false;\n    var ignoreUndefined = typeof options.ignoreUndefined === 'boolean' ? options.ignoreUndefined : true;\n    var minInternalBufferSize = typeof options.minInternalBufferSize === 'number' ? options.minInternalBufferSize : MAXSIZE;\n    // Resize the internal serialization buffer if needed\n    if (buffer.length < minInternalBufferSize) {\n        buffer = buffer_1.Buffer.alloc(minInternalBufferSize);\n    }\n    // Attempt to serialize\n    var serializationIndex = (0, serializer_1.serializeInto)(buffer, object, checkKeys, 0, 0, serializeFunctions, ignoreUndefined, []);\n    // Create the final buffer\n    var finishedBuffer = buffer_1.Buffer.alloc(serializationIndex);\n    // Copy into the finished buffer\n    buffer.copy(finishedBuffer, 0, 0, finishedBuffer.length);\n    // Return the buffer\n    return finishedBuffer;\n}\nexports.serialize = serialize;\n/**\n * Serialize a Javascript object using a predefined Buffer and index into the buffer,\n * useful when pre-allocating the space for serialization.\n *\n * @param object - the Javascript object to serialize.\n * @param finalBuffer - the Buffer you pre-allocated to store the serialized BSON object.\n * @returns the index pointing to the last written byte in the buffer.\n * @public\n */\nfunction serializeWithBufferAndIndex(object, finalBuffer, options) {\n    if (options === void 0) { options = {}; }\n    // Unpack the options\n    var checkKeys = typeof options.checkKeys === 'boolean' ? options.checkKeys : false;\n    var serializeFunctions = typeof options.serializeFunctions === 'boolean' ? options.serializeFunctions : false;\n    var ignoreUndefined = typeof options.ignoreUndefined === 'boolean' ? options.ignoreUndefined : true;\n    var startIndex = typeof options.index === 'number' ? options.index : 0;\n    // Attempt to serialize\n    var serializationIndex = (0, serializer_1.serializeInto)(buffer, object, checkKeys, 0, 0, serializeFunctions, ignoreUndefined);\n    buffer.copy(finalBuffer, startIndex, 0, serializationIndex);\n    // Return the index\n    return startIndex + serializationIndex - 1;\n}\nexports.serializeWithBufferAndIndex = serializeWithBufferAndIndex;\n/**\n * Deserialize data as BSON.\n *\n * @param buffer - the buffer containing the serialized set of BSON documents.\n * @returns returns the deserialized Javascript Object.\n * @public\n */\nfunction deserialize(buffer, options) {\n    if (options === void 0) { options = {}; }\n    return (0, deserializer_1.deserialize)(buffer instanceof buffer_1.Buffer ? buffer : (0, ensure_buffer_1.ensureBuffer)(buffer), options);\n}\nexports.deserialize = deserialize;\n/**\n * Calculate the bson size for a passed in Javascript object.\n *\n * @param object - the Javascript object to calculate the BSON byte size for\n * @returns size of BSON object in bytes\n * @public\n */\nfunction calculateObjectSize(object, options) {\n    if (options === void 0) { options = {}; }\n    options = options || {};\n    var serializeFunctions = typeof options.serializeFunctions === 'boolean' ? options.serializeFunctions : false;\n    var ignoreUndefined = typeof options.ignoreUndefined === 'boolean' ? options.ignoreUndefined : true;\n    return (0, calculate_size_1.calculateObjectSize)(object, serializeFunctions, ignoreUndefined);\n}\nexports.calculateObjectSize = calculateObjectSize;\n/**\n * Deserialize stream data as BSON documents.\n *\n * @param data - the buffer containing the serialized set of BSON documents.\n * @param startIndex - the start index in the data Buffer where the deserialization is to start.\n * @param numberOfDocuments - number of documents to deserialize.\n * @param documents - an array where to store the deserialized documents.\n * @param docStartIndex - the index in the documents array from where to start inserting documents.\n * @param options - additional options used for the deserialization.\n * @returns next index in the buffer after deserialization **x** numbers of documents.\n * @public\n */\nfunction deserializeStream(data, startIndex, numberOfDocuments, documents, docStartIndex, options) {\n    var internalOptions = Object.assign({ allowObjectSmallerThanBufferSize: true, index: 0 }, options);\n    var bufferData = (0, ensure_buffer_1.ensureBuffer)(data);\n    var index = startIndex;\n    // Loop over all documents\n    for (var i = 0; i < numberOfDocuments; i++) {\n        // Find size of the document\n        var size = bufferData[index] |\n            (bufferData[index + 1] << 8) |\n            (bufferData[index + 2] << 16) |\n            (bufferData[index + 3] << 24);\n        // Update options with index\n        internalOptions.index = index;\n        // Parse the document at this point\n        documents[docStartIndex + i] = (0, deserializer_1.deserialize)(bufferData, internalOptions);\n        // Adjust index by the document size\n        index = index + size;\n    }\n    // Return object containing end index of parsing and list of documents\n    return index;\n}\nexports.deserializeStream = deserializeStream;\n/**\n * BSON default export\n * @deprecated Please use named exports\n * @privateRemarks\n * We want to someday deprecate the default export,\n * so none of the new TS types are being exported on the default\n * @public\n */\nvar BSON = {\n    Binary: binary_1.Binary,\n    Code: code_1.Code,\n    DBRef: db_ref_1.DBRef,\n    Decimal128: decimal128_1.Decimal128,\n    Double: double_1.Double,\n    Int32: int_32_1.Int32,\n    Long: long_1.Long,\n    UUID: binary_1.UUID,\n    Map: map_1.Map,\n    MaxKey: max_key_1.MaxKey,\n    MinKey: min_key_1.MinKey,\n    ObjectId: objectid_1.ObjectId,\n    ObjectID: objectid_1.ObjectId,\n    BSONRegExp: regexp_1.BSONRegExp,\n    BSONSymbol: symbol_1.BSONSymbol,\n    Timestamp: timestamp_1.Timestamp,\n    EJSON: extended_json_1.EJSON,\n    setInternalBufferSize: setInternalBufferSize,\n    serialize: serialize,\n    serializeWithBufferAndIndex: serializeWithBufferAndIndex,\n    deserialize: deserialize,\n    calculateObjectSize: calculateObjectSize,\n    deserializeStream: deserializeStream,\n    BSONError: error_1.BSONError,\n    BSONTypeError: error_1.BSONTypeError\n};\nexports.default = BSON;\n//# sourceMappingURL=bson.js.map", "\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.UUID = exports.Binary = void 0;\nvar buffer_1 = require(\"buffer\");\nvar ensure_buffer_1 = require(\"./ensure_buffer\");\nvar uuid_utils_1 = require(\"./uuid_utils\");\nvar utils_1 = require(\"./parser/utils\");\nvar error_1 = require(\"./error\");\nvar constants_1 = require(\"./constants\");\n/**\n * A class representation of the BSON Binary type.\n * @public\n * @category BSONType\n */\nvar Binary = /** @class */ (function () {\n    /**\n     * Create a new Binary instance.\n     *\n     * This constructor can accept a string as its first argument. In this case,\n     * this string will be encoded using ISO-8859-1, **not** using UTF-8.\n     * This is almost certainly not what you want. Use `new Binary(Buffer.from(string))`\n     * instead to convert the string to a Buffer using UTF-8 first.\n     *\n     * @param buffer - a buffer object containing the binary data.\n     * @param subType - the option binary type.\n     */\n    function Binary(buffer, subType) {\n        if (!(this instanceof Binary))\n            return new Binary(buffer, subType);\n        if (!(buffer == null) &&\n            !(typeof buffer === 'string') &&\n            !ArrayBuffer.isView(buffer) &&\n            !(buffer instanceof ArrayBuffer) &&\n            !Array.isArray(buffer)) {\n            throw new error_1.BSONTypeError('Binary can only be constructed from string, Buffer, TypedArray, or Array<number>');\n        }\n        this.sub_type = subType !== null && subType !== void 0 ? subType : Binary.BSON_BINARY_SUBTYPE_DEFAULT;\n        if (buffer == null) {\n            // create an empty binary buffer\n            this.buffer = buffer_1.Buffer.alloc(Binary.BUFFER_SIZE);\n            this.position = 0;\n        }\n        else {\n            if (typeof buffer === 'string') {\n                // string\n                this.buffer = buffer_1.Buffer.from(buffer, 'binary');\n            }\n            else if (Array.isArray(buffer)) {\n                // number[]\n                this.buffer = buffer_1.Buffer.from(buffer);\n            }\n            else {\n                // Buffer | TypedArray | ArrayBuffer\n                this.buffer = (0, ensure_buffer_1.ensureBuffer)(buffer);\n            }\n            this.position = this.buffer.byteLength;\n        }\n    }\n    /**\n     * Updates this binary with byte_value.\n     *\n     * @param byteValue - a single byte we wish to write.\n     */\n    Binary.prototype.put = function (byteValue) {\n        // If it's a string and a has more than one character throw an error\n        if (typeof byteValue === 'string' && byteValue.length !== 1) {\n            throw new error_1.BSONTypeError('only accepts single character String');\n        }\n        else if (typeof byteValue !== 'number' && byteValue.length !== 1)\n            throw new error_1.BSONTypeError('only accepts single character Uint8Array or Array');\n        // Decode the byte value once\n        var decodedByte;\n        if (typeof byteValue === 'string') {\n            decodedByte = byteValue.charCodeAt(0);\n        }\n        else if (typeof byteValue === 'number') {\n            decodedByte = byteValue;\n        }\n        else {\n            decodedByte = byteValue[0];\n        }\n        if (decodedByte < 0 || decodedByte > 255) {\n            throw new error_1.BSONTypeError('only accepts number in a valid unsigned byte range 0-255');\n        }\n        if (this.buffer.length > this.position) {\n            this.buffer[this.position++] = decodedByte;\n        }\n        else {\n            var buffer = buffer_1.Buffer.alloc(Binary.BUFFER_SIZE + this.buffer.length);\n            // Combine the two buffers together\n            this.buffer.copy(buffer, 0, 0, this.buffer.length);\n            this.buffer = buffer;\n            this.buffer[this.position++] = decodedByte;\n        }\n    };\n    /**\n     * Writes a buffer or string to the binary.\n     *\n     * @param sequence - a string or buffer to be written to the Binary BSON object.\n     * @param offset - specify the binary of where to write the content.\n     */\n    Binary.prototype.write = function (sequence, offset) {\n        offset = typeof offset === 'number' ? offset : this.position;\n        // If the buffer is to small let's extend the buffer\n        if (this.buffer.length < offset + sequence.length) {\n            var buffer = buffer_1.Buffer.alloc(this.buffer.length + sequence.length);\n            this.buffer.copy(buffer, 0, 0, this.buffer.length);\n            // Assign the new buffer\n            this.buffer = buffer;\n        }\n        if (ArrayBuffer.isView(sequence)) {\n            this.buffer.set((0, ensure_buffer_1.ensureBuffer)(sequence), offset);\n            this.position =\n                offset + sequence.byteLength > this.position ? offset + sequence.length : this.position;\n        }\n        else if (typeof sequence === 'string') {\n            this.buffer.write(sequence, offset, sequence.length, 'binary');\n            this.position =\n                offset + sequence.length > this.position ? offset + sequence.length : this.position;\n        }\n    };\n    /**\n     * Reads **length** bytes starting at **position**.\n     *\n     * @param position - read from the given position in the Binary.\n     * @param length - the number of bytes to read.\n     */\n    Binary.prototype.read = function (position, length) {\n        length = length && length > 0 ? length : this.position;\n        // Let's return the data based on the type we have\n        return this.buffer.slice(position, position + length);\n    };\n    /**\n     * Returns the value of this binary as a string.\n     * @param asRaw - Will skip converting to a string\n     * @remarks\n     * This is handy when calling this function conditionally for some key value pairs and not others\n     */\n    Binary.prototype.value = function (asRaw) {\n        asRaw = !!asRaw;\n        // Optimize to serialize for the situation where the data == size of buffer\n        if (asRaw && this.buffer.length === this.position) {\n            return this.buffer;\n        }\n        // If it's a node.js buffer object\n        if (asRaw) {\n            return this.buffer.slice(0, this.position);\n        }\n        return this.buffer.toString('binary', 0, this.position);\n    };\n    /** the length of the binary sequence */\n    Binary.prototype.length = function () {\n        return this.position;\n    };\n    Binary.prototype.toJSON = function () {\n        return this.buffer.toString('base64');\n    };\n    Binary.prototype.toString = function (format) {\n        return this.buffer.toString(format);\n    };\n    /** @internal */\n    Binary.prototype.toExtendedJSON = function (options) {\n        options = options || {};\n        var base64String = this.buffer.toString('base64');\n        var subType = Number(this.sub_type).toString(16);\n        if (options.legacy) {\n            return {\n                $binary: base64String,\n                $type: subType.length === 1 ? '0' + subType : subType\n            };\n        }\n        return {\n            $binary: {\n                base64: base64String,\n                subType: subType.length === 1 ? '0' + subType : subType\n            }\n        };\n    };\n    Binary.prototype.toUUID = function () {\n        if (this.sub_type === Binary.SUBTYPE_UUID) {\n            return new UUID(this.buffer.slice(0, this.position));\n        }\n        throw new error_1.BSONError(\"Binary sub_type \\\"\".concat(this.sub_type, \"\\\" is not supported for converting to UUID. Only \\\"\").concat(Binary.SUBTYPE_UUID, \"\\\" is currently supported.\"));\n    };\n    /** @internal */\n    Binary.fromExtendedJSON = function (doc, options) {\n        options = options || {};\n        var data;\n        var type;\n        if ('$binary' in doc) {\n            if (options.legacy && typeof doc.$binary === 'string' && '$type' in doc) {\n                type = doc.$type ? parseInt(doc.$type, 16) : 0;\n                data = buffer_1.Buffer.from(doc.$binary, 'base64');\n            }\n            else {\n                if (typeof doc.$binary !== 'string') {\n                    type = doc.$binary.subType ? parseInt(doc.$binary.subType, 16) : 0;\n                    data = buffer_1.Buffer.from(doc.$binary.base64, 'base64');\n                }\n            }\n        }\n        else if ('$uuid' in doc) {\n            type = 4;\n            data = (0, uuid_utils_1.uuidHexStringToBuffer)(doc.$uuid);\n        }\n        if (!data) {\n            throw new error_1.BSONTypeError(\"Unexpected Binary Extended JSON format \".concat(JSON.stringify(doc)));\n        }\n        return type === constants_1.BSON_BINARY_SUBTYPE_UUID_NEW ? new UUID(data) : new Binary(data, type);\n    };\n    /** @internal */\n    Binary.prototype[Symbol.for('nodejs.util.inspect.custom')] = function () {\n        return this.inspect();\n    };\n    Binary.prototype.inspect = function () {\n        var asBuffer = this.value(true);\n        return \"new Binary(Buffer.from(\\\"\".concat(asBuffer.toString('hex'), \"\\\", \\\"hex\\\"), \").concat(this.sub_type, \")\");\n    };\n    /**\n     * Binary default subtype\n     * @internal\n     */\n    Binary.BSON_BINARY_SUBTYPE_DEFAULT = 0;\n    /** Initial buffer default size */\n    Binary.BUFFER_SIZE = 256;\n    /** Default BSON type */\n    Binary.SUBTYPE_DEFAULT = 0;\n    /** Function BSON type */\n    Binary.SUBTYPE_FUNCTION = 1;\n    /** Byte Array BSON type */\n    Binary.SUBTYPE_BYTE_ARRAY = 2;\n    /** Deprecated UUID BSON type @deprecated Please use SUBTYPE_UUID */\n    Binary.SUBTYPE_UUID_OLD = 3;\n    /** UUID BSON type */\n    Binary.SUBTYPE_UUID = 4;\n    /** MD5 BSON type */\n    Binary.SUBTYPE_MD5 = 5;\n    /** Encrypted BSON type */\n    Binary.SUBTYPE_ENCRYPTED = 6;\n    /** Column BSON type */\n    Binary.SUBTYPE_COLUMN = 7;\n    /** User BSON type */\n    Binary.SUBTYPE_USER_DEFINED = 128;\n    return Binary;\n}());\nexports.Binary = Binary;\nObject.defineProperty(Binary.prototype, '_bsontype', { value: 'Binary' });\nvar UUID_BYTE_LENGTH = 16;\n/**\n * A class representation of the BSON UUID type.\n * @public\n */\nvar UUID = /** @class */ (function (_super) {\n    __extends(UUID, _super);\n    /**\n     * Create an UUID type\n     *\n     * @param input - Can be a 32 or 36 character hex string (dashes excluded/included) or a 16 byte binary Buffer.\n     */\n    function UUID(input) {\n        var _this = this;\n        var bytes;\n        var hexStr;\n        if (input == null) {\n            bytes = UUID.generate();\n        }\n        else if (input instanceof UUID) {\n            bytes = buffer_1.Buffer.from(input.buffer);\n            hexStr = input.__id;\n        }\n        else if (ArrayBuffer.isView(input) && input.byteLength === UUID_BYTE_LENGTH) {\n            bytes = (0, ensure_buffer_1.ensureBuffer)(input);\n        }\n        else if (typeof input === 'string') {\n            bytes = (0, uuid_utils_1.uuidHexStringToBuffer)(input);\n        }\n        else {\n            throw new error_1.BSONTypeError('Argument passed in UUID constructor must be a UUID, a 16 byte Buffer or a 32/36 character hex string (dashes excluded/included, format: xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx).');\n        }\n        _this = _super.call(this, bytes, constants_1.BSON_BINARY_SUBTYPE_UUID_NEW) || this;\n        _this.__id = hexStr;\n        return _this;\n    }\n    Object.defineProperty(UUID.prototype, \"id\", {\n        /**\n         * The UUID bytes\n         * @readonly\n         */\n        get: function () {\n            return this.buffer;\n        },\n        set: function (value) {\n            this.buffer = value;\n            if (UUID.cacheHexString) {\n                this.__id = (0, uuid_utils_1.bufferToUuidHexString)(value);\n            }\n        },\n        enumerable: false,\n        configurable: true\n    });\n    /**\n     * Returns the UUID id as a 32 or 36 character hex string representation, excluding/including dashes (defaults to 36 character dash separated)\n     * @param includeDashes - should the string exclude dash-separators.\n     * */\n    UUID.prototype.toHexString = function (includeDashes) {\n        if (includeDashes === void 0) { includeDashes = true; }\n        if (UUID.cacheHexString && this.__id) {\n            return this.__id;\n        }\n        var uuidHexString = (0, uuid_utils_1.bufferToUuidHexString)(this.id, includeDashes);\n        if (UUID.cacheHexString) {\n            this.__id = uuidHexString;\n        }\n        return uuidHexString;\n    };\n    /**\n     * Converts the id into a 36 character (dashes included) hex string, unless a encoding is specified.\n     */\n    UUID.prototype.toString = function (encoding) {\n        return encoding ? this.id.toString(encoding) : this.toHexString();\n    };\n    /**\n     * Converts the id into its JSON string representation.\n     * A 36 character (dashes included) hex string in the format: xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx\n     */\n    UUID.prototype.toJSON = function () {\n        return this.toHexString();\n    };\n    /**\n     * Compares the equality of this UUID with `otherID`.\n     *\n     * @param otherId - UUID instance to compare against.\n     */\n    UUID.prototype.equals = function (otherId) {\n        if (!otherId) {\n            return false;\n        }\n        if (otherId instanceof UUID) {\n            return otherId.id.equals(this.id);\n        }\n        try {\n            return new UUID(otherId).id.equals(this.id);\n        }\n        catch (_a) {\n            return false;\n        }\n    };\n    /**\n     * Creates a Binary instance from the current UUID.\n     */\n    UUID.prototype.toBinary = function () {\n        return new Binary(this.id, Binary.SUBTYPE_UUID);\n    };\n    /**\n     * Generates a populated buffer containing a v4 uuid\n     */\n    UUID.generate = function () {\n        var bytes = (0, utils_1.randomBytes)(UUID_BYTE_LENGTH);\n        // Per 4.4, set bits for version and `clock_seq_hi_and_reserved`\n        // Kindly borrowed from https://github.com/uuidjs/uuid/blob/master/src/v4.js\n        bytes[6] = (bytes[6] & 0x0f) | 0x40;\n        bytes[8] = (bytes[8] & 0x3f) | 0x80;\n        return buffer_1.Buffer.from(bytes);\n    };\n    /**\n     * Checks if a value is a valid bson UUID\n     * @param input - UUID, string or Buffer to validate.\n     */\n    UUID.isValid = function (input) {\n        if (!input) {\n            return false;\n        }\n        if (input instanceof UUID) {\n            return true;\n        }\n        if (typeof input === 'string') {\n            return (0, uuid_utils_1.uuidValidateString)(input);\n        }\n        if ((0, utils_1.isUint8Array)(input)) {\n            // check for length & uuid version (https://tools.ietf.org/html/rfc4122#section-4.1.3)\n            if (input.length !== UUID_BYTE_LENGTH) {\n                return false;\n            }\n            return (input[6] & 0xf0) === 0x40 && (input[8] & 0x80) === 0x80;\n        }\n        return false;\n    };\n    /**\n     * Creates an UUID from a hex string representation of an UUID.\n     * @param hexString - 32 or 36 character hex string (dashes excluded/included).\n     */\n    UUID.createFromHexString = function (hexString) {\n        var buffer = (0, uuid_utils_1.uuidHexStringToBuffer)(hexString);\n        return new UUID(buffer);\n    };\n    /**\n     * Converts to a string representation of this Id.\n     *\n     * @returns return the 36 character hex string representation.\n     * @internal\n     */\n    UUID.prototype[Symbol.for('nodejs.util.inspect.custom')] = function () {\n        return this.inspect();\n    };\n    UUID.prototype.inspect = function () {\n        return \"new UUID(\\\"\".concat(this.toHexString(), \"\\\")\");\n    };\n    return UUID;\n}(Binary));\nexports.UUID = UUID;\n//# sourceMappingURL=binary.js.map", "\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.ensureBuffer = void 0;\nvar buffer_1 = require(\"buffer\");\nvar error_1 = require(\"./error\");\nvar utils_1 = require(\"./parser/utils\");\n/**\n * Makes sure that, if a Uint8Array is passed in, it is wrapped in a Buffer.\n *\n * @param potentialBuffer - The potential buffer\n * @returns Buffer the input if potentialBuffer is a buffer, or a buffer that\n * wraps a passed in Uint8Array\n * @throws BSONTypeError If anything other than a Buffer or Uint8Array is passed in\n */\nfunction ensureBuffer(potentialBuffer) {\n    if (ArrayBuffer.isView(potentialBuffer)) {\n        return buffer_1.Buffer.from(potentialBuffer.buffer, potentialBuffer.byteOffset, potentialBuffer.byteLength);\n    }\n    if ((0, utils_1.isAnyArrayBuffer)(potentialBuffer)) {\n        return buffer_1.Buffer.from(potentialBuffer);\n    }\n    throw new error_1.BSONTypeError('Must use either Buffer or TypedArray');\n}\nexports.ensureBuffer = ensureBuffer;\n//# sourceMappingURL=ensure_buffer.js.map", "\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.BSONTypeError = exports.BSONError = void 0;\n/** @public */\nvar BSONError = /** @class */ (function (_super) {\n    __extends(BSONError, _super);\n    function BSONError(message) {\n        var _this = _super.call(this, message) || this;\n        Object.setPrototypeOf(_this, BSONError.prototype);\n        return _this;\n    }\n    Object.defineProperty(BSONError.prototype, \"name\", {\n        get: function () {\n            return 'BSONError';\n        },\n        enumerable: false,\n        configurable: true\n    });\n    return BSONError;\n}(Error));\nexports.BSONError = BSONError;\n/** @public */\nvar BSONTypeError = /** @class */ (function (_super) {\n    __extends(BSONTypeError, _super);\n    function BSONTypeError(message) {\n        var _this = _super.call(this, message) || this;\n        Object.setPrototypeOf(_this, BSONTypeError.prototype);\n        return _this;\n    }\n    Object.defineProperty(BSONTypeError.prototype, \"name\", {\n        get: function () {\n            return 'BSONTypeError';\n        },\n        enumerable: false,\n        configurable: true\n    });\n    return BSONTypeError;\n}(TypeError));\nexports.BSONTypeError = BSONTypeError;\n//# sourceMappingURL=error.js.map", "\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.deprecate = exports.isObjectLike = exports.isDate = exports.haveBuffer = exports.isMap = exports.isRegExp = exports.isBigUInt64Array = exports.isBigInt64Array = exports.isUint8Array = exports.isAnyArrayBuffer = exports.randomBytes = exports.normalizedFunctionString = void 0;\nvar buffer_1 = require(\"buffer\");\nvar global_1 = require(\"../utils/global\");\n/**\n * Normalizes our expected stringified form of a function across versions of node\n * @param fn - The function to stringify\n */\nfunction normalizedFunctionString(fn) {\n    return fn.toString().replace('function(', 'function (');\n}\nexports.normalizedFunctionString = normalizedFunctionString;\nfunction isReactNative() {\n    var g = (0, global_1.getGlobal)();\n    return typeof g.navigator === 'object' && g.navigator.product === 'ReactNative';\n}\nvar insecureRandomBytes = function insecureRandomBytes(size) {\n    var insecureWarning = isReactNative()\n        ? 'BSON: For React Native please polyfill crypto.getRandomValues, e.g. using: https://www.npmjs.com/package/react-native-get-random-values.'\n        : 'BSON: No cryptographic implementation for random bytes present, falling back to a less secure implementation.';\n    console.warn(insecureWarning);\n    var result = buffer_1.Buffer.alloc(size);\n    for (var i = 0; i < size; ++i)\n        result[i] = Math.floor(Math.random() * 256);\n    return result;\n};\nvar detectRandomBytes = function () {\n    if (process.browser) {\n        if (typeof window !== 'undefined') {\n            // browser crypto implementation(s)\n            var target_1 = window.crypto || window.msCrypto; // allow for IE11\n            if (target_1 && target_1.getRandomValues) {\n                return function (size) { return target_1.getRandomValues(buffer_1.Buffer.alloc(size)); };\n            }\n        }\n        if (typeof global !== 'undefined' && global.crypto && global.crypto.getRandomValues) {\n            // allow for RN packages such as https://www.npmjs.com/package/react-native-get-random-values to populate global\n            return function (size) { return global.crypto.getRandomValues(buffer_1.Buffer.alloc(size)); };\n        }\n        return insecureRandomBytes;\n    }\n    else {\n        var requiredRandomBytes = void 0;\n        try {\n            requiredRandomBytes = require('crypto').randomBytes;\n        }\n        catch (e) {\n            // keep the fallback\n        }\n        // NOTE: in transpiled cases the above require might return null/undefined\n        return requiredRandomBytes || insecureRandomBytes;\n    }\n};\nexports.randomBytes = detectRandomBytes();\nfunction isAnyArrayBuffer(value) {\n    return ['[object ArrayBuffer]', '[object SharedArrayBuffer]'].includes(Object.prototype.toString.call(value));\n}\nexports.isAnyArrayBuffer = isAnyArrayBuffer;\nfunction isUint8Array(value) {\n    return Object.prototype.toString.call(value) === '[object Uint8Array]';\n}\nexports.isUint8Array = isUint8Array;\nfunction isBigInt64Array(value) {\n    return Object.prototype.toString.call(value) === '[object BigInt64Array]';\n}\nexports.isBigInt64Array = isBigInt64Array;\nfunction isBigUInt64Array(value) {\n    return Object.prototype.toString.call(value) === '[object BigUint64Array]';\n}\nexports.isBigUInt64Array = isBigUInt64Array;\nfunction isRegExp(d) {\n    return Object.prototype.toString.call(d) === '[object RegExp]';\n}\nexports.isRegExp = isRegExp;\nfunction isMap(d) {\n    return Object.prototype.toString.call(d) === '[object Map]';\n}\nexports.isMap = isMap;\n/** Call to check if your environment has `Buffer` */\nfunction haveBuffer() {\n    return typeof global !== 'undefined' && typeof global.Buffer !== 'undefined';\n}\nexports.haveBuffer = haveBuffer;\n// To ensure that 0.4 of node works correctly\nfunction isDate(d) {\n    return isObjectLike(d) && Object.prototype.toString.call(d) === '[object Date]';\n}\nexports.isDate = isDate;\n/**\n * @internal\n * this is to solve the `'someKey' in x` problem where x is unknown.\n * https://github.com/typescript-eslint/typescript-eslint/issues/1071#issuecomment-541955753\n */\nfunction isObjectLike(candidate) {\n    return typeof candidate === 'object' && candidate !== null;\n}\nexports.isObjectLike = isObjectLike;\nfunction deprecate(fn, message) {\n    var warned = false;\n    function deprecated() {\n        var args = [];\n        for (var _i = 0; _i < arguments.length; _i++) {\n            args[_i] = arguments[_i];\n        }\n        if (!warned) {\n            console.warn(message);\n            warned = true;\n        }\n        return fn.apply(this, args);\n    }\n    return deprecated;\n}\nexports.deprecate = deprecate;\n//# sourceMappingURL=utils.js.map", "\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.getGlobal = void 0;\nfunction checkForMath(potentialGlobal) {\n    // eslint-disable-next-line eqeqeq\n    return potentialGlobal && potentialGlobal.Math == Math && potentialGlobal;\n}\n// https://github.com/zloirock/core-js/issues/86#issuecomment-115759028\nfunction getGlobal() {\n    return (checkForMath(typeof globalThis === 'object' && globalThis) ||\n        checkForMath(typeof window === 'object' && window) ||\n        checkForMath(typeof self === 'object' && self) ||\n        checkForMath(typeof global === 'object' && global) ||\n        // eslint-disable-next-line @typescript-eslint/no-implied-eval\n        Function('return this')());\n}\nexports.getGlobal = getGlobal;\n//# sourceMappingURL=global.js.map", "\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.bufferToUuidHexString = exports.uuidHexStringToBuffer = exports.uuidValidateString = void 0;\nvar buffer_1 = require(\"buffer\");\nvar error_1 = require(\"./error\");\n// Validation regex for v4 uuid (validates with or without dashes)\nvar VALIDATION_REGEX = /^(?:[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}|[0-9a-f]{12}4[0-9a-f]{3}[89ab][0-9a-f]{15})$/i;\nvar uuidValidateString = function (str) {\n    return typeof str === 'string' && VALIDATION_REGEX.test(str);\n};\nexports.uuidValidateString = uuidValidateString;\nvar uuidHexStringToBuffer = function (hexString) {\n    if (!(0, exports.uuidValidateString)(hexString)) {\n        throw new error_1.BSONTypeError('UUID string representations must be a 32 or 36 character hex string (dashes excluded/included). Format: \"xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx\" or \"xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx\".');\n    }\n    var sanitizedHexString = hexString.replace(/-/g, '');\n    return buffer_1.Buffer.from(sanitizedHexString, 'hex');\n};\nexports.uuidHexStringToBuffer = uuidHexStringToBuffer;\nvar bufferToUuidHexString = function (buffer, includeDashes) {\n    if (includeDashes === void 0) { includeDashes = true; }\n    return includeDashes\n        ? buffer.toString('hex', 0, 4) +\n            '-' +\n            buffer.toString('hex', 4, 6) +\n            '-' +\n            buffer.toString('hex', 6, 8) +\n            '-' +\n            buffer.toString('hex', 8, 10) +\n            '-' +\n            buffer.toString('hex', 10, 16)\n        : buffer.toString('hex');\n};\nexports.bufferToUuidHexString = bufferToUuidHexString;\n//# sourceMappingURL=uuid_utils.js.map", "\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.BSON_BINARY_SUBTYPE_USER_DEFINED = exports.BSON_BINARY_SUBTYPE_COLUMN = exports.BSON_BINARY_SUBTYPE_ENCRYPTED = exports.BSON_BINARY_SUBTYPE_MD5 = exports.BSON_BINARY_SUBTYPE_UUID_NEW = exports.BSON_BINARY_SUBTYPE_UUID = exports.BSON_BINARY_SUBTYPE_BYTE_ARRAY = exports.BSON_BINARY_SUBTYPE_FUNCTION = exports.BSON_BINARY_SUBTYPE_DEFAULT = exports.BSON_DATA_MAX_KEY = exports.BSON_DATA_MIN_KEY = exports.BSON_DATA_DECIMAL128 = exports.BSON_DATA_LONG = exports.BSON_DATA_TIMESTAMP = exports.BSON_DATA_INT = exports.BSON_DATA_CODE_W_SCOPE = exports.BSON_DATA_SYMBOL = exports.BSON_DATA_CODE = exports.BSON_DATA_DBPOINTER = exports.BSON_DATA_REGEXP = exports.BSON_DATA_NULL = exports.BSON_DATA_DATE = exports.BSON_DATA_BOOLEAN = exports.BSON_DATA_OID = exports.BSON_DATA_UNDEFINED = exports.BSON_DATA_BINARY = exports.BSON_DATA_ARRAY = exports.BSON_DATA_OBJECT = exports.BSON_DATA_STRING = exports.BSON_DATA_NUMBER = exports.JS_INT_MIN = exports.JS_INT_MAX = exports.BSON_INT64_MIN = exports.BSON_INT64_MAX = exports.BSON_INT32_MIN = exports.BSON_INT32_MAX = void 0;\n/** @internal */\nexports.BSON_INT32_MAX = 0x7fffffff;\n/** @internal */\nexports.BSON_INT32_MIN = -0x80000000;\n/** @internal */\nexports.BSON_INT64_MAX = Math.pow(2, 63) - 1;\n/** @internal */\nexports.BSON_INT64_MIN = -Math.pow(2, 63);\n/**\n * Any integer up to 2^53 can be precisely represented by a double.\n * @internal\n */\nexports.JS_INT_MAX = Math.pow(2, 53);\n/**\n * Any integer down to -2^53 can be precisely represented by a double.\n * @internal\n */\nexports.JS_INT_MIN = -Math.pow(2, 53);\n/** Number BSON Type @internal */\nexports.BSON_DATA_NUMBER = 1;\n/** String BSON Type @internal */\nexports.BSON_DATA_STRING = 2;\n/** Object BSON Type @internal */\nexports.BSON_DATA_OBJECT = 3;\n/** Array BSON Type @internal */\nexports.BSON_DATA_ARRAY = 4;\n/** Binary BSON Type @internal */\nexports.BSON_DATA_BINARY = 5;\n/** Binary BSON Type @internal */\nexports.BSON_DATA_UNDEFINED = 6;\n/** ObjectId BSON Type @internal */\nexports.BSON_DATA_OID = 7;\n/** Boolean BSON Type @internal */\nexports.BSON_DATA_BOOLEAN = 8;\n/** Date BSON Type @internal */\nexports.BSON_DATA_DATE = 9;\n/** null BSON Type @internal */\nexports.BSON_DATA_NULL = 10;\n/** RegExp BSON Type @internal */\nexports.BSON_DATA_REGEXP = 11;\n/** Code BSON Type @internal */\nexports.BSON_DATA_DBPOINTER = 12;\n/** Code BSON Type @internal */\nexports.BSON_DATA_CODE = 13;\n/** Symbol BSON Type @internal */\nexports.BSON_DATA_SYMBOL = 14;\n/** Code with Scope BSON Type @internal */\nexports.BSON_DATA_CODE_W_SCOPE = 15;\n/** 32 bit Integer BSON Type @internal */\nexports.BSON_DATA_INT = 16;\n/** Timestamp BSON Type @internal */\nexports.BSON_DATA_TIMESTAMP = 17;\n/** Long BSON Type @internal */\nexports.BSON_DATA_LONG = 18;\n/** Decimal128 BSON Type @internal */\nexports.BSON_DATA_DECIMAL128 = 19;\n/** MinKey BSON Type @internal */\nexports.BSON_DATA_MIN_KEY = 0xff;\n/** MaxKey BSON Type @internal */\nexports.BSON_DATA_MAX_KEY = 0x7f;\n/** Binary Default Type @internal */\nexports.BSON_BINARY_SUBTYPE_DEFAULT = 0;\n/** Binary Function Type @internal */\nexports.BSON_BINARY_SUBTYPE_FUNCTION = 1;\n/** Binary Byte Array Type @internal */\nexports.BSON_BINARY_SUBTYPE_BYTE_ARRAY = 2;\n/** Binary Deprecated UUID Type @deprecated Please use BSON_BINARY_SUBTYPE_UUID_NEW @internal */\nexports.BSON_BINARY_SUBTYPE_UUID = 3;\n/** Binary UUID Type @internal */\nexports.BSON_BINARY_SUBTYPE_UUID_NEW = 4;\n/** Binary MD5 Type @internal */\nexports.BSON_BINARY_SUBTYPE_MD5 = 5;\n/** Encrypted BSON type @internal */\nexports.BSON_BINARY_SUBTYPE_ENCRYPTED = 6;\n/** Column BSON type @internal */\nexports.BSON_BINARY_SUBTYPE_COLUMN = 7;\n/** Binary User Defined Type @internal */\nexports.BSON_BINARY_SUBTYPE_USER_DEFINED = 128;\n//# sourceMappingURL=constants.js.map", "\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.Code = void 0;\n/**\n * A class representation of the BSON Code type.\n * @public\n * @category BSONType\n */\nvar Code = /** @class */ (function () {\n    /**\n     * @param code - a string or function.\n     * @param scope - an optional scope for the function.\n     */\n    function Code(code, scope) {\n        if (!(this instanceof Code))\n            return new Code(code, scope);\n        this.code = code;\n        this.scope = scope;\n    }\n    Code.prototype.toJSON = function () {\n        return { code: this.code, scope: this.scope };\n    };\n    /** @internal */\n    Code.prototype.toExtendedJSON = function () {\n        if (this.scope) {\n            return { $code: this.code, $scope: this.scope };\n        }\n        return { $code: this.code };\n    };\n    /** @internal */\n    Code.fromExtendedJSON = function (doc) {\n        return new Code(doc.$code, doc.$scope);\n    };\n    /** @internal */\n    Code.prototype[Symbol.for('nodejs.util.inspect.custom')] = function () {\n        return this.inspect();\n    };\n    Code.prototype.inspect = function () {\n        var codeJson = this.toJSON();\n        return \"new Code(\\\"\".concat(String(codeJson.code), \"\\\"\").concat(codeJson.scope ? \", \".concat(JSON.stringify(codeJson.scope)) : '', \")\");\n    };\n    return Code;\n}());\nexports.Code = Code;\nObject.defineProperty(Code.prototype, '_bsontype', { value: 'Code' });\n//# sourceMappingURL=code.js.map", "\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.DBRef = exports.isDBRefLike = void 0;\nvar utils_1 = require(\"./parser/utils\");\n/** @internal */\nfunction isDBRefLike(value) {\n    return ((0, utils_1.isObjectLike)(value) &&\n        value.$id != null &&\n        typeof value.$ref === 'string' &&\n        (value.$db == null || typeof value.$db === 'string'));\n}\nexports.isDBRefLike = isDBRefLike;\n/**\n * A class representation of the BSON DBRef type.\n * @public\n * @category BSONType\n */\nvar DBRef = /** @class */ (function () {\n    /**\n     * @param collection - the collection name.\n     * @param oid - the reference ObjectId.\n     * @param db - optional db name, if omitted the reference is local to the current db.\n     */\n    function DBRef(collection, oid, db, fields) {\n        if (!(this instanceof DBRef))\n            return new DBRef(collection, oid, db, fields);\n        // check if namespace has been provided\n        var parts = collection.split('.');\n        if (parts.length === 2) {\n            db = parts.shift();\n            // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n            collection = parts.shift();\n        }\n        this.collection = collection;\n        this.oid = oid;\n        this.db = db;\n        this.fields = fields || {};\n    }\n    Object.defineProperty(DBRef.prototype, \"namespace\", {\n        // Property provided for compatibility with the 1.x parser\n        // the 1.x parser used a \"namespace\" property, while 4.x uses \"collection\"\n        /** @internal */\n        get: function () {\n            return this.collection;\n        },\n        set: function (value) {\n            this.collection = value;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    DBRef.prototype.toJSON = function () {\n        var o = Object.assign({\n            $ref: this.collection,\n            $id: this.oid\n        }, this.fields);\n        if (this.db != null)\n            o.$db = this.db;\n        return o;\n    };\n    /** @internal */\n    DBRef.prototype.toExtendedJSON = function (options) {\n        options = options || {};\n        var o = {\n            $ref: this.collection,\n            $id: this.oid\n        };\n        if (options.legacy) {\n            return o;\n        }\n        if (this.db)\n            o.$db = this.db;\n        o = Object.assign(o, this.fields);\n        return o;\n    };\n    /** @internal */\n    DBRef.fromExtendedJSON = function (doc) {\n        var copy = Object.assign({}, doc);\n        delete copy.$ref;\n        delete copy.$id;\n        delete copy.$db;\n        return new DBRef(doc.$ref, doc.$id, doc.$db, copy);\n    };\n    /** @internal */\n    DBRef.prototype[Symbol.for('nodejs.util.inspect.custom')] = function () {\n        return this.inspect();\n    };\n    DBRef.prototype.inspect = function () {\n        // NOTE: if OID is an ObjectId class it will just print the oid string.\n        var oid = this.oid === undefined || this.oid.toString === undefined ? this.oid : this.oid.toString();\n        return \"new DBRef(\\\"\".concat(this.namespace, \"\\\", new ObjectId(\\\"\").concat(String(oid), \"\\\")\").concat(this.db ? \", \\\"\".concat(this.db, \"\\\"\") : '', \")\");\n    };\n    return DBRef;\n}());\nexports.DBRef = DBRef;\nObject.defineProperty(DBRef.prototype, '_bsontype', { value: 'DBRef' });\n//# sourceMappingURL=db_ref.js.map", "\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.Decimal128 = void 0;\nvar buffer_1 = require(\"buffer\");\nvar error_1 = require(\"./error\");\nvar long_1 = require(\"./long\");\nvar utils_1 = require(\"./parser/utils\");\nvar PARSE_STRING_REGEXP = /^(\\+|-)?(\\d+|(\\d*\\.\\d*))?(E|e)?([-+])?(\\d+)?$/;\nvar PARSE_INF_REGEXP = /^(\\+|-)?(Infinity|inf)$/i;\nvar PARSE_NAN_REGEXP = /^(\\+|-)?NaN$/i;\nvar EXPONENT_MAX = 6111;\nvar EXPONENT_MIN = -6176;\nvar EXPONENT_BIAS = 6176;\nvar MAX_DIGITS = 34;\n// Nan value bits as 32 bit values (due to lack of longs)\nvar NAN_BUFFER = [\n    0x7c, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00\n].reverse();\n// Infinity value bits 32 bit values (due to lack of longs)\nvar INF_NEGATIVE_BUFFER = [\n    0xf8, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00\n].reverse();\nvar INF_POSITIVE_BUFFER = [\n    0x78, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00\n].reverse();\nvar EXPONENT_REGEX = /^([-+])?(\\d+)?$/;\n// Extract least significant 5 bits\nvar COMBINATION_MASK = 0x1f;\n// Extract least significant 14 bits\nvar EXPONENT_MASK = 0x3fff;\n// Value of combination field for Inf\nvar COMBINATION_INFINITY = 30;\n// Value of combination field for NaN\nvar COMBINATION_NAN = 31;\n// Detect if the value is a digit\nfunction isDigit(value) {\n    return !isNaN(parseInt(value, 10));\n}\n// Divide two uint128 values\nfunction divideu128(value) {\n    var DIVISOR = long_1.Long.fromNumber(1000 * 1000 * 1000);\n    var _rem = long_1.Long.fromNumber(0);\n    if (!value.parts[0] && !value.parts[1] && !value.parts[2] && !value.parts[3]) {\n        return { quotient: value, rem: _rem };\n    }\n    for (var i = 0; i <= 3; i++) {\n        // Adjust remainder to match value of next dividend\n        _rem = _rem.shiftLeft(32);\n        // Add the divided to _rem\n        _rem = _rem.add(new long_1.Long(value.parts[i], 0));\n        value.parts[i] = _rem.div(DIVISOR).low;\n        _rem = _rem.modulo(DIVISOR);\n    }\n    return { quotient: value, rem: _rem };\n}\n// Multiply two Long values and return the 128 bit value\nfunction multiply64x2(left, right) {\n    if (!left && !right) {\n        return { high: long_1.Long.fromNumber(0), low: long_1.Long.fromNumber(0) };\n    }\n    var leftHigh = left.shiftRightUnsigned(32);\n    var leftLow = new long_1.Long(left.getLowBits(), 0);\n    var rightHigh = right.shiftRightUnsigned(32);\n    var rightLow = new long_1.Long(right.getLowBits(), 0);\n    var productHigh = leftHigh.multiply(rightHigh);\n    var productMid = leftHigh.multiply(rightLow);\n    var productMid2 = leftLow.multiply(rightHigh);\n    var productLow = leftLow.multiply(rightLow);\n    productHigh = productHigh.add(productMid.shiftRightUnsigned(32));\n    productMid = new long_1.Long(productMid.getLowBits(), 0)\n        .add(productMid2)\n        .add(productLow.shiftRightUnsigned(32));\n    productHigh = productHigh.add(productMid.shiftRightUnsigned(32));\n    productLow = productMid.shiftLeft(32).add(new long_1.Long(productLow.getLowBits(), 0));\n    // Return the 128 bit result\n    return { high: productHigh, low: productLow };\n}\nfunction lessThan(left, right) {\n    // Make values unsigned\n    var uhleft = left.high >>> 0;\n    var uhright = right.high >>> 0;\n    // Compare high bits first\n    if (uhleft < uhright) {\n        return true;\n    }\n    else if (uhleft === uhright) {\n        var ulleft = left.low >>> 0;\n        var ulright = right.low >>> 0;\n        if (ulleft < ulright)\n            return true;\n    }\n    return false;\n}\nfunction invalidErr(string, message) {\n    throw new error_1.BSONTypeError(\"\\\"\".concat(string, \"\\\" is not a valid Decimal128 string - \").concat(message));\n}\n/**\n * A class representation of the BSON Decimal128 type.\n * @public\n * @category BSONType\n */\nvar Decimal128 = /** @class */ (function () {\n    /**\n     * @param bytes - a buffer containing the raw Decimal128 bytes in little endian order,\n     *                or a string representation as returned by .toString()\n     */\n    function Decimal128(bytes) {\n        if (!(this instanceof Decimal128))\n            return new Decimal128(bytes);\n        if (typeof bytes === 'string') {\n            this.bytes = Decimal128.fromString(bytes).bytes;\n        }\n        else if ((0, utils_1.isUint8Array)(bytes)) {\n            if (bytes.byteLength !== 16) {\n                throw new error_1.BSONTypeError('Decimal128 must take a Buffer of 16 bytes');\n            }\n            this.bytes = bytes;\n        }\n        else {\n            throw new error_1.BSONTypeError('Decimal128 must take a Buffer or string');\n        }\n    }\n    /**\n     * Create a Decimal128 instance from a string representation\n     *\n     * @param representation - a numeric string representation.\n     */\n    Decimal128.fromString = function (representation) {\n        // Parse state tracking\n        var isNegative = false;\n        var sawRadix = false;\n        var foundNonZero = false;\n        // Total number of significant digits (no leading or trailing zero)\n        var significantDigits = 0;\n        // Total number of significand digits read\n        var nDigitsRead = 0;\n        // Total number of digits (no leading zeros)\n        var nDigits = 0;\n        // The number of the digits after radix\n        var radixPosition = 0;\n        // The index of the first non-zero in *str*\n        var firstNonZero = 0;\n        // Digits Array\n        var digits = [0];\n        // The number of digits in digits\n        var nDigitsStored = 0;\n        // Insertion pointer for digits\n        var digitsInsert = 0;\n        // The index of the first non-zero digit\n        var firstDigit = 0;\n        // The index of the last digit\n        var lastDigit = 0;\n        // Exponent\n        var exponent = 0;\n        // loop index over array\n        var i = 0;\n        // The high 17 digits of the significand\n        var significandHigh = new long_1.Long(0, 0);\n        // The low 17 digits of the significand\n        var significandLow = new long_1.Long(0, 0);\n        // The biased exponent\n        var biasedExponent = 0;\n        // Read index\n        var index = 0;\n        // Naively prevent against REDOS attacks.\n        // TODO: implementing a custom parsing for this, or refactoring the regex would yield\n        //       further gains.\n        if (representation.length >= 7000) {\n            throw new error_1.BSONTypeError('' + representation + ' not a valid Decimal128 string');\n        }\n        // Results\n        var stringMatch = representation.match(PARSE_STRING_REGEXP);\n        var infMatch = representation.match(PARSE_INF_REGEXP);\n        var nanMatch = representation.match(PARSE_NAN_REGEXP);\n        // Validate the string\n        if ((!stringMatch && !infMatch && !nanMatch) || representation.length === 0) {\n            throw new error_1.BSONTypeError('' + representation + ' not a valid Decimal128 string');\n        }\n        if (stringMatch) {\n            // full_match = stringMatch[0]\n            // sign = stringMatch[1]\n            var unsignedNumber = stringMatch[2];\n            // stringMatch[3] is undefined if a whole number (ex \"1\", 12\")\n            // but defined if a number w/ decimal in it (ex \"1.0, 12.2\")\n            var e = stringMatch[4];\n            var expSign = stringMatch[5];\n            var expNumber = stringMatch[6];\n            // they provided e, but didn't give an exponent number. for ex \"1e\"\n            if (e && expNumber === undefined)\n                invalidErr(representation, 'missing exponent power');\n            // they provided e, but didn't give a number before it. for ex \"e1\"\n            if (e && unsignedNumber === undefined)\n                invalidErr(representation, 'missing exponent base');\n            if (e === undefined && (expSign || expNumber)) {\n                invalidErr(representation, 'missing e before exponent');\n            }\n        }\n        // Get the negative or positive sign\n        if (representation[index] === '+' || representation[index] === '-') {\n            isNegative = representation[index++] === '-';\n        }\n        // Check if user passed Infinity or NaN\n        if (!isDigit(representation[index]) && representation[index] !== '.') {\n            if (representation[index] === 'i' || representation[index] === 'I') {\n                return new Decimal128(buffer_1.Buffer.from(isNegative ? INF_NEGATIVE_BUFFER : INF_POSITIVE_BUFFER));\n            }\n            else if (representation[index] === 'N') {\n                return new Decimal128(buffer_1.Buffer.from(NAN_BUFFER));\n            }\n        }\n        // Read all the digits\n        while (isDigit(representation[index]) || representation[index] === '.') {\n            if (representation[index] === '.') {\n                if (sawRadix)\n                    invalidErr(representation, 'contains multiple periods');\n                sawRadix = true;\n                index = index + 1;\n                continue;\n            }\n            if (nDigitsStored < 34) {\n                if (representation[index] !== '0' || foundNonZero) {\n                    if (!foundNonZero) {\n                        firstNonZero = nDigitsRead;\n                    }\n                    foundNonZero = true;\n                    // Only store 34 digits\n                    digits[digitsInsert++] = parseInt(representation[index], 10);\n                    nDigitsStored = nDigitsStored + 1;\n                }\n            }\n            if (foundNonZero)\n                nDigits = nDigits + 1;\n            if (sawRadix)\n                radixPosition = radixPosition + 1;\n            nDigitsRead = nDigitsRead + 1;\n            index = index + 1;\n        }\n        if (sawRadix && !nDigitsRead)\n            throw new error_1.BSONTypeError('' + representation + ' not a valid Decimal128 string');\n        // Read exponent if exists\n        if (representation[index] === 'e' || representation[index] === 'E') {\n            // Read exponent digits\n            var match = representation.substr(++index).match(EXPONENT_REGEX);\n            // No digits read\n            if (!match || !match[2])\n                return new Decimal128(buffer_1.Buffer.from(NAN_BUFFER));\n            // Get exponent\n            exponent = parseInt(match[0], 10);\n            // Adjust the index\n            index = index + match[0].length;\n        }\n        // Return not a number\n        if (representation[index])\n            return new Decimal128(buffer_1.Buffer.from(NAN_BUFFER));\n        // Done reading input\n        // Find first non-zero digit in digits\n        firstDigit = 0;\n        if (!nDigitsStored) {\n            firstDigit = 0;\n            lastDigit = 0;\n            digits[0] = 0;\n            nDigits = 1;\n            nDigitsStored = 1;\n            significantDigits = 0;\n        }\n        else {\n            lastDigit = nDigitsStored - 1;\n            significantDigits = nDigits;\n            if (significantDigits !== 1) {\n                while (digits[firstNonZero + significantDigits - 1] === 0) {\n                    significantDigits = significantDigits - 1;\n                }\n            }\n        }\n        // Normalization of exponent\n        // Correct exponent based on radix position, and shift significand as needed\n        // to represent user input\n        // Overflow prevention\n        if (exponent <= radixPosition && radixPosition - exponent > 1 << 14) {\n            exponent = EXPONENT_MIN;\n        }\n        else {\n            exponent = exponent - radixPosition;\n        }\n        // Attempt to normalize the exponent\n        while (exponent > EXPONENT_MAX) {\n            // Shift exponent to significand and decrease\n            lastDigit = lastDigit + 1;\n            if (lastDigit - firstDigit > MAX_DIGITS) {\n                // Check if we have a zero then just hard clamp, otherwise fail\n                var digitsString = digits.join('');\n                if (digitsString.match(/^0+$/)) {\n                    exponent = EXPONENT_MAX;\n                    break;\n                }\n                invalidErr(representation, 'overflow');\n            }\n            exponent = exponent - 1;\n        }\n        while (exponent < EXPONENT_MIN || nDigitsStored < nDigits) {\n            // Shift last digit. can only do this if < significant digits than # stored.\n            if (lastDigit === 0 && significantDigits < nDigitsStored) {\n                exponent = EXPONENT_MIN;\n                significantDigits = 0;\n                break;\n            }\n            if (nDigitsStored < nDigits) {\n                // adjust to match digits not stored\n                nDigits = nDigits - 1;\n            }\n            else {\n                // adjust to round\n                lastDigit = lastDigit - 1;\n            }\n            if (exponent < EXPONENT_MAX) {\n                exponent = exponent + 1;\n            }\n            else {\n                // Check if we have a zero then just hard clamp, otherwise fail\n                var digitsString = digits.join('');\n                if (digitsString.match(/^0+$/)) {\n                    exponent = EXPONENT_MAX;\n                    break;\n                }\n                invalidErr(representation, 'overflow');\n            }\n        }\n        // Round\n        // We've normalized the exponent, but might still need to round.\n        if (lastDigit - firstDigit + 1 < significantDigits) {\n            var endOfString = nDigitsRead;\n            // If we have seen a radix point, 'string' is 1 longer than we have\n            // documented with ndigits_read, so inc the position of the first nonzero\n            // digit and the position that digits are read to.\n            if (sawRadix) {\n                firstNonZero = firstNonZero + 1;\n                endOfString = endOfString + 1;\n            }\n            // if negative, we need to increment again to account for - sign at start.\n            if (isNegative) {\n                firstNonZero = firstNonZero + 1;\n                endOfString = endOfString + 1;\n            }\n            var roundDigit = parseInt(representation[firstNonZero + lastDigit + 1], 10);\n            var roundBit = 0;\n            if (roundDigit >= 5) {\n                roundBit = 1;\n                if (roundDigit === 5) {\n                    roundBit = digits[lastDigit] % 2 === 1 ? 1 : 0;\n                    for (i = firstNonZero + lastDigit + 2; i < endOfString; i++) {\n                        if (parseInt(representation[i], 10)) {\n                            roundBit = 1;\n                            break;\n                        }\n                    }\n                }\n            }\n            if (roundBit) {\n                var dIdx = lastDigit;\n                for (; dIdx >= 0; dIdx--) {\n                    if (++digits[dIdx] > 9) {\n                        digits[dIdx] = 0;\n                        // overflowed most significant digit\n                        if (dIdx === 0) {\n                            if (exponent < EXPONENT_MAX) {\n                                exponent = exponent + 1;\n                                digits[dIdx] = 1;\n                            }\n                            else {\n                                return new Decimal128(buffer_1.Buffer.from(isNegative ? INF_NEGATIVE_BUFFER : INF_POSITIVE_BUFFER));\n                            }\n                        }\n                    }\n                }\n            }\n        }\n        // Encode significand\n        // The high 17 digits of the significand\n        significandHigh = long_1.Long.fromNumber(0);\n        // The low 17 digits of the significand\n        significandLow = long_1.Long.fromNumber(0);\n        // read a zero\n        if (significantDigits === 0) {\n            significandHigh = long_1.Long.fromNumber(0);\n            significandLow = long_1.Long.fromNumber(0);\n        }\n        else if (lastDigit - firstDigit < 17) {\n            var dIdx = firstDigit;\n            significandLow = long_1.Long.fromNumber(digits[dIdx++]);\n            significandHigh = new long_1.Long(0, 0);\n            for (; dIdx <= lastDigit; dIdx++) {\n                significandLow = significandLow.multiply(long_1.Long.fromNumber(10));\n                significandLow = significandLow.add(long_1.Long.fromNumber(digits[dIdx]));\n            }\n        }\n        else {\n            var dIdx = firstDigit;\n            significandHigh = long_1.Long.fromNumber(digits[dIdx++]);\n            for (; dIdx <= lastDigit - 17; dIdx++) {\n                significandHigh = significandHigh.multiply(long_1.Long.fromNumber(10));\n                significandHigh = significandHigh.add(long_1.Long.fromNumber(digits[dIdx]));\n            }\n            significandLow = long_1.Long.fromNumber(digits[dIdx++]);\n            for (; dIdx <= lastDigit; dIdx++) {\n                significandLow = significandLow.multiply(long_1.Long.fromNumber(10));\n                significandLow = significandLow.add(long_1.Long.fromNumber(digits[dIdx]));\n            }\n        }\n        var significand = multiply64x2(significandHigh, long_1.Long.fromString('100000000000000000'));\n        significand.low = significand.low.add(significandLow);\n        if (lessThan(significand.low, significandLow)) {\n            significand.high = significand.high.add(long_1.Long.fromNumber(1));\n        }\n        // Biased exponent\n        biasedExponent = exponent + EXPONENT_BIAS;\n        var dec = { low: long_1.Long.fromNumber(0), high: long_1.Long.fromNumber(0) };\n        // Encode combination, exponent, and significand.\n        if (significand.high.shiftRightUnsigned(49).and(long_1.Long.fromNumber(1)).equals(long_1.Long.fromNumber(1))) {\n            // Encode '11' into bits 1 to 3\n            dec.high = dec.high.or(long_1.Long.fromNumber(0x3).shiftLeft(61));\n            dec.high = dec.high.or(long_1.Long.fromNumber(biasedExponent).and(long_1.Long.fromNumber(0x3fff).shiftLeft(47)));\n            dec.high = dec.high.or(significand.high.and(long_1.Long.fromNumber(0x7fffffffffff)));\n        }\n        else {\n            dec.high = dec.high.or(long_1.Long.fromNumber(biasedExponent & 0x3fff).shiftLeft(49));\n            dec.high = dec.high.or(significand.high.and(long_1.Long.fromNumber(0x1ffffffffffff)));\n        }\n        dec.low = significand.low;\n        // Encode sign\n        if (isNegative) {\n            dec.high = dec.high.or(long_1.Long.fromString('9223372036854775808'));\n        }\n        // Encode into a buffer\n        var buffer = buffer_1.Buffer.alloc(16);\n        index = 0;\n        // Encode the low 64 bits of the decimal\n        // Encode low bits\n        buffer[index++] = dec.low.low & 0xff;\n        buffer[index++] = (dec.low.low >> 8) & 0xff;\n        buffer[index++] = (dec.low.low >> 16) & 0xff;\n        buffer[index++] = (dec.low.low >> 24) & 0xff;\n        // Encode high bits\n        buffer[index++] = dec.low.high & 0xff;\n        buffer[index++] = (dec.low.high >> 8) & 0xff;\n        buffer[index++] = (dec.low.high >> 16) & 0xff;\n        buffer[index++] = (dec.low.high >> 24) & 0xff;\n        // Encode the high 64 bits of the decimal\n        // Encode low bits\n        buffer[index++] = dec.high.low & 0xff;\n        buffer[index++] = (dec.high.low >> 8) & 0xff;\n        buffer[index++] = (dec.high.low >> 16) & 0xff;\n        buffer[index++] = (dec.high.low >> 24) & 0xff;\n        // Encode high bits\n        buffer[index++] = dec.high.high & 0xff;\n        buffer[index++] = (dec.high.high >> 8) & 0xff;\n        buffer[index++] = (dec.high.high >> 16) & 0xff;\n        buffer[index++] = (dec.high.high >> 24) & 0xff;\n        // Return the new Decimal128\n        return new Decimal128(buffer);\n    };\n    /** Create a string representation of the raw Decimal128 value */\n    Decimal128.prototype.toString = function () {\n        // Note: bits in this routine are referred to starting at 0,\n        // from the sign bit, towards the coefficient.\n        // decoded biased exponent (14 bits)\n        var biased_exponent;\n        // the number of significand digits\n        var significand_digits = 0;\n        // the base-10 digits in the significand\n        var significand = new Array(36);\n        for (var i = 0; i < significand.length; i++)\n            significand[i] = 0;\n        // read pointer into significand\n        var index = 0;\n        // true if the number is zero\n        var is_zero = false;\n        // the most significant significand bits (50-46)\n        var significand_msb;\n        // temporary storage for significand decoding\n        var significand128 = { parts: [0, 0, 0, 0] };\n        // indexing variables\n        var j, k;\n        // Output string\n        var string = [];\n        // Unpack index\n        index = 0;\n        // Buffer reference\n        var buffer = this.bytes;\n        // Unpack the low 64bits into a long\n        // bits 96 - 127\n        var low = buffer[index++] | (buffer[index++] << 8) | (buffer[index++] << 16) | (buffer[index++] << 24);\n        // bits 64 - 95\n        var midl = buffer[index++] | (buffer[index++] << 8) | (buffer[index++] << 16) | (buffer[index++] << 24);\n        // Unpack the high 64bits into a long\n        // bits 32 - 63\n        var midh = buffer[index++] | (buffer[index++] << 8) | (buffer[index++] << 16) | (buffer[index++] << 24);\n        // bits 0 - 31\n        var high = buffer[index++] | (buffer[index++] << 8) | (buffer[index++] << 16) | (buffer[index++] << 24);\n        // Unpack index\n        index = 0;\n        // Create the state of the decimal\n        var dec = {\n            low: new long_1.Long(low, midl),\n            high: new long_1.Long(midh, high)\n        };\n        if (dec.high.lessThan(long_1.Long.ZERO)) {\n            string.push('-');\n        }\n        // Decode combination field and exponent\n        // bits 1 - 5\n        var combination = (high >> 26) & COMBINATION_MASK;\n        if (combination >> 3 === 3) {\n            // Check for 'special' values\n            if (combination === COMBINATION_INFINITY) {\n                return string.join('') + 'Infinity';\n            }\n            else if (combination === COMBINATION_NAN) {\n                return 'NaN';\n            }\n            else {\n                biased_exponent = (high >> 15) & EXPONENT_MASK;\n                significand_msb = 0x08 + ((high >> 14) & 0x01);\n            }\n        }\n        else {\n            significand_msb = (high >> 14) & 0x07;\n            biased_exponent = (high >> 17) & EXPONENT_MASK;\n        }\n        // unbiased exponent\n        var exponent = biased_exponent - EXPONENT_BIAS;\n        // Create string of significand digits\n        // Convert the 114-bit binary number represented by\n        // (significand_high, significand_low) to at most 34 decimal\n        // digits through modulo and division.\n        significand128.parts[0] = (high & 0x3fff) + ((significand_msb & 0xf) << 14);\n        significand128.parts[1] = midh;\n        significand128.parts[2] = midl;\n        significand128.parts[3] = low;\n        if (significand128.parts[0] === 0 &&\n            significand128.parts[1] === 0 &&\n            significand128.parts[2] === 0 &&\n            significand128.parts[3] === 0) {\n            is_zero = true;\n        }\n        else {\n            for (k = 3; k >= 0; k--) {\n                var least_digits = 0;\n                // Perform the divide\n                var result = divideu128(significand128);\n                significand128 = result.quotient;\n                least_digits = result.rem.low;\n                // We now have the 9 least significant digits (in base 2).\n                // Convert and output to string.\n                if (!least_digits)\n                    continue;\n                for (j = 8; j >= 0; j--) {\n                    // significand[k * 9 + j] = Math.round(least_digits % 10);\n                    significand[k * 9 + j] = least_digits % 10;\n                    // least_digits = Math.round(least_digits / 10);\n                    least_digits = Math.floor(least_digits / 10);\n                }\n            }\n        }\n        // Output format options:\n        // Scientific - [-]d.dddE(+/-)dd or [-]dE(+/-)dd\n        // Regular    - ddd.ddd\n        if (is_zero) {\n            significand_digits = 1;\n            significand[index] = 0;\n        }\n        else {\n            significand_digits = 36;\n            while (!significand[index]) {\n                significand_digits = significand_digits - 1;\n                index = index + 1;\n            }\n        }\n        // the exponent if scientific notation is used\n        var scientific_exponent = significand_digits - 1 + exponent;\n        // The scientific exponent checks are dictated by the string conversion\n        // specification and are somewhat arbitrary cutoffs.\n        //\n        // We must check exponent > 0, because if this is the case, the number\n        // has trailing zeros.  However, we *cannot* output these trailing zeros,\n        // because doing so would change the precision of the value, and would\n        // change stored data if the string converted number is round tripped.\n        if (scientific_exponent >= 34 || scientific_exponent <= -7 || exponent > 0) {\n            // Scientific format\n            // if there are too many significant digits, we should just be treating numbers\n            // as + or - 0 and using the non-scientific exponent (this is for the \"invalid\n            // representation should be treated as 0/-0\" spec cases in decimal128-1.json)\n            if (significand_digits > 34) {\n                string.push(\"\".concat(0));\n                if (exponent > 0)\n                    string.push(\"E+\".concat(exponent));\n                else if (exponent < 0)\n                    string.push(\"E\".concat(exponent));\n                return string.join('');\n            }\n            string.push(\"\".concat(significand[index++]));\n            significand_digits = significand_digits - 1;\n            if (significand_digits) {\n                string.push('.');\n            }\n            for (var i = 0; i < significand_digits; i++) {\n                string.push(\"\".concat(significand[index++]));\n            }\n            // Exponent\n            string.push('E');\n            if (scientific_exponent > 0) {\n                string.push(\"+\".concat(scientific_exponent));\n            }\n            else {\n                string.push(\"\".concat(scientific_exponent));\n            }\n        }\n        else {\n            // Regular format with no decimal place\n            if (exponent >= 0) {\n                for (var i = 0; i < significand_digits; i++) {\n                    string.push(\"\".concat(significand[index++]));\n                }\n            }\n            else {\n                var radix_position = significand_digits + exponent;\n                // non-zero digits before radix\n                if (radix_position > 0) {\n                    for (var i = 0; i < radix_position; i++) {\n                        string.push(\"\".concat(significand[index++]));\n                    }\n                }\n                else {\n                    string.push('0');\n                }\n                string.push('.');\n                // add leading zeros after radix\n                while (radix_position++ < 0) {\n                    string.push('0');\n                }\n                for (var i = 0; i < significand_digits - Math.max(radix_position - 1, 0); i++) {\n                    string.push(\"\".concat(significand[index++]));\n                }\n            }\n        }\n        return string.join('');\n    };\n    Decimal128.prototype.toJSON = function () {\n        return { $numberDecimal: this.toString() };\n    };\n    /** @internal */\n    Decimal128.prototype.toExtendedJSON = function () {\n        return { $numberDecimal: this.toString() };\n    };\n    /** @internal */\n    Decimal128.fromExtendedJSON = function (doc) {\n        return Decimal128.fromString(doc.$numberDecimal);\n    };\n    /** @internal */\n    Decimal128.prototype[Symbol.for('nodejs.util.inspect.custom')] = function () {\n        return this.inspect();\n    };\n    Decimal128.prototype.inspect = function () {\n        return \"new Decimal128(\\\"\".concat(this.toString(), \"\\\")\");\n    };\n    return Decimal128;\n}());\nexports.Decimal128 = Decimal128;\nObject.defineProperty(Decimal128.prototype, '_bsontype', { value: 'Decimal128' });\n//# sourceMappingURL=decimal128.js.map", "\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.Long = void 0;\nvar utils_1 = require(\"./parser/utils\");\n/**\n * wasm optimizations, to do native i64 multiplication and divide\n */\nvar wasm = undefined;\ntry {\n    wasm = new WebAssembly.Instance(new WebAssembly.Module(\n    // prettier-ignore\n    new Uint8Array([0, 97, 115, 109, 1, 0, 0, 0, 1, 13, 2, 96, 0, 1, 127, 96, 4, 127, 127, 127, 127, 1, 127, 3, 7, 6, 0, 1, 1, 1, 1, 1, 6, 6, 1, 127, 1, 65, 0, 11, 7, 50, 6, 3, 109, 117, 108, 0, 1, 5, 100, 105, 118, 95, 115, 0, 2, 5, 100, 105, 118, 95, 117, 0, 3, 5, 114, 101, 109, 95, 115, 0, 4, 5, 114, 101, 109, 95, 117, 0, 5, 8, 103, 101, 116, 95, 104, 105, 103, 104, 0, 0, 10, 191, 1, 6, 4, 0, 35, 0, 11, 36, 1, 1, 126, 32, 0, 173, 32, 1, 173, 66, 32, 134, 132, 32, 2, 173, 32, 3, 173, 66, 32, 134, 132, 126, 34, 4, 66, 32, 135, 167, 36, 0, 32, 4, 167, 11, 36, 1, 1, 126, 32, 0, 173, 32, 1, 173, 66, 32, 134, 132, 32, 2, 173, 32, 3, 173, 66, 32, 134, 132, 127, 34, 4, 66, 32, 135, 167, 36, 0, 32, 4, 167, 11, 36, 1, 1, 126, 32, 0, 173, 32, 1, 173, 66, 32, 134, 132, 32, 2, 173, 32, 3, 173, 66, 32, 134, 132, 128, 34, 4, 66, 32, 135, 167, 36, 0, 32, 4, 167, 11, 36, 1, 1, 126, 32, 0, 173, 32, 1, 173, 66, 32, 134, 132, 32, 2, 173, 32, 3, 173, 66, 32, 134, 132, 129, 34, 4, 66, 32, 135, 167, 36, 0, 32, 4, 167, 11, 36, 1, 1, 126, 32, 0, 173, 32, 1, 173, 66, 32, 134, 132, 32, 2, 173, 32, 3, 173, 66, 32, 134, 132, 130, 34, 4, 66, 32, 135, 167, 36, 0, 32, 4, 167, 11])), {}).exports;\n}\ncatch (_a) {\n    // no wasm support\n}\nvar TWO_PWR_16_DBL = 1 << 16;\nvar TWO_PWR_24_DBL = 1 << 24;\nvar TWO_PWR_32_DBL = TWO_PWR_16_DBL * TWO_PWR_16_DBL;\nvar TWO_PWR_64_DBL = TWO_PWR_32_DBL * TWO_PWR_32_DBL;\nvar TWO_PWR_63_DBL = TWO_PWR_64_DBL / 2;\n/** A cache of the Long representations of small integer values. */\nvar INT_CACHE = {};\n/** A cache of the Long representations of small unsigned integer values. */\nvar UINT_CACHE = {};\n/**\n * A class representing a 64-bit integer\n * @public\n * @category BSONType\n * @remarks\n * The internal representation of a long is the two given signed, 32-bit values.\n * We use 32-bit pieces because these are the size of integers on which\n * Javascript performs bit-operations.  For operations like addition and\n * multiplication, we split each number into 16 bit pieces, which can easily be\n * multiplied within Javascript's floating-point representation without overflow\n * or change in sign.\n * In the algorithms below, we frequently reduce the negative case to the\n * positive case by negating the input(s) and then post-processing the result.\n * Note that we must ALWAYS check specially whether those values are MIN_VALUE\n * (-2^63) because -MIN_VALUE == MIN_VALUE (since 2^63 cannot be represented as\n * a positive number, it overflows back into a negative).  Not handling this\n * case would often result in infinite recursion.\n * Common constant values ZERO, ONE, NEG_ONE, etc. are found as static properties on this class.\n */\nvar Long = /** @class */ (function () {\n    /**\n     * Constructs a 64 bit two's-complement integer, given its low and high 32 bit values as *signed* integers.\n     *  See the from* functions below for more convenient ways of constructing Longs.\n     *\n     * Acceptable signatures are:\n     * - Long(low, high, unsigned?)\n     * - Long(bigint, unsigned?)\n     * - Long(string, unsigned?)\n     *\n     * @param low - The low (signed) 32 bits of the long\n     * @param high - The high (signed) 32 bits of the long\n     * @param unsigned - Whether unsigned or not, defaults to signed\n     */\n    function Long(low, high, unsigned) {\n        if (low === void 0) { low = 0; }\n        if (!(this instanceof Long))\n            return new Long(low, high, unsigned);\n        if (typeof low === 'bigint') {\n            Object.assign(this, Long.fromBigInt(low, !!high));\n        }\n        else if (typeof low === 'string') {\n            Object.assign(this, Long.fromString(low, !!high));\n        }\n        else {\n            this.low = low | 0;\n            this.high = high | 0;\n            this.unsigned = !!unsigned;\n        }\n        Object.defineProperty(this, '__isLong__', {\n            value: true,\n            configurable: false,\n            writable: false,\n            enumerable: false\n        });\n    }\n    /**\n     * Returns a Long representing the 64 bit integer that comes by concatenating the given low and high bits.\n     * Each is assumed to use 32 bits.\n     * @param lowBits - The low 32 bits\n     * @param highBits - The high 32 bits\n     * @param unsigned - Whether unsigned or not, defaults to signed\n     * @returns The corresponding Long value\n     */\n    Long.fromBits = function (lowBits, highBits, unsigned) {\n        return new Long(lowBits, highBits, unsigned);\n    };\n    /**\n     * Returns a Long representing the given 32 bit integer value.\n     * @param value - The 32 bit integer in question\n     * @param unsigned - Whether unsigned or not, defaults to signed\n     * @returns The corresponding Long value\n     */\n    Long.fromInt = function (value, unsigned) {\n        var obj, cachedObj, cache;\n        if (unsigned) {\n            value >>>= 0;\n            if ((cache = 0 <= value && value < 256)) {\n                cachedObj = UINT_CACHE[value];\n                if (cachedObj)\n                    return cachedObj;\n            }\n            obj = Long.fromBits(value, (value | 0) < 0 ? -1 : 0, true);\n            if (cache)\n                UINT_CACHE[value] = obj;\n            return obj;\n        }\n        else {\n            value |= 0;\n            if ((cache = -128 <= value && value < 128)) {\n                cachedObj = INT_CACHE[value];\n                if (cachedObj)\n                    return cachedObj;\n            }\n            obj = Long.fromBits(value, value < 0 ? -1 : 0, false);\n            if (cache)\n                INT_CACHE[value] = obj;\n            return obj;\n        }\n    };\n    /**\n     * Returns a Long representing the given value, provided that it is a finite number. Otherwise, zero is returned.\n     * @param value - The number in question\n     * @param unsigned - Whether unsigned or not, defaults to signed\n     * @returns The corresponding Long value\n     */\n    Long.fromNumber = function (value, unsigned) {\n        if (isNaN(value))\n            return unsigned ? Long.UZERO : Long.ZERO;\n        if (unsigned) {\n            if (value < 0)\n                return Long.UZERO;\n            if (value >= TWO_PWR_64_DBL)\n                return Long.MAX_UNSIGNED_VALUE;\n        }\n        else {\n            if (value <= -TWO_PWR_63_DBL)\n                return Long.MIN_VALUE;\n            if (value + 1 >= TWO_PWR_63_DBL)\n                return Long.MAX_VALUE;\n        }\n        if (value < 0)\n            return Long.fromNumber(-value, unsigned).neg();\n        return Long.fromBits(value % TWO_PWR_32_DBL | 0, (value / TWO_PWR_32_DBL) | 0, unsigned);\n    };\n    /**\n     * Returns a Long representing the given value, provided that it is a finite number. Otherwise, zero is returned.\n     * @param value - The number in question\n     * @param unsigned - Whether unsigned or not, defaults to signed\n     * @returns The corresponding Long value\n     */\n    Long.fromBigInt = function (value, unsigned) {\n        return Long.fromString(value.toString(), unsigned);\n    };\n    /**\n     * Returns a Long representation of the given string, written using the specified radix.\n     * @param str - The textual representation of the Long\n     * @param unsigned - Whether unsigned or not, defaults to signed\n     * @param radix - The radix in which the text is written (2-36), defaults to 10\n     * @returns The corresponding Long value\n     */\n    Long.fromString = function (str, unsigned, radix) {\n        if (str.length === 0)\n            throw Error('empty string');\n        if (str === 'NaN' || str === 'Infinity' || str === '+Infinity' || str === '-Infinity')\n            return Long.ZERO;\n        if (typeof unsigned === 'number') {\n            // For goog.math.long compatibility\n            (radix = unsigned), (unsigned = false);\n        }\n        else {\n            unsigned = !!unsigned;\n        }\n        radix = radix || 10;\n        if (radix < 2 || 36 < radix)\n            throw RangeError('radix');\n        var p;\n        if ((p = str.indexOf('-')) > 0)\n            throw Error('interior hyphen');\n        else if (p === 0) {\n            return Long.fromString(str.substring(1), unsigned, radix).neg();\n        }\n        // Do several (8) digits each time through the loop, so as to\n        // minimize the calls to the very expensive emulated div.\n        var radixToPower = Long.fromNumber(Math.pow(radix, 8));\n        var result = Long.ZERO;\n        for (var i = 0; i < str.length; i += 8) {\n            var size = Math.min(8, str.length - i), value = parseInt(str.substring(i, i + size), radix);\n            if (size < 8) {\n                var power = Long.fromNumber(Math.pow(radix, size));\n                result = result.mul(power).add(Long.fromNumber(value));\n            }\n            else {\n                result = result.mul(radixToPower);\n                result = result.add(Long.fromNumber(value));\n            }\n        }\n        result.unsigned = unsigned;\n        return result;\n    };\n    /**\n     * Creates a Long from its byte representation.\n     * @param bytes - Byte representation\n     * @param unsigned - Whether unsigned or not, defaults to signed\n     * @param le - Whether little or big endian, defaults to big endian\n     * @returns The corresponding Long value\n     */\n    Long.fromBytes = function (bytes, unsigned, le) {\n        return le ? Long.fromBytesLE(bytes, unsigned) : Long.fromBytesBE(bytes, unsigned);\n    };\n    /**\n     * Creates a Long from its little endian byte representation.\n     * @param bytes - Little endian byte representation\n     * @param unsigned - Whether unsigned or not, defaults to signed\n     * @returns The corresponding Long value\n     */\n    Long.fromBytesLE = function (bytes, unsigned) {\n        return new Long(bytes[0] | (bytes[1] << 8) | (bytes[2] << 16) | (bytes[3] << 24), bytes[4] | (bytes[5] << 8) | (bytes[6] << 16) | (bytes[7] << 24), unsigned);\n    };\n    /**\n     * Creates a Long from its big endian byte representation.\n     * @param bytes - Big endian byte representation\n     * @param unsigned - Whether unsigned or not, defaults to signed\n     * @returns The corresponding Long value\n     */\n    Long.fromBytesBE = function (bytes, unsigned) {\n        return new Long((bytes[4] << 24) | (bytes[5] << 16) | (bytes[6] << 8) | bytes[7], (bytes[0] << 24) | (bytes[1] << 16) | (bytes[2] << 8) | bytes[3], unsigned);\n    };\n    /**\n     * Tests if the specified object is a Long.\n     */\n    Long.isLong = function (value) {\n        return (0, utils_1.isObjectLike)(value) && value['__isLong__'] === true;\n    };\n    /**\n     * Converts the specified value to a Long.\n     * @param unsigned - Whether unsigned or not, defaults to signed\n     */\n    Long.fromValue = function (val, unsigned) {\n        if (typeof val === 'number')\n            return Long.fromNumber(val, unsigned);\n        if (typeof val === 'string')\n            return Long.fromString(val, unsigned);\n        // Throws for non-objects, converts non-instanceof Long:\n        return Long.fromBits(val.low, val.high, typeof unsigned === 'boolean' ? unsigned : val.unsigned);\n    };\n    /** Returns the sum of this and the specified Long. */\n    Long.prototype.add = function (addend) {\n        if (!Long.isLong(addend))\n            addend = Long.fromValue(addend);\n        // Divide each number into 4 chunks of 16 bits, and then sum the chunks.\n        var a48 = this.high >>> 16;\n        var a32 = this.high & 0xffff;\n        var a16 = this.low >>> 16;\n        var a00 = this.low & 0xffff;\n        var b48 = addend.high >>> 16;\n        var b32 = addend.high & 0xffff;\n        var b16 = addend.low >>> 16;\n        var b00 = addend.low & 0xffff;\n        var c48 = 0, c32 = 0, c16 = 0, c00 = 0;\n        c00 += a00 + b00;\n        c16 += c00 >>> 16;\n        c00 &= 0xffff;\n        c16 += a16 + b16;\n        c32 += c16 >>> 16;\n        c16 &= 0xffff;\n        c32 += a32 + b32;\n        c48 += c32 >>> 16;\n        c32 &= 0xffff;\n        c48 += a48 + b48;\n        c48 &= 0xffff;\n        return Long.fromBits((c16 << 16) | c00, (c48 << 16) | c32, this.unsigned);\n    };\n    /**\n     * Returns the sum of this and the specified Long.\n     * @returns Sum\n     */\n    Long.prototype.and = function (other) {\n        if (!Long.isLong(other))\n            other = Long.fromValue(other);\n        return Long.fromBits(this.low & other.low, this.high & other.high, this.unsigned);\n    };\n    /**\n     * Compares this Long's value with the specified's.\n     * @returns 0 if they are the same, 1 if the this is greater and -1 if the given one is greater\n     */\n    Long.prototype.compare = function (other) {\n        if (!Long.isLong(other))\n            other = Long.fromValue(other);\n        if (this.eq(other))\n            return 0;\n        var thisNeg = this.isNegative(), otherNeg = other.isNegative();\n        if (thisNeg && !otherNeg)\n            return -1;\n        if (!thisNeg && otherNeg)\n            return 1;\n        // At this point the sign bits are the same\n        if (!this.unsigned)\n            return this.sub(other).isNegative() ? -1 : 1;\n        // Both are positive if at least one is unsigned\n        return other.high >>> 0 > this.high >>> 0 ||\n            (other.high === this.high && other.low >>> 0 > this.low >>> 0)\n            ? -1\n            : 1;\n    };\n    /** This is an alias of {@link Long.compare} */\n    Long.prototype.comp = function (other) {\n        return this.compare(other);\n    };\n    /**\n     * Returns this Long divided by the specified. The result is signed if this Long is signed or unsigned if this Long is unsigned.\n     * @returns Quotient\n     */\n    Long.prototype.divide = function (divisor) {\n        if (!Long.isLong(divisor))\n            divisor = Long.fromValue(divisor);\n        if (divisor.isZero())\n            throw Error('division by zero');\n        // use wasm support if present\n        if (wasm) {\n            // guard against signed division overflow: the largest\n            // negative number / -1 would be 1 larger than the largest\n            // positive number, due to two's complement.\n            if (!this.unsigned &&\n                this.high === -0x80000000 &&\n                divisor.low === -1 &&\n                divisor.high === -1) {\n                // be consistent with non-wasm code path\n                return this;\n            }\n            var low = (this.unsigned ? wasm.div_u : wasm.div_s)(this.low, this.high, divisor.low, divisor.high);\n            return Long.fromBits(low, wasm.get_high(), this.unsigned);\n        }\n        if (this.isZero())\n            return this.unsigned ? Long.UZERO : Long.ZERO;\n        var approx, rem, res;\n        if (!this.unsigned) {\n            // This section is only relevant for signed longs and is derived from the\n            // closure library as a whole.\n            if (this.eq(Long.MIN_VALUE)) {\n                if (divisor.eq(Long.ONE) || divisor.eq(Long.NEG_ONE))\n                    return Long.MIN_VALUE;\n                // recall that -MIN_VALUE == MIN_VALUE\n                else if (divisor.eq(Long.MIN_VALUE))\n                    return Long.ONE;\n                else {\n                    // At this point, we have |other| >= 2, so |this/other| < |MIN_VALUE|.\n                    var halfThis = this.shr(1);\n                    approx = halfThis.div(divisor).shl(1);\n                    if (approx.eq(Long.ZERO)) {\n                        return divisor.isNegative() ? Long.ONE : Long.NEG_ONE;\n                    }\n                    else {\n                        rem = this.sub(divisor.mul(approx));\n                        res = approx.add(rem.div(divisor));\n                        return res;\n                    }\n                }\n            }\n            else if (divisor.eq(Long.MIN_VALUE))\n                return this.unsigned ? Long.UZERO : Long.ZERO;\n            if (this.isNegative()) {\n                if (divisor.isNegative())\n                    return this.neg().div(divisor.neg());\n                return this.neg().div(divisor).neg();\n            }\n            else if (divisor.isNegative())\n                return this.div(divisor.neg()).neg();\n            res = Long.ZERO;\n        }\n        else {\n            // The algorithm below has not been made for unsigned longs. It's therefore\n            // required to take special care of the MSB prior to running it.\n            if (!divisor.unsigned)\n                divisor = divisor.toUnsigned();\n            if (divisor.gt(this))\n                return Long.UZERO;\n            if (divisor.gt(this.shru(1)))\n                // 15 >>> 1 = 7 ; with divisor = 8 ; true\n                return Long.UONE;\n            res = Long.UZERO;\n        }\n        // Repeat the following until the remainder is less than other:  find a\n        // floating-point that approximates remainder / other *from below*, add this\n        // into the result, and subtract it from the remainder.  It is critical that\n        // the approximate value is less than or equal to the real value so that the\n        // remainder never becomes negative.\n        // eslint-disable-next-line @typescript-eslint/no-this-alias\n        rem = this;\n        while (rem.gte(divisor)) {\n            // Approximate the result of division. This may be a little greater or\n            // smaller than the actual value.\n            approx = Math.max(1, Math.floor(rem.toNumber() / divisor.toNumber()));\n            // We will tweak the approximate result by changing it in the 48-th digit or\n            // the smallest non-fractional digit, whichever is larger.\n            var log2 = Math.ceil(Math.log(approx) / Math.LN2);\n            var delta = log2 <= 48 ? 1 : Math.pow(2, log2 - 48);\n            // Decrease the approximation until it is smaller than the remainder.  Note\n            // that if it is too large, the product overflows and is negative.\n            var approxRes = Long.fromNumber(approx);\n            var approxRem = approxRes.mul(divisor);\n            while (approxRem.isNegative() || approxRem.gt(rem)) {\n                approx -= delta;\n                approxRes = Long.fromNumber(approx, this.unsigned);\n                approxRem = approxRes.mul(divisor);\n            }\n            // We know the answer can't be zero... and actually, zero would cause\n            // infinite recursion since we would make no progress.\n            if (approxRes.isZero())\n                approxRes = Long.ONE;\n            res = res.add(approxRes);\n            rem = rem.sub(approxRem);\n        }\n        return res;\n    };\n    /**This is an alias of {@link Long.divide} */\n    Long.prototype.div = function (divisor) {\n        return this.divide(divisor);\n    };\n    /**\n     * Tests if this Long's value equals the specified's.\n     * @param other - Other value\n     */\n    Long.prototype.equals = function (other) {\n        if (!Long.isLong(other))\n            other = Long.fromValue(other);\n        if (this.unsigned !== other.unsigned && this.high >>> 31 === 1 && other.high >>> 31 === 1)\n            return false;\n        return this.high === other.high && this.low === other.low;\n    };\n    /** This is an alias of {@link Long.equals} */\n    Long.prototype.eq = function (other) {\n        return this.equals(other);\n    };\n    /** Gets the high 32 bits as a signed integer. */\n    Long.prototype.getHighBits = function () {\n        return this.high;\n    };\n    /** Gets the high 32 bits as an unsigned integer. */\n    Long.prototype.getHighBitsUnsigned = function () {\n        return this.high >>> 0;\n    };\n    /** Gets the low 32 bits as a signed integer. */\n    Long.prototype.getLowBits = function () {\n        return this.low;\n    };\n    /** Gets the low 32 bits as an unsigned integer. */\n    Long.prototype.getLowBitsUnsigned = function () {\n        return this.low >>> 0;\n    };\n    /** Gets the number of bits needed to represent the absolute value of this Long. */\n    Long.prototype.getNumBitsAbs = function () {\n        if (this.isNegative()) {\n            // Unsigned Longs are never negative\n            return this.eq(Long.MIN_VALUE) ? 64 : this.neg().getNumBitsAbs();\n        }\n        var val = this.high !== 0 ? this.high : this.low;\n        var bit;\n        for (bit = 31; bit > 0; bit--)\n            if ((val & (1 << bit)) !== 0)\n                break;\n        return this.high !== 0 ? bit + 33 : bit + 1;\n    };\n    /** Tests if this Long's value is greater than the specified's. */\n    Long.prototype.greaterThan = function (other) {\n        return this.comp(other) > 0;\n    };\n    /** This is an alias of {@link Long.greaterThan} */\n    Long.prototype.gt = function (other) {\n        return this.greaterThan(other);\n    };\n    /** Tests if this Long's value is greater than or equal the specified's. */\n    Long.prototype.greaterThanOrEqual = function (other) {\n        return this.comp(other) >= 0;\n    };\n    /** This is an alias of {@link Long.greaterThanOrEqual} */\n    Long.prototype.gte = function (other) {\n        return this.greaterThanOrEqual(other);\n    };\n    /** This is an alias of {@link Long.greaterThanOrEqual} */\n    Long.prototype.ge = function (other) {\n        return this.greaterThanOrEqual(other);\n    };\n    /** Tests if this Long's value is even. */\n    Long.prototype.isEven = function () {\n        return (this.low & 1) === 0;\n    };\n    /** Tests if this Long's value is negative. */\n    Long.prototype.isNegative = function () {\n        return !this.unsigned && this.high < 0;\n    };\n    /** Tests if this Long's value is odd. */\n    Long.prototype.isOdd = function () {\n        return (this.low & 1) === 1;\n    };\n    /** Tests if this Long's value is positive. */\n    Long.prototype.isPositive = function () {\n        return this.unsigned || this.high >= 0;\n    };\n    /** Tests if this Long's value equals zero. */\n    Long.prototype.isZero = function () {\n        return this.high === 0 && this.low === 0;\n    };\n    /** Tests if this Long's value is less than the specified's. */\n    Long.prototype.lessThan = function (other) {\n        return this.comp(other) < 0;\n    };\n    /** This is an alias of {@link Long#lessThan}. */\n    Long.prototype.lt = function (other) {\n        return this.lessThan(other);\n    };\n    /** Tests if this Long's value is less than or equal the specified's. */\n    Long.prototype.lessThanOrEqual = function (other) {\n        return this.comp(other) <= 0;\n    };\n    /** This is an alias of {@link Long.lessThanOrEqual} */\n    Long.prototype.lte = function (other) {\n        return this.lessThanOrEqual(other);\n    };\n    /** Returns this Long modulo the specified. */\n    Long.prototype.modulo = function (divisor) {\n        if (!Long.isLong(divisor))\n            divisor = Long.fromValue(divisor);\n        // use wasm support if present\n        if (wasm) {\n            var low = (this.unsigned ? wasm.rem_u : wasm.rem_s)(this.low, this.high, divisor.low, divisor.high);\n            return Long.fromBits(low, wasm.get_high(), this.unsigned);\n        }\n        return this.sub(this.div(divisor).mul(divisor));\n    };\n    /** This is an alias of {@link Long.modulo} */\n    Long.prototype.mod = function (divisor) {\n        return this.modulo(divisor);\n    };\n    /** This is an alias of {@link Long.modulo} */\n    Long.prototype.rem = function (divisor) {\n        return this.modulo(divisor);\n    };\n    /**\n     * Returns the product of this and the specified Long.\n     * @param multiplier - Multiplier\n     * @returns Product\n     */\n    Long.prototype.multiply = function (multiplier) {\n        if (this.isZero())\n            return Long.ZERO;\n        if (!Long.isLong(multiplier))\n            multiplier = Long.fromValue(multiplier);\n        // use wasm support if present\n        if (wasm) {\n            var low = wasm.mul(this.low, this.high, multiplier.low, multiplier.high);\n            return Long.fromBits(low, wasm.get_high(), this.unsigned);\n        }\n        if (multiplier.isZero())\n            return Long.ZERO;\n        if (this.eq(Long.MIN_VALUE))\n            return multiplier.isOdd() ? Long.MIN_VALUE : Long.ZERO;\n        if (multiplier.eq(Long.MIN_VALUE))\n            return this.isOdd() ? Long.MIN_VALUE : Long.ZERO;\n        if (this.isNegative()) {\n            if (multiplier.isNegative())\n                return this.neg().mul(multiplier.neg());\n            else\n                return this.neg().mul(multiplier).neg();\n        }\n        else if (multiplier.isNegative())\n            return this.mul(multiplier.neg()).neg();\n        // If both longs are small, use float multiplication\n        if (this.lt(Long.TWO_PWR_24) && multiplier.lt(Long.TWO_PWR_24))\n            return Long.fromNumber(this.toNumber() * multiplier.toNumber(), this.unsigned);\n        // Divide each long into 4 chunks of 16 bits, and then add up 4x4 products.\n        // We can skip products that would overflow.\n        var a48 = this.high >>> 16;\n        var a32 = this.high & 0xffff;\n        var a16 = this.low >>> 16;\n        var a00 = this.low & 0xffff;\n        var b48 = multiplier.high >>> 16;\n        var b32 = multiplier.high & 0xffff;\n        var b16 = multiplier.low >>> 16;\n        var b00 = multiplier.low & 0xffff;\n        var c48 = 0, c32 = 0, c16 = 0, c00 = 0;\n        c00 += a00 * b00;\n        c16 += c00 >>> 16;\n        c00 &= 0xffff;\n        c16 += a16 * b00;\n        c32 += c16 >>> 16;\n        c16 &= 0xffff;\n        c16 += a00 * b16;\n        c32 += c16 >>> 16;\n        c16 &= 0xffff;\n        c32 += a32 * b00;\n        c48 += c32 >>> 16;\n        c32 &= 0xffff;\n        c32 += a16 * b16;\n        c48 += c32 >>> 16;\n        c32 &= 0xffff;\n        c32 += a00 * b32;\n        c48 += c32 >>> 16;\n        c32 &= 0xffff;\n        c48 += a48 * b00 + a32 * b16 + a16 * b32 + a00 * b48;\n        c48 &= 0xffff;\n        return Long.fromBits((c16 << 16) | c00, (c48 << 16) | c32, this.unsigned);\n    };\n    /** This is an alias of {@link Long.multiply} */\n    Long.prototype.mul = function (multiplier) {\n        return this.multiply(multiplier);\n    };\n    /** Returns the Negation of this Long's value. */\n    Long.prototype.negate = function () {\n        if (!this.unsigned && this.eq(Long.MIN_VALUE))\n            return Long.MIN_VALUE;\n        return this.not().add(Long.ONE);\n    };\n    /** This is an alias of {@link Long.negate} */\n    Long.prototype.neg = function () {\n        return this.negate();\n    };\n    /** Returns the bitwise NOT of this Long. */\n    Long.prototype.not = function () {\n        return Long.fromBits(~this.low, ~this.high, this.unsigned);\n    };\n    /** Tests if this Long's value differs from the specified's. */\n    Long.prototype.notEquals = function (other) {\n        return !this.equals(other);\n    };\n    /** This is an alias of {@link Long.notEquals} */\n    Long.prototype.neq = function (other) {\n        return this.notEquals(other);\n    };\n    /** This is an alias of {@link Long.notEquals} */\n    Long.prototype.ne = function (other) {\n        return this.notEquals(other);\n    };\n    /**\n     * Returns the bitwise OR of this Long and the specified.\n     */\n    Long.prototype.or = function (other) {\n        if (!Long.isLong(other))\n            other = Long.fromValue(other);\n        return Long.fromBits(this.low | other.low, this.high | other.high, this.unsigned);\n    };\n    /**\n     * Returns this Long with bits shifted to the left by the given amount.\n     * @param numBits - Number of bits\n     * @returns Shifted Long\n     */\n    Long.prototype.shiftLeft = function (numBits) {\n        if (Long.isLong(numBits))\n            numBits = numBits.toInt();\n        if ((numBits &= 63) === 0)\n            return this;\n        else if (numBits < 32)\n            return Long.fromBits(this.low << numBits, (this.high << numBits) | (this.low >>> (32 - numBits)), this.unsigned);\n        else\n            return Long.fromBits(0, this.low << (numBits - 32), this.unsigned);\n    };\n    /** This is an alias of {@link Long.shiftLeft} */\n    Long.prototype.shl = function (numBits) {\n        return this.shiftLeft(numBits);\n    };\n    /**\n     * Returns this Long with bits arithmetically shifted to the right by the given amount.\n     * @param numBits - Number of bits\n     * @returns Shifted Long\n     */\n    Long.prototype.shiftRight = function (numBits) {\n        if (Long.isLong(numBits))\n            numBits = numBits.toInt();\n        if ((numBits &= 63) === 0)\n            return this;\n        else if (numBits < 32)\n            return Long.fromBits((this.low >>> numBits) | (this.high << (32 - numBits)), this.high >> numBits, this.unsigned);\n        else\n            return Long.fromBits(this.high >> (numBits - 32), this.high >= 0 ? 0 : -1, this.unsigned);\n    };\n    /** This is an alias of {@link Long.shiftRight} */\n    Long.prototype.shr = function (numBits) {\n        return this.shiftRight(numBits);\n    };\n    /**\n     * Returns this Long with bits logically shifted to the right by the given amount.\n     * @param numBits - Number of bits\n     * @returns Shifted Long\n     */\n    Long.prototype.shiftRightUnsigned = function (numBits) {\n        if (Long.isLong(numBits))\n            numBits = numBits.toInt();\n        numBits &= 63;\n        if (numBits === 0)\n            return this;\n        else {\n            var high = this.high;\n            if (numBits < 32) {\n                var low = this.low;\n                return Long.fromBits((low >>> numBits) | (high << (32 - numBits)), high >>> numBits, this.unsigned);\n            }\n            else if (numBits === 32)\n                return Long.fromBits(high, 0, this.unsigned);\n            else\n                return Long.fromBits(high >>> (numBits - 32), 0, this.unsigned);\n        }\n    };\n    /** This is an alias of {@link Long.shiftRightUnsigned} */\n    Long.prototype.shr_u = function (numBits) {\n        return this.shiftRightUnsigned(numBits);\n    };\n    /** This is an alias of {@link Long.shiftRightUnsigned} */\n    Long.prototype.shru = function (numBits) {\n        return this.shiftRightUnsigned(numBits);\n    };\n    /**\n     * Returns the difference of this and the specified Long.\n     * @param subtrahend - Subtrahend\n     * @returns Difference\n     */\n    Long.prototype.subtract = function (subtrahend) {\n        if (!Long.isLong(subtrahend))\n            subtrahend = Long.fromValue(subtrahend);\n        return this.add(subtrahend.neg());\n    };\n    /** This is an alias of {@link Long.subtract} */\n    Long.prototype.sub = function (subtrahend) {\n        return this.subtract(subtrahend);\n    };\n    /** Converts the Long to a 32 bit integer, assuming it is a 32 bit integer. */\n    Long.prototype.toInt = function () {\n        return this.unsigned ? this.low >>> 0 : this.low;\n    };\n    /** Converts the Long to a the nearest floating-point representation of this value (double, 53 bit mantissa). */\n    Long.prototype.toNumber = function () {\n        if (this.unsigned)\n            return (this.high >>> 0) * TWO_PWR_32_DBL + (this.low >>> 0);\n        return this.high * TWO_PWR_32_DBL + (this.low >>> 0);\n    };\n    /** Converts the Long to a BigInt (arbitrary precision). */\n    Long.prototype.toBigInt = function () {\n        return BigInt(this.toString());\n    };\n    /**\n     * Converts this Long to its byte representation.\n     * @param le - Whether little or big endian, defaults to big endian\n     * @returns Byte representation\n     */\n    Long.prototype.toBytes = function (le) {\n        return le ? this.toBytesLE() : this.toBytesBE();\n    };\n    /**\n     * Converts this Long to its little endian byte representation.\n     * @returns Little endian byte representation\n     */\n    Long.prototype.toBytesLE = function () {\n        var hi = this.high, lo = this.low;\n        return [\n            lo & 0xff,\n            (lo >>> 8) & 0xff,\n            (lo >>> 16) & 0xff,\n            lo >>> 24,\n            hi & 0xff,\n            (hi >>> 8) & 0xff,\n            (hi >>> 16) & 0xff,\n            hi >>> 24\n        ];\n    };\n    /**\n     * Converts this Long to its big endian byte representation.\n     * @returns Big endian byte representation\n     */\n    Long.prototype.toBytesBE = function () {\n        var hi = this.high, lo = this.low;\n        return [\n            hi >>> 24,\n            (hi >>> 16) & 0xff,\n            (hi >>> 8) & 0xff,\n            hi & 0xff,\n            lo >>> 24,\n            (lo >>> 16) & 0xff,\n            (lo >>> 8) & 0xff,\n            lo & 0xff\n        ];\n    };\n    /**\n     * Converts this Long to signed.\n     */\n    Long.prototype.toSigned = function () {\n        if (!this.unsigned)\n            return this;\n        return Long.fromBits(this.low, this.high, false);\n    };\n    /**\n     * Converts the Long to a string written in the specified radix.\n     * @param radix - Radix (2-36), defaults to 10\n     * @throws RangeError If `radix` is out of range\n     */\n    Long.prototype.toString = function (radix) {\n        radix = radix || 10;\n        if (radix < 2 || 36 < radix)\n            throw RangeError('radix');\n        if (this.isZero())\n            return '0';\n        if (this.isNegative()) {\n            // Unsigned Longs are never negative\n            if (this.eq(Long.MIN_VALUE)) {\n                // We need to change the Long value before it can be negated, so we remove\n                // the bottom-most digit in this base and then recurse to do the rest.\n                var radixLong = Long.fromNumber(radix), div = this.div(radixLong), rem1 = div.mul(radixLong).sub(this);\n                return div.toString(radix) + rem1.toInt().toString(radix);\n            }\n            else\n                return '-' + this.neg().toString(radix);\n        }\n        // Do several (6) digits each time through the loop, so as to\n        // minimize the calls to the very expensive emulated div.\n        var radixToPower = Long.fromNumber(Math.pow(radix, 6), this.unsigned);\n        // eslint-disable-next-line @typescript-eslint/no-this-alias\n        var rem = this;\n        var result = '';\n        // eslint-disable-next-line no-constant-condition\n        while (true) {\n            var remDiv = rem.div(radixToPower);\n            var intval = rem.sub(remDiv.mul(radixToPower)).toInt() >>> 0;\n            var digits = intval.toString(radix);\n            rem = remDiv;\n            if (rem.isZero()) {\n                return digits + result;\n            }\n            else {\n                while (digits.length < 6)\n                    digits = '0' + digits;\n                result = '' + digits + result;\n            }\n        }\n    };\n    /** Converts this Long to unsigned. */\n    Long.prototype.toUnsigned = function () {\n        if (this.unsigned)\n            return this;\n        return Long.fromBits(this.low, this.high, true);\n    };\n    /** Returns the bitwise XOR of this Long and the given one. */\n    Long.prototype.xor = function (other) {\n        if (!Long.isLong(other))\n            other = Long.fromValue(other);\n        return Long.fromBits(this.low ^ other.low, this.high ^ other.high, this.unsigned);\n    };\n    /** This is an alias of {@link Long.isZero} */\n    Long.prototype.eqz = function () {\n        return this.isZero();\n    };\n    /** This is an alias of {@link Long.lessThanOrEqual} */\n    Long.prototype.le = function (other) {\n        return this.lessThanOrEqual(other);\n    };\n    /*\n     ****************************************************************\n     *                  BSON SPECIFIC ADDITIONS                     *\n     ****************************************************************\n     */\n    Long.prototype.toExtendedJSON = function (options) {\n        if (options && options.relaxed)\n            return this.toNumber();\n        return { $numberLong: this.toString() };\n    };\n    Long.fromExtendedJSON = function (doc, options) {\n        var result = Long.fromString(doc.$numberLong);\n        return options && options.relaxed ? result.toNumber() : result;\n    };\n    /** @internal */\n    Long.prototype[Symbol.for('nodejs.util.inspect.custom')] = function () {\n        return this.inspect();\n    };\n    Long.prototype.inspect = function () {\n        return \"new Long(\\\"\".concat(this.toString(), \"\\\"\").concat(this.unsigned ? ', true' : '', \")\");\n    };\n    Long.TWO_PWR_24 = Long.fromInt(TWO_PWR_24_DBL);\n    /** Maximum unsigned value. */\n    Long.MAX_UNSIGNED_VALUE = Long.fromBits(0xffffffff | 0, 0xffffffff | 0, true);\n    /** Signed zero */\n    Long.ZERO = Long.fromInt(0);\n    /** Unsigned zero. */\n    Long.UZERO = Long.fromInt(0, true);\n    /** Signed one. */\n    Long.ONE = Long.fromInt(1);\n    /** Unsigned one. */\n    Long.UONE = Long.fromInt(1, true);\n    /** Signed negative one. */\n    Long.NEG_ONE = Long.fromInt(-1);\n    /** Maximum signed value. */\n    Long.MAX_VALUE = Long.fromBits(0xffffffff | 0, 0x7fffffff | 0, false);\n    /** Minimum signed value. */\n    Long.MIN_VALUE = Long.fromBits(0, 0x80000000 | 0, false);\n    return Long;\n}());\nexports.Long = Long;\nObject.defineProperty(Long.prototype, '__isLong__', { value: true });\nObject.defineProperty(Long.prototype, '_bsontype', { value: 'Long' });\n//# sourceMappingURL=long.js.map", "\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.Double = void 0;\n/**\n * A class representation of the BSON Double type.\n * @public\n * @category BSONType\n */\nvar Double = /** @class */ (function () {\n    /**\n     * Create a Double type\n     *\n     * @param value - the number we want to represent as a double.\n     */\n    function Double(value) {\n        if (!(this instanceof Double))\n            return new Double(value);\n        if (value instanceof Number) {\n            value = value.valueOf();\n        }\n        this.value = +value;\n    }\n    /**\n     * Access the number value.\n     *\n     * @returns returns the wrapped double number.\n     */\n    Double.prototype.valueOf = function () {\n        return this.value;\n    };\n    Double.prototype.toJSON = function () {\n        return this.value;\n    };\n    Double.prototype.toString = function (radix) {\n        return this.value.toString(radix);\n    };\n    /** @internal */\n    Double.prototype.toExtendedJSON = function (options) {\n        if (options && (options.legacy || (options.relaxed && isFinite(this.value)))) {\n            return this.value;\n        }\n        if (Object.is(Math.sign(this.value), -0)) {\n            // NOTE: JavaScript has +0 and -0, apparently to model limit calculations. If a user\n            // explicitly provided `-0` then we need to ensure the sign makes it into the output\n            return { $numberDouble: \"-\".concat(this.value.toFixed(1)) };\n        }\n        return {\n            $numberDouble: Number.isInteger(this.value) ? this.value.toFixed(1) : this.value.toString()\n        };\n    };\n    /** @internal */\n    Double.fromExtendedJSON = function (doc, options) {\n        var doubleValue = parseFloat(doc.$numberDouble);\n        return options && options.relaxed ? doubleValue : new Double(doubleValue);\n    };\n    /** @internal */\n    Double.prototype[Symbol.for('nodejs.util.inspect.custom')] = function () {\n        return this.inspect();\n    };\n    Double.prototype.inspect = function () {\n        var eJSON = this.toExtendedJSON();\n        return \"new Double(\".concat(eJSON.$numberDouble, \")\");\n    };\n    return Double;\n}());\nexports.Double = Double;\nObject.defineProperty(Double.prototype, '_bsontype', { value: 'Double' });\n//# sourceMappingURL=double.js.map", "\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.EJSON = exports.isBSONType = void 0;\nvar binary_1 = require(\"./binary\");\nvar code_1 = require(\"./code\");\nvar db_ref_1 = require(\"./db_ref\");\nvar decimal128_1 = require(\"./decimal128\");\nvar double_1 = require(\"./double\");\nvar error_1 = require(\"./error\");\nvar int_32_1 = require(\"./int_32\");\nvar long_1 = require(\"./long\");\nvar max_key_1 = require(\"./max_key\");\nvar min_key_1 = require(\"./min_key\");\nvar objectid_1 = require(\"./objectid\");\nvar utils_1 = require(\"./parser/utils\");\nvar regexp_1 = require(\"./regexp\");\nvar symbol_1 = require(\"./symbol\");\nvar timestamp_1 = require(\"./timestamp\");\nfunction isBSONType(value) {\n    return ((0, utils_1.isObjectLike)(value) && Reflect.has(value, '_bsontype') && typeof value._bsontype === 'string');\n}\nexports.isBSONType = isBSONType;\n// INT32 boundaries\nvar BSON_INT32_MAX = 0x7fffffff;\nvar BSON_INT32_MIN = -0x80000000;\n// INT64 boundaries\n// const BSON_INT64_MAX = 0x7fffffffffffffff; // TODO(NODE-4377): This number cannot be precisely represented in JS\nvar BSON_INT64_MAX = 0x8000000000000000;\nvar BSON_INT64_MIN = -0x8000000000000000;\n// all the types where we don't need to do any special processing and can just pass the EJSON\n//straight to type.fromExtendedJSON\nvar keysToCodecs = {\n    $oid: objectid_1.ObjectId,\n    $binary: binary_1.Binary,\n    $uuid: binary_1.Binary,\n    $symbol: symbol_1.BSONSymbol,\n    $numberInt: int_32_1.Int32,\n    $numberDecimal: decimal128_1.Decimal128,\n    $numberDouble: double_1.Double,\n    $numberLong: long_1.Long,\n    $minKey: min_key_1.MinKey,\n    $maxKey: max_key_1.MaxKey,\n    $regex: regexp_1.BSONRegExp,\n    $regularExpression: regexp_1.BSONRegExp,\n    $timestamp: timestamp_1.Timestamp\n};\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nfunction deserializeValue(value, options) {\n    if (options === void 0) { options = {}; }\n    if (typeof value === 'number') {\n        if (options.relaxed || options.legacy) {\n            return value;\n        }\n        // if it's an integer, should interpret as smallest BSON integer\n        // that can represent it exactly. (if out of range, interpret as double.)\n        if (Math.floor(value) === value) {\n            if (value >= BSON_INT32_MIN && value <= BSON_INT32_MAX)\n                return new int_32_1.Int32(value);\n            if (value >= BSON_INT64_MIN && value <= BSON_INT64_MAX)\n                return long_1.Long.fromNumber(value);\n        }\n        // If the number is a non-integer or out of integer range, should interpret as BSON Double.\n        return new double_1.Double(value);\n    }\n    // from here on out we're looking for bson types, so bail if its not an object\n    if (value == null || typeof value !== 'object')\n        return value;\n    // upgrade deprecated undefined to null\n    if (value.$undefined)\n        return null;\n    var keys = Object.keys(value).filter(function (k) { return k.startsWith('$') && value[k] != null; });\n    for (var i = 0; i < keys.length; i++) {\n        var c = keysToCodecs[keys[i]];\n        if (c)\n            return c.fromExtendedJSON(value, options);\n    }\n    if (value.$date != null) {\n        var d = value.$date;\n        var date = new Date();\n        if (options.legacy) {\n            if (typeof d === 'number')\n                date.setTime(d);\n            else if (typeof d === 'string')\n                date.setTime(Date.parse(d));\n        }\n        else {\n            if (typeof d === 'string')\n                date.setTime(Date.parse(d));\n            else if (long_1.Long.isLong(d))\n                date.setTime(d.toNumber());\n            else if (typeof d === 'number' && options.relaxed)\n                date.setTime(d);\n        }\n        return date;\n    }\n    if (value.$code != null) {\n        var copy = Object.assign({}, value);\n        if (value.$scope) {\n            copy.$scope = deserializeValue(value.$scope);\n        }\n        return code_1.Code.fromExtendedJSON(value);\n    }\n    if ((0, db_ref_1.isDBRefLike)(value) || value.$dbPointer) {\n        var v = value.$ref ? value : value.$dbPointer;\n        // we run into this in a \"degenerate EJSON\" case (with $id and $ref order flipped)\n        // because of the order JSON.parse goes through the document\n        if (v instanceof db_ref_1.DBRef)\n            return v;\n        var dollarKeys = Object.keys(v).filter(function (k) { return k.startsWith('$'); });\n        var valid_1 = true;\n        dollarKeys.forEach(function (k) {\n            if (['$ref', '$id', '$db'].indexOf(k) === -1)\n                valid_1 = false;\n        });\n        // only make DBRef if $ keys are all valid\n        if (valid_1)\n            return db_ref_1.DBRef.fromExtendedJSON(v);\n    }\n    return value;\n}\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nfunction serializeArray(array, options) {\n    return array.map(function (v, index) {\n        options.seenObjects.push({ propertyName: \"index \".concat(index), obj: null });\n        try {\n            return serializeValue(v, options);\n        }\n        finally {\n            options.seenObjects.pop();\n        }\n    });\n}\nfunction getISOString(date) {\n    var isoStr = date.toISOString();\n    // we should only show milliseconds in timestamp if they're non-zero\n    return date.getUTCMilliseconds() !== 0 ? isoStr : isoStr.slice(0, -5) + 'Z';\n}\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nfunction serializeValue(value, options) {\n    if ((typeof value === 'object' || typeof value === 'function') && value !== null) {\n        var index = options.seenObjects.findIndex(function (entry) { return entry.obj === value; });\n        if (index !== -1) {\n            var props = options.seenObjects.map(function (entry) { return entry.propertyName; });\n            var leadingPart = props\n                .slice(0, index)\n                .map(function (prop) { return \"\".concat(prop, \" -> \"); })\n                .join('');\n            var alreadySeen = props[index];\n            var circularPart = ' -> ' +\n                props\n                    .slice(index + 1, props.length - 1)\n                    .map(function (prop) { return \"\".concat(prop, \" -> \"); })\n                    .join('');\n            var current = props[props.length - 1];\n            var leadingSpace = ' '.repeat(leadingPart.length + alreadySeen.length / 2);\n            var dashes = '-'.repeat(circularPart.length + (alreadySeen.length + current.length) / 2 - 1);\n            throw new error_1.BSONTypeError('Converting circular structure to EJSON:\\n' +\n                \"    \".concat(leadingPart).concat(alreadySeen).concat(circularPart).concat(current, \"\\n\") +\n                \"    \".concat(leadingSpace, \"\\\\\").concat(dashes, \"/\"));\n        }\n        options.seenObjects[options.seenObjects.length - 1].obj = value;\n    }\n    if (Array.isArray(value))\n        return serializeArray(value, options);\n    if (value === undefined)\n        return null;\n    if (value instanceof Date || (0, utils_1.isDate)(value)) {\n        var dateNum = value.getTime(), \n        // is it in year range 1970-9999?\n        inRange = dateNum > -1 && dateNum < 253402318800000;\n        if (options.legacy) {\n            return options.relaxed && inRange\n                ? { $date: value.getTime() }\n                : { $date: getISOString(value) };\n        }\n        return options.relaxed && inRange\n            ? { $date: getISOString(value) }\n            : { $date: { $numberLong: value.getTime().toString() } };\n    }\n    if (typeof value === 'number' && (!options.relaxed || !isFinite(value))) {\n        // it's an integer\n        if (Math.floor(value) === value) {\n            var int32Range = value >= BSON_INT32_MIN && value <= BSON_INT32_MAX, int64Range = value >= BSON_INT64_MIN && value <= BSON_INT64_MAX;\n            // interpret as being of the smallest BSON integer type that can represent the number exactly\n            if (int32Range)\n                return { $numberInt: value.toString() };\n            if (int64Range)\n                return { $numberLong: value.toString() };\n        }\n        return { $numberDouble: value.toString() };\n    }\n    if (value instanceof RegExp || (0, utils_1.isRegExp)(value)) {\n        var flags = value.flags;\n        if (flags === undefined) {\n            var match = value.toString().match(/[gimuy]*$/);\n            if (match) {\n                flags = match[0];\n            }\n        }\n        var rx = new regexp_1.BSONRegExp(value.source, flags);\n        return rx.toExtendedJSON(options);\n    }\n    if (value != null && typeof value === 'object')\n        return serializeDocument(value, options);\n    return value;\n}\nvar BSON_TYPE_MAPPINGS = {\n    Binary: function (o) { return new binary_1.Binary(o.value(), o.sub_type); },\n    Code: function (o) { return new code_1.Code(o.code, o.scope); },\n    DBRef: function (o) { return new db_ref_1.DBRef(o.collection || o.namespace, o.oid, o.db, o.fields); },\n    Decimal128: function (o) { return new decimal128_1.Decimal128(o.bytes); },\n    Double: function (o) { return new double_1.Double(o.value); },\n    Int32: function (o) { return new int_32_1.Int32(o.value); },\n    Long: function (o) {\n        return long_1.Long.fromBits(\n        // underscore variants for 1.x backwards compatibility\n        o.low != null ? o.low : o.low_, o.low != null ? o.high : o.high_, o.low != null ? o.unsigned : o.unsigned_);\n    },\n    MaxKey: function () { return new max_key_1.MaxKey(); },\n    MinKey: function () { return new min_key_1.MinKey(); },\n    ObjectID: function (o) { return new objectid_1.ObjectId(o); },\n    ObjectId: function (o) { return new objectid_1.ObjectId(o); },\n    BSONRegExp: function (o) { return new regexp_1.BSONRegExp(o.pattern, o.options); },\n    Symbol: function (o) { return new symbol_1.BSONSymbol(o.value); },\n    Timestamp: function (o) { return timestamp_1.Timestamp.fromBits(o.low, o.high); }\n};\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nfunction serializeDocument(doc, options) {\n    if (doc == null || typeof doc !== 'object')\n        throw new error_1.BSONError('not an object instance');\n    var bsontype = doc._bsontype;\n    if (typeof bsontype === 'undefined') {\n        // It's a regular object. Recursively serialize its property values.\n        var _doc = {};\n        for (var name in doc) {\n            options.seenObjects.push({ propertyName: name, obj: null });\n            try {\n                var value = serializeValue(doc[name], options);\n                if (name === '__proto__') {\n                    Object.defineProperty(_doc, name, {\n                        value: value,\n                        writable: true,\n                        enumerable: true,\n                        configurable: true\n                    });\n                }\n                else {\n                    _doc[name] = value;\n                }\n            }\n            finally {\n                options.seenObjects.pop();\n            }\n        }\n        return _doc;\n    }\n    else if (isBSONType(doc)) {\n        // the \"document\" is really just a BSON type object\n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\n        var outDoc = doc;\n        if (typeof outDoc.toExtendedJSON !== 'function') {\n            // There's no EJSON serialization function on the object. It's probably an\n            // object created by a previous version of this library (or another library)\n            // that's duck-typing objects to look like they were generated by this library).\n            // Copy the object into this library's version of that type.\n            var mapper = BSON_TYPE_MAPPINGS[doc._bsontype];\n            if (!mapper) {\n                throw new error_1.BSONTypeError('Unrecognized or invalid _bsontype: ' + doc._bsontype);\n            }\n            outDoc = mapper(outDoc);\n        }\n        // Two BSON types may have nested objects that may need to be serialized too\n        if (bsontype === 'Code' && outDoc.scope) {\n            outDoc = new code_1.Code(outDoc.code, serializeValue(outDoc.scope, options));\n        }\n        else if (bsontype === 'DBRef' && outDoc.oid) {\n            outDoc = new db_ref_1.DBRef(serializeValue(outDoc.collection, options), serializeValue(outDoc.oid, options), serializeValue(outDoc.db, options), serializeValue(outDoc.fields, options));\n        }\n        return outDoc.toExtendedJSON(options);\n    }\n    else {\n        throw new error_1.BSONError('_bsontype must be a string, but was: ' + typeof bsontype);\n    }\n}\n/**\n * EJSON parse / stringify API\n * @public\n */\n// the namespace here is used to emulate `export * as EJSON from '...'`\n// which as of now (sept 2020) api-extractor does not support\n// eslint-disable-next-line @typescript-eslint/no-namespace\nvar EJSON;\n(function (EJSON) {\n    /**\n     * Parse an Extended JSON string, constructing the JavaScript value or object described by that\n     * string.\n     *\n     * @example\n     * ```js\n     * const { EJSON } = require('bson');\n     * const text = '{ \"int32\": { \"$numberInt\": \"10\" } }';\n     *\n     * // prints { int32: { [String: '10'] _bsontype: 'Int32', value: '10' } }\n     * console.log(EJSON.parse(text, { relaxed: false }));\n     *\n     * // prints { int32: 10 }\n     * console.log(EJSON.parse(text));\n     * ```\n     */\n    function parse(text, options) {\n        var finalOptions = Object.assign({}, { relaxed: true, legacy: false }, options);\n        // relaxed implies not strict\n        if (typeof finalOptions.relaxed === 'boolean')\n            finalOptions.strict = !finalOptions.relaxed;\n        if (typeof finalOptions.strict === 'boolean')\n            finalOptions.relaxed = !finalOptions.strict;\n        return JSON.parse(text, function (key, value) {\n            if (key.indexOf('\\x00') !== -1) {\n                throw new error_1.BSONError(\"BSON Document field names cannot contain null bytes, found: \".concat(JSON.stringify(key)));\n            }\n            return deserializeValue(value, finalOptions);\n        });\n    }\n    EJSON.parse = parse;\n    /**\n     * Converts a BSON document to an Extended JSON string, optionally replacing values if a replacer\n     * function is specified or optionally including only the specified properties if a replacer array\n     * is specified.\n     *\n     * @param value - The value to convert to extended JSON\n     * @param replacer - A function that alters the behavior of the stringification process, or an array of String and Number objects that serve as a whitelist for selecting/filtering the properties of the value object to be included in the JSON string. If this value is null or not provided, all properties of the object are included in the resulting JSON string\n     * @param space - A String or Number object that's used to insert white space into the output JSON string for readability purposes.\n     * @param options - Optional settings\n     *\n     * @example\n     * ```js\n     * const { EJSON } = require('bson');\n     * const Int32 = require('mongodb').Int32;\n     * const doc = { int32: new Int32(10) };\n     *\n     * // prints '{\"int32\":{\"$numberInt\":\"10\"}}'\n     * console.log(EJSON.stringify(doc, { relaxed: false }));\n     *\n     * // prints '{\"int32\":10}'\n     * console.log(EJSON.stringify(doc));\n     * ```\n     */\n    function stringify(value, \n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    replacer, space, options) {\n        if (space != null && typeof space === 'object') {\n            options = space;\n            space = 0;\n        }\n        if (replacer != null && typeof replacer === 'object' && !Array.isArray(replacer)) {\n            options = replacer;\n            replacer = undefined;\n            space = 0;\n        }\n        var serializeOptions = Object.assign({ relaxed: true, legacy: false }, options, {\n            seenObjects: [{ propertyName: '(root)', obj: null }]\n        });\n        var doc = serializeValue(value, serializeOptions);\n        return JSON.stringify(doc, replacer, space);\n    }\n    EJSON.stringify = stringify;\n    /**\n     * Serializes an object to an Extended JSON string, and reparse it as a JavaScript object.\n     *\n     * @param value - The object to serialize\n     * @param options - Optional settings passed to the `stringify` function\n     */\n    function serialize(value, options) {\n        options = options || {};\n        return JSON.parse(stringify(value, options));\n    }\n    EJSON.serialize = serialize;\n    /**\n     * Deserializes an Extended JSON object into a plain JavaScript object with native/BSON types\n     *\n     * @param ejson - The Extended JSON object to deserialize\n     * @param options - Optional settings passed to the parse method\n     */\n    function deserialize(ejson, options) {\n        options = options || {};\n        return parse(JSON.stringify(ejson), options);\n    }\n    EJSON.deserialize = deserialize;\n})(EJSON = exports.EJSON || (exports.EJSON = {}));\n//# sourceMappingURL=extended_json.js.map", "\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.Int32 = void 0;\n/**\n * A class representation of a BSON Int32 type.\n * @public\n * @category BSONType\n */\nvar Int32 = /** @class */ (function () {\n    /**\n     * Create an Int32 type\n     *\n     * @param value - the number we want to represent as an int32.\n     */\n    function Int32(value) {\n        if (!(this instanceof Int32))\n            return new Int32(value);\n        if (value instanceof Number) {\n            value = value.valueOf();\n        }\n        this.value = +value | 0;\n    }\n    /**\n     * Access the number value.\n     *\n     * @returns returns the wrapped int32 number.\n     */\n    Int32.prototype.valueOf = function () {\n        return this.value;\n    };\n    Int32.prototype.toString = function (radix) {\n        return this.value.toString(radix);\n    };\n    Int32.prototype.toJSON = function () {\n        return this.value;\n    };\n    /** @internal */\n    Int32.prototype.toExtendedJSON = function (options) {\n        if (options && (options.relaxed || options.legacy))\n            return this.value;\n        return { $numberInt: this.value.toString() };\n    };\n    /** @internal */\n    Int32.fromExtendedJSON = function (doc, options) {\n        return options && options.relaxed ? parseInt(doc.$numberInt, 10) : new Int32(doc.$numberInt);\n    };\n    /** @internal */\n    Int32.prototype[Symbol.for('nodejs.util.inspect.custom')] = function () {\n        return this.inspect();\n    };\n    Int32.prototype.inspect = function () {\n        return \"new Int32(\".concat(this.valueOf(), \")\");\n    };\n    return Int32;\n}());\nexports.Int32 = Int32;\nObject.defineProperty(Int32.prototype, '_bsontype', { value: 'Int32' });\n//# sourceMappingURL=int_32.js.map", "\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.MaxKey = void 0;\n/**\n * A class representation of the BSON MaxKey type.\n * @public\n * @category BSONType\n */\nvar MaxKey = /** @class */ (function () {\n    function MaxKey() {\n        if (!(this instanceof MaxKey))\n            return new MaxKey();\n    }\n    /** @internal */\n    MaxKey.prototype.toExtendedJSON = function () {\n        return { $maxKey: 1 };\n    };\n    /** @internal */\n    MaxKey.fromExtendedJSON = function () {\n        return new MaxKey();\n    };\n    /** @internal */\n    MaxKey.prototype[Symbol.for('nodejs.util.inspect.custom')] = function () {\n        return this.inspect();\n    };\n    MaxKey.prototype.inspect = function () {\n        return 'new <PERSON>K<PERSON>()';\n    };\n    return MaxKey;\n}());\nexports.MaxKey = MaxKey;\nObject.defineProperty(MaxKey.prototype, '_bsontype', { value: 'MaxKey' });\n//# sourceMappingURL=max_key.js.map", "\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.MinKey = void 0;\n/**\n * A class representation of the BSON MinKey type.\n * @public\n * @category BSONType\n */\nvar MinKey = /** @class */ (function () {\n    function MinKey() {\n        if (!(this instanceof MinKey))\n            return new MinKey();\n    }\n    /** @internal */\n    MinKey.prototype.toExtendedJSON = function () {\n        return { $minKey: 1 };\n    };\n    /** @internal */\n    MinKey.fromExtendedJSON = function () {\n        return new MinKey();\n    };\n    /** @internal */\n    MinKey.prototype[Symbol.for('nodejs.util.inspect.custom')] = function () {\n        return this.inspect();\n    };\n    MinKey.prototype.inspect = function () {\n        return 'new <PERSON>Key()';\n    };\n    return MinKey;\n}());\nexports.MinKey = MinKey;\nObject.defineProperty(MinKey.prototype, '_bsontype', { value: 'MinKey' });\n//# sourceMappingURL=min_key.js.map", "\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.ObjectId = void 0;\nvar buffer_1 = require(\"buffer\");\nvar ensure_buffer_1 = require(\"./ensure_buffer\");\nvar error_1 = require(\"./error\");\nvar utils_1 = require(\"./parser/utils\");\n// Regular expression that checks for hex value\nvar checkForHexRegExp = new RegExp('^[0-9a-fA-F]{24}$');\n// Unique sequence for the current process (initialized on first use)\nvar PROCESS_UNIQUE = null;\nvar kId = Symbol('id');\n/**\n * A class representation of the BSON ObjectId type.\n * @public\n * @category BSONType\n */\nvar ObjectId = /** @class */ (function () {\n    /**\n     * Create an ObjectId type\n     *\n     * @param inputId - Can be a 24 character hex string, 12 byte binary Buffer, or a number.\n     */\n    function ObjectId(inputId) {\n        if (!(this instanceof ObjectId))\n            return new ObjectId(inputId);\n        // workingId is set based on type of input and whether valid id exists for the input\n        var workingId;\n        if (typeof inputId === 'object' && inputId && 'id' in inputId) {\n            if (typeof inputId.id !== 'string' && !ArrayBuffer.isView(inputId.id)) {\n                throw new error_1.BSONTypeError('Argument passed in must have an id that is of type string or Buffer');\n            }\n            if ('toHexString' in inputId && typeof inputId.toHexString === 'function') {\n                workingId = buffer_1.Buffer.from(inputId.toHexString(), 'hex');\n            }\n            else {\n                workingId = inputId.id;\n            }\n        }\n        else {\n            workingId = inputId;\n        }\n        // the following cases use workingId to construct an ObjectId\n        if (workingId == null || typeof workingId === 'number') {\n            // The most common use case (blank id, new objectId instance)\n            // Generate a new id\n            this[kId] = ObjectId.generate(typeof workingId === 'number' ? workingId : undefined);\n        }\n        else if (ArrayBuffer.isView(workingId) && workingId.byteLength === 12) {\n            // If intstanceof matches we can escape calling ensure buffer in Node.js environments\n            this[kId] = workingId instanceof buffer_1.Buffer ? workingId : (0, ensure_buffer_1.ensureBuffer)(workingId);\n        }\n        else if (typeof workingId === 'string') {\n            if (workingId.length === 12) {\n                var bytes = buffer_1.Buffer.from(workingId);\n                if (bytes.byteLength === 12) {\n                    this[kId] = bytes;\n                }\n                else {\n                    throw new error_1.BSONTypeError('Argument passed in must be a string of 12 bytes');\n                }\n            }\n            else if (workingId.length === 24 && checkForHexRegExp.test(workingId)) {\n                this[kId] = buffer_1.Buffer.from(workingId, 'hex');\n            }\n            else {\n                throw new error_1.BSONTypeError('Argument passed in must be a string of 12 bytes or a string of 24 hex characters or an integer');\n            }\n        }\n        else {\n            throw new error_1.BSONTypeError('Argument passed in does not match the accepted types');\n        }\n        // If we are caching the hex string\n        if (ObjectId.cacheHexString) {\n            this.__id = this.id.toString('hex');\n        }\n    }\n    Object.defineProperty(ObjectId.prototype, \"id\", {\n        /**\n         * The ObjectId bytes\n         * @readonly\n         */\n        get: function () {\n            return this[kId];\n        },\n        set: function (value) {\n            this[kId] = value;\n            if (ObjectId.cacheHexString) {\n                this.__id = value.toString('hex');\n            }\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(ObjectId.prototype, \"generationTime\", {\n        /**\n         * The generation time of this ObjectId instance\n         * @deprecated Please use getTimestamp / createFromTime which returns an int32 epoch\n         */\n        get: function () {\n            return this.id.readInt32BE(0);\n        },\n        set: function (value) {\n            // Encode time into first 4 bytes\n            this.id.writeUInt32BE(value, 0);\n        },\n        enumerable: false,\n        configurable: true\n    });\n    /** Returns the ObjectId id as a 24 character hex string representation */\n    ObjectId.prototype.toHexString = function () {\n        if (ObjectId.cacheHexString && this.__id) {\n            return this.__id;\n        }\n        var hexString = this.id.toString('hex');\n        if (ObjectId.cacheHexString && !this.__id) {\n            this.__id = hexString;\n        }\n        return hexString;\n    };\n    /**\n     * Update the ObjectId index\n     * @privateRemarks\n     * Used in generating new ObjectId's on the driver\n     * @internal\n     */\n    ObjectId.getInc = function () {\n        return (ObjectId.index = (ObjectId.index + 1) % 0xffffff);\n    };\n    /**\n     * Generate a 12 byte id buffer used in ObjectId's\n     *\n     * @param time - pass in a second based timestamp.\n     */\n    ObjectId.generate = function (time) {\n        if ('number' !== typeof time) {\n            time = Math.floor(Date.now() / 1000);\n        }\n        var inc = ObjectId.getInc();\n        var buffer = buffer_1.Buffer.alloc(12);\n        // 4-byte timestamp\n        buffer.writeUInt32BE(time, 0);\n        // set PROCESS_UNIQUE if yet not initialized\n        if (PROCESS_UNIQUE === null) {\n            PROCESS_UNIQUE = (0, utils_1.randomBytes)(5);\n        }\n        // 5-byte process unique\n        buffer[4] = PROCESS_UNIQUE[0];\n        buffer[5] = PROCESS_UNIQUE[1];\n        buffer[6] = PROCESS_UNIQUE[2];\n        buffer[7] = PROCESS_UNIQUE[3];\n        buffer[8] = PROCESS_UNIQUE[4];\n        // 3-byte counter\n        buffer[11] = inc & 0xff;\n        buffer[10] = (inc >> 8) & 0xff;\n        buffer[9] = (inc >> 16) & 0xff;\n        return buffer;\n    };\n    /**\n     * Converts the id into a 24 character hex string for printing\n     *\n     * @param format - The Buffer toString format parameter.\n     */\n    ObjectId.prototype.toString = function (format) {\n        // Is the id a buffer then use the buffer toString method to return the format\n        if (format)\n            return this.id.toString(format);\n        return this.toHexString();\n    };\n    /** Converts to its JSON the 24 character hex string representation. */\n    ObjectId.prototype.toJSON = function () {\n        return this.toHexString();\n    };\n    /**\n     * Compares the equality of this ObjectId with `otherID`.\n     *\n     * @param otherId - ObjectId instance to compare against.\n     */\n    ObjectId.prototype.equals = function (otherId) {\n        if (otherId === undefined || otherId === null) {\n            return false;\n        }\n        if (otherId instanceof ObjectId) {\n            return this[kId][11] === otherId[kId][11] && this[kId].equals(otherId[kId]);\n        }\n        if (typeof otherId === 'string' &&\n            ObjectId.isValid(otherId) &&\n            otherId.length === 12 &&\n            (0, utils_1.isUint8Array)(this.id)) {\n            return otherId === buffer_1.Buffer.prototype.toString.call(this.id, 'latin1');\n        }\n        if (typeof otherId === 'string' && ObjectId.isValid(otherId) && otherId.length === 24) {\n            return otherId.toLowerCase() === this.toHexString();\n        }\n        if (typeof otherId === 'string' && ObjectId.isValid(otherId) && otherId.length === 12) {\n            return buffer_1.Buffer.from(otherId).equals(this.id);\n        }\n        if (typeof otherId === 'object' &&\n            'toHexString' in otherId &&\n            typeof otherId.toHexString === 'function') {\n            var otherIdString = otherId.toHexString();\n            var thisIdString = this.toHexString().toLowerCase();\n            return typeof otherIdString === 'string' && otherIdString.toLowerCase() === thisIdString;\n        }\n        return false;\n    };\n    /** Returns the generation date (accurate up to the second) that this ID was generated. */\n    ObjectId.prototype.getTimestamp = function () {\n        var timestamp = new Date();\n        var time = this.id.readUInt32BE(0);\n        timestamp.setTime(Math.floor(time) * 1000);\n        return timestamp;\n    };\n    /** @internal */\n    ObjectId.createPk = function () {\n        return new ObjectId();\n    };\n    /**\n     * Creates an ObjectId from a second based number, with the rest of the ObjectId zeroed out. Used for comparisons or sorting the ObjectId.\n     *\n     * @param time - an integer number representing a number of seconds.\n     */\n    ObjectId.createFromTime = function (time) {\n        var buffer = buffer_1.Buffer.from([0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]);\n        // Encode time into first 4 bytes\n        buffer.writeUInt32BE(time, 0);\n        // Return the new objectId\n        return new ObjectId(buffer);\n    };\n    /**\n     * Creates an ObjectId from a hex string representation of an ObjectId.\n     *\n     * @param hexString - create a ObjectId from a passed in 24 character hexstring.\n     */\n    ObjectId.createFromHexString = function (hexString) {\n        // Throw an error if it's not a valid setup\n        if (typeof hexString === 'undefined' || (hexString != null && hexString.length !== 24)) {\n            throw new error_1.BSONTypeError('Argument passed in must be a single String of 12 bytes or a string of 24 hex characters');\n        }\n        return new ObjectId(buffer_1.Buffer.from(hexString, 'hex'));\n    };\n    /**\n     * Checks if a value is a valid bson ObjectId\n     *\n     * @param id - ObjectId instance to validate.\n     */\n    ObjectId.isValid = function (id) {\n        if (id == null)\n            return false;\n        try {\n            new ObjectId(id);\n            return true;\n        }\n        catch (_a) {\n            return false;\n        }\n    };\n    /** @internal */\n    ObjectId.prototype.toExtendedJSON = function () {\n        if (this.toHexString)\n            return { $oid: this.toHexString() };\n        return { $oid: this.toString('hex') };\n    };\n    /** @internal */\n    ObjectId.fromExtendedJSON = function (doc) {\n        return new ObjectId(doc.$oid);\n    };\n    /**\n     * Converts to a string representation of this Id.\n     *\n     * @returns return the 24 character hex string representation.\n     * @internal\n     */\n    ObjectId.prototype[Symbol.for('nodejs.util.inspect.custom')] = function () {\n        return this.inspect();\n    };\n    ObjectId.prototype.inspect = function () {\n        return \"new ObjectId(\\\"\".concat(this.toHexString(), \"\\\")\");\n    };\n    /** @internal */\n    ObjectId.index = Math.floor(Math.random() * 0xffffff);\n    return ObjectId;\n}());\nexports.ObjectId = ObjectId;\n// Deprecated methods\nObject.defineProperty(ObjectId.prototype, 'generate', {\n    value: (0, utils_1.deprecate)(function (time) { return ObjectId.generate(time); }, 'Please use the static `ObjectId.generate(time)` instead')\n});\nObject.defineProperty(ObjectId.prototype, 'getInc', {\n    value: (0, utils_1.deprecate)(function () { return ObjectId.getInc(); }, 'Please use the static `ObjectId.getInc()` instead')\n});\nObject.defineProperty(ObjectId.prototype, 'get_inc', {\n    value: (0, utils_1.deprecate)(function () { return ObjectId.getInc(); }, 'Please use the static `ObjectId.getInc()` instead')\n});\nObject.defineProperty(ObjectId, 'get_inc', {\n    value: (0, utils_1.deprecate)(function () { return ObjectId.getInc(); }, 'Please use the static `ObjectId.getInc()` instead')\n});\nObject.defineProperty(ObjectId.prototype, '_bsontype', { value: 'ObjectID' });\n//# sourceMappingURL=objectid.js.map", "\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.BSONRegExp = void 0;\nvar error_1 = require(\"./error\");\nfunction alphabetize(str) {\n    return str.split('').sort().join('');\n}\n/**\n * A class representation of the BSON RegExp type.\n * @public\n * @category BSONType\n */\nvar BSONRegExp = /** @class */ (function () {\n    /**\n     * @param pattern - The regular expression pattern to match\n     * @param options - The regular expression options\n     */\n    function BSONRegExp(pattern, options) {\n        if (!(this instanceof BSONRegExp))\n            return new BSONRegExp(pattern, options);\n        this.pattern = pattern;\n        this.options = alphabetize(options !== null && options !== void 0 ? options : '');\n        if (this.pattern.indexOf('\\x00') !== -1) {\n            throw new error_1.BSONError(\"BSON Regex patterns cannot contain null bytes, found: \".concat(JSON.stringify(this.pattern)));\n        }\n        if (this.options.indexOf('\\x00') !== -1) {\n            throw new error_1.BSONError(\"BSON Regex options cannot contain null bytes, found: \".concat(JSON.stringify(this.options)));\n        }\n        // Validate options\n        for (var i = 0; i < this.options.length; i++) {\n            if (!(this.options[i] === 'i' ||\n                this.options[i] === 'm' ||\n                this.options[i] === 'x' ||\n                this.options[i] === 'l' ||\n                this.options[i] === 's' ||\n                this.options[i] === 'u')) {\n                throw new error_1.BSONError(\"The regular expression option [\".concat(this.options[i], \"] is not supported\"));\n            }\n        }\n    }\n    BSONRegExp.parseOptions = function (options) {\n        return options ? options.split('').sort().join('') : '';\n    };\n    /** @internal */\n    BSONRegExp.prototype.toExtendedJSON = function (options) {\n        options = options || {};\n        if (options.legacy) {\n            return { $regex: this.pattern, $options: this.options };\n        }\n        return { $regularExpression: { pattern: this.pattern, options: this.options } };\n    };\n    /** @internal */\n    BSONRegExp.fromExtendedJSON = function (doc) {\n        if ('$regex' in doc) {\n            if (typeof doc.$regex !== 'string') {\n                // This is for $regex query operators that have extended json values.\n                if (doc.$regex._bsontype === 'BSONRegExp') {\n                    return doc;\n                }\n            }\n            else {\n                return new BSONRegExp(doc.$regex, BSONRegExp.parseOptions(doc.$options));\n            }\n        }\n        if ('$regularExpression' in doc) {\n            return new BSONRegExp(doc.$regularExpression.pattern, BSONRegExp.parseOptions(doc.$regularExpression.options));\n        }\n        throw new error_1.BSONTypeError(\"Unexpected BSONRegExp EJSON object form: \".concat(JSON.stringify(doc)));\n    };\n    return BSONRegExp;\n}());\nexports.BSONRegExp = BSONRegExp;\nObject.defineProperty(BSONRegExp.prototype, '_bsontype', { value: 'BSONRegExp' });\n//# sourceMappingURL=regexp.js.map", "\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.BSONSymbol = void 0;\n/**\n * A class representation of the BSON Symbol type.\n * @public\n * @category BSONType\n */\nvar BSONSymbol = /** @class */ (function () {\n    /**\n     * @param value - the string representing the symbol.\n     */\n    function BSONSymbol(value) {\n        if (!(this instanceof BSONSymbol))\n            return new BSONSymbol(value);\n        this.value = value;\n    }\n    /** Access the wrapped string value. */\n    BSONSymbol.prototype.valueOf = function () {\n        return this.value;\n    };\n    BSONSymbol.prototype.toString = function () {\n        return this.value;\n    };\n    /** @internal */\n    BSONSymbol.prototype.inspect = function () {\n        return \"new BSONSymbol(\\\"\".concat(this.value, \"\\\")\");\n    };\n    BSONSymbol.prototype.toJSON = function () {\n        return this.value;\n    };\n    /** @internal */\n    BSONSymbol.prototype.toExtendedJSON = function () {\n        return { $symbol: this.value };\n    };\n    /** @internal */\n    BSONSymbol.fromExtendedJSON = function (doc) {\n        return new BSONSymbol(doc.$symbol);\n    };\n    /** @internal */\n    BSONSymbol.prototype[Symbol.for('nodejs.util.inspect.custom')] = function () {\n        return this.inspect();\n    };\n    return BSONSymbol;\n}());\nexports.BSONSymbol = BSONSymbol;\nObject.defineProperty(BSONSymbol.prototype, '_bsontype', { value: 'Symbol' });\n//# sourceMappingURL=symbol.js.map", "\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.Timestamp = exports.LongWithoutOverridesClass = void 0;\nvar long_1 = require(\"./long\");\nvar utils_1 = require(\"./parser/utils\");\n/** @public */\nexports.LongWithoutOverridesClass = long_1.Long;\n/**\n * @public\n * @category BSONType\n * */\nvar Timestamp = /** @class */ (function (_super) {\n    __extends(Timestamp, _super);\n    function Timestamp(low, high) {\n        var _this = this;\n        // eslint-disable-next-line @typescript-eslint/ban-ts-comment\n        // @ts-expect-error\n        if (!(_this instanceof Timestamp))\n            return new Timestamp(low, high);\n        if (long_1.Long.isLong(low)) {\n            _this = _super.call(this, low.low, low.high, true) || this;\n        }\n        else if ((0, utils_1.isObjectLike)(low) && typeof low.t !== 'undefined' && typeof low.i !== 'undefined') {\n            _this = _super.call(this, low.i, low.t, true) || this;\n        }\n        else {\n            _this = _super.call(this, low, high, true) || this;\n        }\n        Object.defineProperty(_this, '_bsontype', {\n            value: 'Timestamp',\n            writable: false,\n            configurable: false,\n            enumerable: false\n        });\n        return _this;\n    }\n    Timestamp.prototype.toJSON = function () {\n        return {\n            $timestamp: this.toString()\n        };\n    };\n    /** Returns a Timestamp represented by the given (32-bit) integer value. */\n    Timestamp.fromInt = function (value) {\n        return new Timestamp(long_1.Long.fromInt(value, true));\n    };\n    /** Returns a Timestamp representing the given number value, provided that it is a finite number. Otherwise, zero is returned. */\n    Timestamp.fromNumber = function (value) {\n        return new Timestamp(long_1.Long.fromNumber(value, true));\n    };\n    /**\n     * Returns a Timestamp for the given high and low bits. Each is assumed to use 32 bits.\n     *\n     * @param lowBits - the low 32-bits.\n     * @param highBits - the high 32-bits.\n     */\n    Timestamp.fromBits = function (lowBits, highBits) {\n        return new Timestamp(lowBits, highBits);\n    };\n    /**\n     * Returns a Timestamp from the given string, optionally using the given radix.\n     *\n     * @param str - the textual representation of the Timestamp.\n     * @param optRadix - the radix in which the text is written.\n     */\n    Timestamp.fromString = function (str, optRadix) {\n        return new Timestamp(long_1.Long.fromString(str, true, optRadix));\n    };\n    /** @internal */\n    Timestamp.prototype.toExtendedJSON = function () {\n        return { $timestamp: { t: this.high >>> 0, i: this.low >>> 0 } };\n    };\n    /** @internal */\n    Timestamp.fromExtendedJSON = function (doc) {\n        return new Timestamp(doc.$timestamp);\n    };\n    /** @internal */\n    Timestamp.prototype[Symbol.for('nodejs.util.inspect.custom')] = function () {\n        return this.inspect();\n    };\n    Timestamp.prototype.inspect = function () {\n        return \"new Timestamp({ t: \".concat(this.getHighBits(), \", i: \").concat(this.getLowBits(), \" })\");\n    };\n    Timestamp.MAX_VALUE = long_1.Long.MAX_UNSIGNED_VALUE;\n    return Timestamp;\n}(exports.LongWithoutOverridesClass));\nexports.Timestamp = Timestamp;\n//# sourceMappingURL=timestamp.js.map", "\n/* eslint-disable @typescript-eslint/no-explicit-any */\n// We have an ES6 Map available, return the native instance\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.Map = void 0;\nvar global_1 = require(\"./utils/global\");\n/** @public */\nvar bsonMap;\nexports.Map = bsonMap;\nvar bsonGlobal = (0, global_1.getGlobal)();\nif (bsonGlobal.Map) {\n    exports.Map = bsonMap = bsonGlobal.Map;\n}\nelse {\n    // We will return a polyfill\n    exports.Map = bsonMap = /** @class */ (function () {\n        function Map(array) {\n            if (array === void 0) { array = []; }\n            this._keys = [];\n            this._values = {};\n            for (var i = 0; i < array.length; i++) {\n                if (array[i] == null)\n                    continue; // skip null and undefined\n                var entry = array[i];\n                var key = entry[0];\n                var value = entry[1];\n                // Add the key to the list of keys in order\n                this._keys.push(key);\n                // Add the key and value to the values dictionary with a point\n                // to the location in the ordered keys list\n                this._values[key] = { v: value, i: this._keys.length - 1 };\n            }\n        }\n        Map.prototype.clear = function () {\n            this._keys = [];\n            this._values = {};\n        };\n        Map.prototype.delete = function (key) {\n            var value = this._values[key];\n            if (value == null)\n                return false;\n            // Delete entry\n            delete this._values[key];\n            // Remove the key from the ordered keys list\n            this._keys.splice(value.i, 1);\n            return true;\n        };\n        Map.prototype.entries = function () {\n            var _this = this;\n            var index = 0;\n            return {\n                next: function () {\n                    var key = _this._keys[index++];\n                    return {\n                        value: key !== undefined ? [key, _this._values[key].v] : undefined,\n                        done: key !== undefined ? false : true\n                    };\n                }\n            };\n        };\n        Map.prototype.forEach = function (callback, self) {\n            self = self || this;\n            for (var i = 0; i < this._keys.length; i++) {\n                var key = this._keys[i];\n                // Call the forEach callback\n                callback.call(self, this._values[key].v, key, self);\n            }\n        };\n        Map.prototype.get = function (key) {\n            return this._values[key] ? this._values[key].v : undefined;\n        };\n        Map.prototype.has = function (key) {\n            return this._values[key] != null;\n        };\n        Map.prototype.keys = function () {\n            var _this = this;\n            var index = 0;\n            return {\n                next: function () {\n                    var key = _this._keys[index++];\n                    return {\n                        value: key !== undefined ? key : undefined,\n                        done: key !== undefined ? false : true\n                    };\n                }\n            };\n        };\n        Map.prototype.set = function (key, value) {\n            if (this._values[key]) {\n                this._values[key].v = value;\n                return this;\n            }\n            // Add the key to the list of keys in order\n            this._keys.push(key);\n            // Add the key and value to the values dictionary with a point\n            // to the location in the ordered keys list\n            this._values[key] = { v: value, i: this._keys.length - 1 };\n            return this;\n        };\n        Map.prototype.values = function () {\n            var _this = this;\n            var index = 0;\n            return {\n                next: function () {\n                    var key = _this._keys[index++];\n                    return {\n                        value: key !== undefined ? _this._values[key].v : undefined,\n                        done: key !== undefined ? false : true\n                    };\n                }\n            };\n        };\n        Object.defineProperty(Map.prototype, \"size\", {\n            get: function () {\n                return this._keys.length;\n            },\n            enumerable: false,\n            configurable: true\n        });\n        return Map;\n    }());\n}\n//# sourceMappingURL=map.js.map", "\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.calculateObjectSize = void 0;\nvar buffer_1 = require(\"buffer\");\nvar binary_1 = require(\"../binary\");\nvar constants = require(\"../constants\");\nvar utils_1 = require(\"./utils\");\nfunction calculateObjectSize(object, serializeFunctions, ignoreUndefined) {\n    var totalLength = 4 + 1;\n    if (Array.isArray(object)) {\n        for (var i = 0; i < object.length; i++) {\n            totalLength += calculateElement(i.toString(), object[i], serializeFunctions, true, ignoreUndefined);\n        }\n    }\n    else {\n        // If we have toBSON defined, override the current object\n        if (typeof (object === null || object === void 0 ? void 0 : object.toBSON) === 'function') {\n            object = object.toBSON();\n        }\n        // Calculate size\n        for (var key in object) {\n            totalLength += calculateElement(key, object[key], serializeFunctions, false, ignoreUndefined);\n        }\n    }\n    return totalLength;\n}\nexports.calculateObjectSize = calculateObjectSize;\n/** @internal */\nfunction calculateElement(name, \n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nvalue, serializeFunctions, isArray, ignoreUndefined) {\n    if (serializeFunctions === void 0) { serializeFunctions = false; }\n    if (isArray === void 0) { isArray = false; }\n    if (ignoreUndefined === void 0) { ignoreUndefined = false; }\n    // If we have toBSON defined, override the current object\n    if (typeof (value === null || value === void 0 ? void 0 : value.toBSON) === 'function') {\n        value = value.toBSON();\n    }\n    switch (typeof value) {\n        case 'string':\n            return 1 + buffer_1.Buffer.byteLength(name, 'utf8') + 1 + 4 + buffer_1.Buffer.byteLength(value, 'utf8') + 1;\n        case 'number':\n            if (Math.floor(value) === value &&\n                value >= constants.JS_INT_MIN &&\n                value <= constants.JS_INT_MAX) {\n                if (value >= constants.BSON_INT32_MIN && value <= constants.BSON_INT32_MAX) {\n                    // 32 bit\n                    return (name != null ? buffer_1.Buffer.byteLength(name, 'utf8') + 1 : 0) + (4 + 1);\n                }\n                else {\n                    return (name != null ? buffer_1.Buffer.byteLength(name, 'utf8') + 1 : 0) + (8 + 1);\n                }\n            }\n            else {\n                // 64 bit\n                return (name != null ? buffer_1.Buffer.byteLength(name, 'utf8') + 1 : 0) + (8 + 1);\n            }\n        case 'undefined':\n            if (isArray || !ignoreUndefined)\n                return (name != null ? buffer_1.Buffer.byteLength(name, 'utf8') + 1 : 0) + 1;\n            return 0;\n        case 'boolean':\n            return (name != null ? buffer_1.Buffer.byteLength(name, 'utf8') + 1 : 0) + (1 + 1);\n        case 'object':\n            if (value == null || value['_bsontype'] === 'MinKey' || value['_bsontype'] === 'MaxKey') {\n                return (name != null ? buffer_1.Buffer.byteLength(name, 'utf8') + 1 : 0) + 1;\n            }\n            else if (value['_bsontype'] === 'ObjectId' || value['_bsontype'] === 'ObjectID') {\n                return (name != null ? buffer_1.Buffer.byteLength(name, 'utf8') + 1 : 0) + (12 + 1);\n            }\n            else if (value instanceof Date || (0, utils_1.isDate)(value)) {\n                return (name != null ? buffer_1.Buffer.byteLength(name, 'utf8') + 1 : 0) + (8 + 1);\n            }\n            else if (ArrayBuffer.isView(value) ||\n                value instanceof ArrayBuffer ||\n                (0, utils_1.isAnyArrayBuffer)(value)) {\n                return ((name != null ? buffer_1.Buffer.byteLength(name, 'utf8') + 1 : 0) + (1 + 4 + 1) + value.byteLength);\n            }\n            else if (value['_bsontype'] === 'Long' ||\n                value['_bsontype'] === 'Double' ||\n                value['_bsontype'] === 'Timestamp') {\n                return (name != null ? buffer_1.Buffer.byteLength(name, 'utf8') + 1 : 0) + (8 + 1);\n            }\n            else if (value['_bsontype'] === 'Decimal128') {\n                return (name != null ? buffer_1.Buffer.byteLength(name, 'utf8') + 1 : 0) + (16 + 1);\n            }\n            else if (value['_bsontype'] === 'Code') {\n                // Calculate size depending on the availability of a scope\n                if (value.scope != null && Object.keys(value.scope).length > 0) {\n                    return ((name != null ? buffer_1.Buffer.byteLength(name, 'utf8') + 1 : 0) +\n                        1 +\n                        4 +\n                        4 +\n                        buffer_1.Buffer.byteLength(value.code.toString(), 'utf8') +\n                        1 +\n                        calculateObjectSize(value.scope, serializeFunctions, ignoreUndefined));\n                }\n                else {\n                    return ((name != null ? buffer_1.Buffer.byteLength(name, 'utf8') + 1 : 0) +\n                        1 +\n                        4 +\n                        buffer_1.Buffer.byteLength(value.code.toString(), 'utf8') +\n                        1);\n                }\n            }\n            else if (value['_bsontype'] === 'Binary') {\n                var binary = value;\n                // Check what kind of subtype we have\n                if (binary.sub_type === binary_1.Binary.SUBTYPE_BYTE_ARRAY) {\n                    return ((name != null ? buffer_1.Buffer.byteLength(name, 'utf8') + 1 : 0) +\n                        (binary.position + 1 + 4 + 1 + 4));\n                }\n                else {\n                    return ((name != null ? buffer_1.Buffer.byteLength(name, 'utf8') + 1 : 0) + (binary.position + 1 + 4 + 1));\n                }\n            }\n            else if (value['_bsontype'] === 'Symbol') {\n                return ((name != null ? buffer_1.Buffer.byteLength(name, 'utf8') + 1 : 0) +\n                    buffer_1.Buffer.byteLength(value.value, 'utf8') +\n                    4 +\n                    1 +\n                    1);\n            }\n            else if (value['_bsontype'] === 'DBRef') {\n                // Set up correct object for serialization\n                var ordered_values = Object.assign({\n                    $ref: value.collection,\n                    $id: value.oid\n                }, value.fields);\n                // Add db reference if it exists\n                if (value.db != null) {\n                    ordered_values['$db'] = value.db;\n                }\n                return ((name != null ? buffer_1.Buffer.byteLength(name, 'utf8') + 1 : 0) +\n                    1 +\n                    calculateObjectSize(ordered_values, serializeFunctions, ignoreUndefined));\n            }\n            else if (value instanceof RegExp || (0, utils_1.isRegExp)(value)) {\n                return ((name != null ? buffer_1.Buffer.byteLength(name, 'utf8') + 1 : 0) +\n                    1 +\n                    buffer_1.Buffer.byteLength(value.source, 'utf8') +\n                    1 +\n                    (value.global ? 1 : 0) +\n                    (value.ignoreCase ? 1 : 0) +\n                    (value.multiline ? 1 : 0) +\n                    1);\n            }\n            else if (value['_bsontype'] === 'BSONRegExp') {\n                return ((name != null ? buffer_1.Buffer.byteLength(name, 'utf8') + 1 : 0) +\n                    1 +\n                    buffer_1.Buffer.byteLength(value.pattern, 'utf8') +\n                    1 +\n                    buffer_1.Buffer.byteLength(value.options, 'utf8') +\n                    1);\n            }\n            else {\n                return ((name != null ? buffer_1.Buffer.byteLength(name, 'utf8') + 1 : 0) +\n                    calculateObjectSize(value, serializeFunctions, ignoreUndefined) +\n                    1);\n            }\n        case 'function':\n            // WTF for 0.4.X where typeof /someregexp/ === 'function'\n            if (value instanceof RegExp || (0, utils_1.isRegExp)(value) || String.call(value) === '[object RegExp]') {\n                return ((name != null ? buffer_1.Buffer.byteLength(name, 'utf8') + 1 : 0) +\n                    1 +\n                    buffer_1.Buffer.byteLength(value.source, 'utf8') +\n                    1 +\n                    (value.global ? 1 : 0) +\n                    (value.ignoreCase ? 1 : 0) +\n                    (value.multiline ? 1 : 0) +\n                    1);\n            }\n            else {\n                if (serializeFunctions && value.scope != null && Object.keys(value.scope).length > 0) {\n                    return ((name != null ? buffer_1.Buffer.byteLength(name, 'utf8') + 1 : 0) +\n                        1 +\n                        4 +\n                        4 +\n                        buffer_1.Buffer.byteLength((0, utils_1.normalizedFunctionString)(value), 'utf8') +\n                        1 +\n                        calculateObjectSize(value.scope, serializeFunctions, ignoreUndefined));\n                }\n                else if (serializeFunctions) {\n                    return ((name != null ? buffer_1.Buffer.byteLength(name, 'utf8') + 1 : 0) +\n                        1 +\n                        4 +\n                        buffer_1.Buffer.byteLength((0, utils_1.normalizedFunctionString)(value), 'utf8') +\n                        1);\n                }\n            }\n    }\n    return 0;\n}\n//# sourceMappingURL=calculate_size.js.map", "\nvar __assign = (this && this.__assign) || function () {\n    __assign = Object.assign || function(t) {\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\n            s = arguments[i];\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))\n                t[p] = s[p];\n        }\n        return t;\n    };\n    return __assign.apply(this, arguments);\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.deserialize = void 0;\nvar buffer_1 = require(\"buffer\");\nvar binary_1 = require(\"../binary\");\nvar code_1 = require(\"../code\");\nvar constants = require(\"../constants\");\nvar db_ref_1 = require(\"../db_ref\");\nvar decimal128_1 = require(\"../decimal128\");\nvar double_1 = require(\"../double\");\nvar error_1 = require(\"../error\");\nvar int_32_1 = require(\"../int_32\");\nvar long_1 = require(\"../long\");\nvar max_key_1 = require(\"../max_key\");\nvar min_key_1 = require(\"../min_key\");\nvar objectid_1 = require(\"../objectid\");\nvar regexp_1 = require(\"../regexp\");\nvar symbol_1 = require(\"../symbol\");\nvar timestamp_1 = require(\"../timestamp\");\nvar validate_utf8_1 = require(\"../validate_utf8\");\n// Internal long versions\nvar JS_INT_MAX_LONG = long_1.Long.fromNumber(constants.JS_INT_MAX);\nvar JS_INT_MIN_LONG = long_1.Long.fromNumber(constants.JS_INT_MIN);\nvar functionCache = {};\nfunction deserialize(buffer, options, isArray) {\n    options = options == null ? {} : options;\n    var index = options && options.index ? options.index : 0;\n    // Read the document size\n    var size = buffer[index] |\n        (buffer[index + 1] << 8) |\n        (buffer[index + 2] << 16) |\n        (buffer[index + 3] << 24);\n    if (size < 5) {\n        throw new error_1.BSONError(\"bson size must be >= 5, is \".concat(size));\n    }\n    if (options.allowObjectSmallerThanBufferSize && buffer.length < size) {\n        throw new error_1.BSONError(\"buffer length \".concat(buffer.length, \" must be >= bson size \").concat(size));\n    }\n    if (!options.allowObjectSmallerThanBufferSize && buffer.length !== size) {\n        throw new error_1.BSONError(\"buffer length \".concat(buffer.length, \" must === bson size \").concat(size));\n    }\n    if (size + index > buffer.byteLength) {\n        throw new error_1.BSONError(\"(bson size \".concat(size, \" + options.index \").concat(index, \" must be <= buffer length \").concat(buffer.byteLength, \")\"));\n    }\n    // Illegal end value\n    if (buffer[index + size - 1] !== 0) {\n        throw new error_1.BSONError(\"One object, sized correctly, with a spot for an EOO, but the EOO isn't 0x00\");\n    }\n    // Start deserializtion\n    return deserializeObject(buffer, index, options, isArray);\n}\nexports.deserialize = deserialize;\nvar allowedDBRefKeys = /^\\$ref$|^\\$id$|^\\$db$/;\nfunction deserializeObject(buffer, index, options, isArray) {\n    if (isArray === void 0) { isArray = false; }\n    var evalFunctions = options['evalFunctions'] == null ? false : options['evalFunctions'];\n    var cacheFunctions = options['cacheFunctions'] == null ? false : options['cacheFunctions'];\n    var fieldsAsRaw = options['fieldsAsRaw'] == null ? null : options['fieldsAsRaw'];\n    // Return raw bson buffer instead of parsing it\n    var raw = options['raw'] == null ? false : options['raw'];\n    // Return BSONRegExp objects instead of native regular expressions\n    var bsonRegExp = typeof options['bsonRegExp'] === 'boolean' ? options['bsonRegExp'] : false;\n    // Controls the promotion of values vs wrapper classes\n    var promoteBuffers = options['promoteBuffers'] == null ? false : options['promoteBuffers'];\n    var promoteLongs = options['promoteLongs'] == null ? true : options['promoteLongs'];\n    var promoteValues = options['promoteValues'] == null ? true : options['promoteValues'];\n    // Ensures default validation option if none given\n    var validation = options.validation == null ? { utf8: true } : options.validation;\n    // Shows if global utf-8 validation is enabled or disabled\n    var globalUTFValidation = true;\n    // Reflects utf-8 validation setting regardless of global or specific key validation\n    var validationSetting;\n    // Set of keys either to enable or disable validation on\n    var utf8KeysSet = new Set();\n    // Check for boolean uniformity and empty validation option\n    var utf8ValidatedKeys = validation.utf8;\n    if (typeof utf8ValidatedKeys === 'boolean') {\n        validationSetting = utf8ValidatedKeys;\n    }\n    else {\n        globalUTFValidation = false;\n        var utf8ValidationValues = Object.keys(utf8ValidatedKeys).map(function (key) {\n            return utf8ValidatedKeys[key];\n        });\n        if (utf8ValidationValues.length === 0) {\n            throw new error_1.BSONError('UTF-8 validation setting cannot be empty');\n        }\n        if (typeof utf8ValidationValues[0] !== 'boolean') {\n            throw new error_1.BSONError('Invalid UTF-8 validation option, must specify boolean values');\n        }\n        validationSetting = utf8ValidationValues[0];\n        // Ensures boolean uniformity in utf-8 validation (all true or all false)\n        if (!utf8ValidationValues.every(function (item) { return item === validationSetting; })) {\n            throw new error_1.BSONError('Invalid UTF-8 validation option - keys must be all true or all false');\n        }\n    }\n    // Add keys to set that will either be validated or not based on validationSetting\n    if (!globalUTFValidation) {\n        for (var _i = 0, _a = Object.keys(utf8ValidatedKeys); _i < _a.length; _i++) {\n            var key = _a[_i];\n            utf8KeysSet.add(key);\n        }\n    }\n    // Set the start index\n    var startIndex = index;\n    // Validate that we have at least 4 bytes of buffer\n    if (buffer.length < 5)\n        throw new error_1.BSONError('corrupt bson message < 5 bytes long');\n    // Read the document size\n    var size = buffer[index++] | (buffer[index++] << 8) | (buffer[index++] << 16) | (buffer[index++] << 24);\n    // Ensure buffer is valid size\n    if (size < 5 || size > buffer.length)\n        throw new error_1.BSONError('corrupt bson message');\n    // Create holding object\n    var object = isArray ? [] : {};\n    // Used for arrays to skip having to perform utf8 decoding\n    var arrayIndex = 0;\n    var done = false;\n    var isPossibleDBRef = isArray ? false : null;\n    // While we have more left data left keep parsing\n    var dataview = new DataView(buffer.buffer, buffer.byteOffset, buffer.byteLength);\n    while (!done) {\n        // Read the type\n        var elementType = buffer[index++];\n        // If we get a zero it's the last byte, exit\n        if (elementType === 0)\n            break;\n        // Get the start search index\n        var i = index;\n        // Locate the end of the c string\n        while (buffer[i] !== 0x00 && i < buffer.length) {\n            i++;\n        }\n        // If are at the end of the buffer there is a problem with the document\n        if (i >= buffer.byteLength)\n            throw new error_1.BSONError('Bad BSON Document: illegal CString');\n        // Represents the key\n        var name = isArray ? arrayIndex++ : buffer.toString('utf8', index, i);\n        // shouldValidateKey is true if the key should be validated, false otherwise\n        var shouldValidateKey = true;\n        if (globalUTFValidation || utf8KeysSet.has(name)) {\n            shouldValidateKey = validationSetting;\n        }\n        else {\n            shouldValidateKey = !validationSetting;\n        }\n        if (isPossibleDBRef !== false && name[0] === '$') {\n            isPossibleDBRef = allowedDBRefKeys.test(name);\n        }\n        var value = void 0;\n        index = i + 1;\n        if (elementType === constants.BSON_DATA_STRING) {\n            var stringSize = buffer[index++] |\n                (buffer[index++] << 8) |\n                (buffer[index++] << 16) |\n                (buffer[index++] << 24);\n            if (stringSize <= 0 ||\n                stringSize > buffer.length - index ||\n                buffer[index + stringSize - 1] !== 0) {\n                throw new error_1.BSONError('bad string length in bson');\n            }\n            value = getValidatedString(buffer, index, index + stringSize - 1, shouldValidateKey);\n            index = index + stringSize;\n        }\n        else if (elementType === constants.BSON_DATA_OID) {\n            var oid = buffer_1.Buffer.alloc(12);\n            buffer.copy(oid, 0, index, index + 12);\n            value = new objectid_1.ObjectId(oid);\n            index = index + 12;\n        }\n        else if (elementType === constants.BSON_DATA_INT && promoteValues === false) {\n            value = new int_32_1.Int32(buffer[index++] | (buffer[index++] << 8) | (buffer[index++] << 16) | (buffer[index++] << 24));\n        }\n        else if (elementType === constants.BSON_DATA_INT) {\n            value =\n                buffer[index++] |\n                    (buffer[index++] << 8) |\n                    (buffer[index++] << 16) |\n                    (buffer[index++] << 24);\n        }\n        else if (elementType === constants.BSON_DATA_NUMBER && promoteValues === false) {\n            value = new double_1.Double(dataview.getFloat64(index, true));\n            index = index + 8;\n        }\n        else if (elementType === constants.BSON_DATA_NUMBER) {\n            value = dataview.getFloat64(index, true);\n            index = index + 8;\n        }\n        else if (elementType === constants.BSON_DATA_DATE) {\n            var lowBits = buffer[index++] |\n                (buffer[index++] << 8) |\n                (buffer[index++] << 16) |\n                (buffer[index++] << 24);\n            var highBits = buffer[index++] |\n                (buffer[index++] << 8) |\n                (buffer[index++] << 16) |\n                (buffer[index++] << 24);\n            value = new Date(new long_1.Long(lowBits, highBits).toNumber());\n        }\n        else if (elementType === constants.BSON_DATA_BOOLEAN) {\n            if (buffer[index] !== 0 && buffer[index] !== 1)\n                throw new error_1.BSONError('illegal boolean type value');\n            value = buffer[index++] === 1;\n        }\n        else if (elementType === constants.BSON_DATA_OBJECT) {\n            var _index = index;\n            var objectSize = buffer[index] |\n                (buffer[index + 1] << 8) |\n                (buffer[index + 2] << 16) |\n                (buffer[index + 3] << 24);\n            if (objectSize <= 0 || objectSize > buffer.length - index)\n                throw new error_1.BSONError('bad embedded document length in bson');\n            // We have a raw value\n            if (raw) {\n                value = buffer.slice(index, index + objectSize);\n            }\n            else {\n                var objectOptions = options;\n                if (!globalUTFValidation) {\n                    objectOptions = __assign(__assign({}, options), { validation: { utf8: shouldValidateKey } });\n                }\n                value = deserializeObject(buffer, _index, objectOptions, false);\n            }\n            index = index + objectSize;\n        }\n        else if (elementType === constants.BSON_DATA_ARRAY) {\n            var _index = index;\n            var objectSize = buffer[index] |\n                (buffer[index + 1] << 8) |\n                (buffer[index + 2] << 16) |\n                (buffer[index + 3] << 24);\n            var arrayOptions = options;\n            // Stop index\n            var stopIndex = index + objectSize;\n            // All elements of array to be returned as raw bson\n            if (fieldsAsRaw && fieldsAsRaw[name]) {\n                arrayOptions = {};\n                for (var n in options) {\n                    arrayOptions[n] = options[n];\n                }\n                arrayOptions['raw'] = true;\n            }\n            if (!globalUTFValidation) {\n                arrayOptions = __assign(__assign({}, arrayOptions), { validation: { utf8: shouldValidateKey } });\n            }\n            value = deserializeObject(buffer, _index, arrayOptions, true);\n            index = index + objectSize;\n            if (buffer[index - 1] !== 0)\n                throw new error_1.BSONError('invalid array terminator byte');\n            if (index !== stopIndex)\n                throw new error_1.BSONError('corrupted array bson');\n        }\n        else if (elementType === constants.BSON_DATA_UNDEFINED) {\n            value = undefined;\n        }\n        else if (elementType === constants.BSON_DATA_NULL) {\n            value = null;\n        }\n        else if (elementType === constants.BSON_DATA_LONG) {\n            // Unpack the low and high bits\n            var lowBits = buffer[index++] |\n                (buffer[index++] << 8) |\n                (buffer[index++] << 16) |\n                (buffer[index++] << 24);\n            var highBits = buffer[index++] |\n                (buffer[index++] << 8) |\n                (buffer[index++] << 16) |\n                (buffer[index++] << 24);\n            var long = new long_1.Long(lowBits, highBits);\n            // Promote the long if possible\n            if (promoteLongs && promoteValues === true) {\n                value =\n                    long.lessThanOrEqual(JS_INT_MAX_LONG) && long.greaterThanOrEqual(JS_INT_MIN_LONG)\n                        ? long.toNumber()\n                        : long;\n            }\n            else {\n                value = long;\n            }\n        }\n        else if (elementType === constants.BSON_DATA_DECIMAL128) {\n            // Buffer to contain the decimal bytes\n            var bytes = buffer_1.Buffer.alloc(16);\n            // Copy the next 16 bytes into the bytes buffer\n            buffer.copy(bytes, 0, index, index + 16);\n            // Update index\n            index = index + 16;\n            // Assign the new Decimal128 value\n            var decimal128 = new decimal128_1.Decimal128(bytes);\n            // If we have an alternative mapper use that\n            if ('toObject' in decimal128 && typeof decimal128.toObject === 'function') {\n                value = decimal128.toObject();\n            }\n            else {\n                value = decimal128;\n            }\n        }\n        else if (elementType === constants.BSON_DATA_BINARY) {\n            var binarySize = buffer[index++] |\n                (buffer[index++] << 8) |\n                (buffer[index++] << 16) |\n                (buffer[index++] << 24);\n            var totalBinarySize = binarySize;\n            var subType = buffer[index++];\n            // Did we have a negative binary size, throw\n            if (binarySize < 0)\n                throw new error_1.BSONError('Negative binary type element size found');\n            // Is the length longer than the document\n            if (binarySize > buffer.byteLength)\n                throw new error_1.BSONError('Binary type size larger than document size');\n            // Decode as raw Buffer object if options specifies it\n            if (buffer['slice'] != null) {\n                // If we have subtype 2 skip the 4 bytes for the size\n                if (subType === binary_1.Binary.SUBTYPE_BYTE_ARRAY) {\n                    binarySize =\n                        buffer[index++] |\n                            (buffer[index++] << 8) |\n                            (buffer[index++] << 16) |\n                            (buffer[index++] << 24);\n                    if (binarySize < 0)\n                        throw new error_1.BSONError('Negative binary type element size found for subtype 0x02');\n                    if (binarySize > totalBinarySize - 4)\n                        throw new error_1.BSONError('Binary type with subtype 0x02 contains too long binary size');\n                    if (binarySize < totalBinarySize - 4)\n                        throw new error_1.BSONError('Binary type with subtype 0x02 contains too short binary size');\n                }\n                if (promoteBuffers && promoteValues) {\n                    value = buffer.slice(index, index + binarySize);\n                }\n                else {\n                    value = new binary_1.Binary(buffer.slice(index, index + binarySize), subType);\n                    if (subType === constants.BSON_BINARY_SUBTYPE_UUID_NEW) {\n                        value = value.toUUID();\n                    }\n                }\n            }\n            else {\n                var _buffer = buffer_1.Buffer.alloc(binarySize);\n                // If we have subtype 2 skip the 4 bytes for the size\n                if (subType === binary_1.Binary.SUBTYPE_BYTE_ARRAY) {\n                    binarySize =\n                        buffer[index++] |\n                            (buffer[index++] << 8) |\n                            (buffer[index++] << 16) |\n                            (buffer[index++] << 24);\n                    if (binarySize < 0)\n                        throw new error_1.BSONError('Negative binary type element size found for subtype 0x02');\n                    if (binarySize > totalBinarySize - 4)\n                        throw new error_1.BSONError('Binary type with subtype 0x02 contains too long binary size');\n                    if (binarySize < totalBinarySize - 4)\n                        throw new error_1.BSONError('Binary type with subtype 0x02 contains too short binary size');\n                }\n                // Copy the data\n                for (i = 0; i < binarySize; i++) {\n                    _buffer[i] = buffer[index + i];\n                }\n                if (promoteBuffers && promoteValues) {\n                    value = _buffer;\n                }\n                else if (subType === constants.BSON_BINARY_SUBTYPE_UUID_NEW) {\n                    value = new binary_1.Binary(buffer.slice(index, index + binarySize), subType).toUUID();\n                }\n                else {\n                    value = new binary_1.Binary(buffer.slice(index, index + binarySize), subType);\n                }\n            }\n            // Update the index\n            index = index + binarySize;\n        }\n        else if (elementType === constants.BSON_DATA_REGEXP && bsonRegExp === false) {\n            // Get the start search index\n            i = index;\n            // Locate the end of the c string\n            while (buffer[i] !== 0x00 && i < buffer.length) {\n                i++;\n            }\n            // If are at the end of the buffer there is a problem with the document\n            if (i >= buffer.length)\n                throw new error_1.BSONError('Bad BSON Document: illegal CString');\n            // Return the C string\n            var source = buffer.toString('utf8', index, i);\n            // Create the regexp\n            index = i + 1;\n            // Get the start search index\n            i = index;\n            // Locate the end of the c string\n            while (buffer[i] !== 0x00 && i < buffer.length) {\n                i++;\n            }\n            // If are at the end of the buffer there is a problem with the document\n            if (i >= buffer.length)\n                throw new error_1.BSONError('Bad BSON Document: illegal CString');\n            // Return the C string\n            var regExpOptions = buffer.toString('utf8', index, i);\n            index = i + 1;\n            // For each option add the corresponding one for javascript\n            var optionsArray = new Array(regExpOptions.length);\n            // Parse options\n            for (i = 0; i < regExpOptions.length; i++) {\n                switch (regExpOptions[i]) {\n                    case 'm':\n                        optionsArray[i] = 'm';\n                        break;\n                    case 's':\n                        optionsArray[i] = 'g';\n                        break;\n                    case 'i':\n                        optionsArray[i] = 'i';\n                        break;\n                }\n            }\n            value = new RegExp(source, optionsArray.join(''));\n        }\n        else if (elementType === constants.BSON_DATA_REGEXP && bsonRegExp === true) {\n            // Get the start search index\n            i = index;\n            // Locate the end of the c string\n            while (buffer[i] !== 0x00 && i < buffer.length) {\n                i++;\n            }\n            // If are at the end of the buffer there is a problem with the document\n            if (i >= buffer.length)\n                throw new error_1.BSONError('Bad BSON Document: illegal CString');\n            // Return the C string\n            var source = buffer.toString('utf8', index, i);\n            index = i + 1;\n            // Get the start search index\n            i = index;\n            // Locate the end of the c string\n            while (buffer[i] !== 0x00 && i < buffer.length) {\n                i++;\n            }\n            // If are at the end of the buffer there is a problem with the document\n            if (i >= buffer.length)\n                throw new error_1.BSONError('Bad BSON Document: illegal CString');\n            // Return the C string\n            var regExpOptions = buffer.toString('utf8', index, i);\n            index = i + 1;\n            // Set the object\n            value = new regexp_1.BSONRegExp(source, regExpOptions);\n        }\n        else if (elementType === constants.BSON_DATA_SYMBOL) {\n            var stringSize = buffer[index++] |\n                (buffer[index++] << 8) |\n                (buffer[index++] << 16) |\n                (buffer[index++] << 24);\n            if (stringSize <= 0 ||\n                stringSize > buffer.length - index ||\n                buffer[index + stringSize - 1] !== 0) {\n                throw new error_1.BSONError('bad string length in bson');\n            }\n            var symbol = getValidatedString(buffer, index, index + stringSize - 1, shouldValidateKey);\n            value = promoteValues ? symbol : new symbol_1.BSONSymbol(symbol);\n            index = index + stringSize;\n        }\n        else if (elementType === constants.BSON_DATA_TIMESTAMP) {\n            var lowBits = buffer[index++] |\n                (buffer[index++] << 8) |\n                (buffer[index++] << 16) |\n                (buffer[index++] << 24);\n            var highBits = buffer[index++] |\n                (buffer[index++] << 8) |\n                (buffer[index++] << 16) |\n                (buffer[index++] << 24);\n            value = new timestamp_1.Timestamp(lowBits, highBits);\n        }\n        else if (elementType === constants.BSON_DATA_MIN_KEY) {\n            value = new min_key_1.MinKey();\n        }\n        else if (elementType === constants.BSON_DATA_MAX_KEY) {\n            value = new max_key_1.MaxKey();\n        }\n        else if (elementType === constants.BSON_DATA_CODE) {\n            var stringSize = buffer[index++] |\n                (buffer[index++] << 8) |\n                (buffer[index++] << 16) |\n                (buffer[index++] << 24);\n            if (stringSize <= 0 ||\n                stringSize > buffer.length - index ||\n                buffer[index + stringSize - 1] !== 0) {\n                throw new error_1.BSONError('bad string length in bson');\n            }\n            var functionString = getValidatedString(buffer, index, index + stringSize - 1, shouldValidateKey);\n            // If we are evaluating the functions\n            if (evalFunctions) {\n                // If we have cache enabled let's look for the md5 of the function in the cache\n                if (cacheFunctions) {\n                    // Got to do this to avoid V8 deoptimizing the call due to finding eval\n                    value = isolateEval(functionString, functionCache, object);\n                }\n                else {\n                    value = isolateEval(functionString);\n                }\n            }\n            else {\n                value = new code_1.Code(functionString);\n            }\n            // Update parse index position\n            index = index + stringSize;\n        }\n        else if (elementType === constants.BSON_DATA_CODE_W_SCOPE) {\n            var totalSize = buffer[index++] |\n                (buffer[index++] << 8) |\n                (buffer[index++] << 16) |\n                (buffer[index++] << 24);\n            // Element cannot be shorter than totalSize + stringSize + documentSize + terminator\n            if (totalSize < 4 + 4 + 4 + 1) {\n                throw new error_1.BSONError('code_w_scope total size shorter minimum expected length');\n            }\n            // Get the code string size\n            var stringSize = buffer[index++] |\n                (buffer[index++] << 8) |\n                (buffer[index++] << 16) |\n                (buffer[index++] << 24);\n            // Check if we have a valid string\n            if (stringSize <= 0 ||\n                stringSize > buffer.length - index ||\n                buffer[index + stringSize - 1] !== 0) {\n                throw new error_1.BSONError('bad string length in bson');\n            }\n            // Javascript function\n            var functionString = getValidatedString(buffer, index, index + stringSize - 1, shouldValidateKey);\n            // Update parse index position\n            index = index + stringSize;\n            // Parse the element\n            var _index = index;\n            // Decode the size of the object document\n            var objectSize = buffer[index] |\n                (buffer[index + 1] << 8) |\n                (buffer[index + 2] << 16) |\n                (buffer[index + 3] << 24);\n            // Decode the scope object\n            var scopeObject = deserializeObject(buffer, _index, options, false);\n            // Adjust the index\n            index = index + objectSize;\n            // Check if field length is too short\n            if (totalSize < 4 + 4 + objectSize + stringSize) {\n                throw new error_1.BSONError('code_w_scope total size is too short, truncating scope');\n            }\n            // Check if totalSize field is too long\n            if (totalSize > 4 + 4 + objectSize + stringSize) {\n                throw new error_1.BSONError('code_w_scope total size is too long, clips outer document');\n            }\n            // If we are evaluating the functions\n            if (evalFunctions) {\n                // If we have cache enabled let's look for the md5 of the function in the cache\n                if (cacheFunctions) {\n                    // Got to do this to avoid V8 deoptimizing the call due to finding eval\n                    value = isolateEval(functionString, functionCache, object);\n                }\n                else {\n                    value = isolateEval(functionString);\n                }\n                value.scope = scopeObject;\n            }\n            else {\n                value = new code_1.Code(functionString, scopeObject);\n            }\n        }\n        else if (elementType === constants.BSON_DATA_DBPOINTER) {\n            // Get the code string size\n            var stringSize = buffer[index++] |\n                (buffer[index++] << 8) |\n                (buffer[index++] << 16) |\n                (buffer[index++] << 24);\n            // Check if we have a valid string\n            if (stringSize <= 0 ||\n                stringSize > buffer.length - index ||\n                buffer[index + stringSize - 1] !== 0)\n                throw new error_1.BSONError('bad string length in bson');\n            // Namespace\n            if (validation != null && validation.utf8) {\n                if (!(0, validate_utf8_1.validateUtf8)(buffer, index, index + stringSize - 1)) {\n                    throw new error_1.BSONError('Invalid UTF-8 string in BSON document');\n                }\n            }\n            var namespace = buffer.toString('utf8', index, index + stringSize - 1);\n            // Update parse index position\n            index = index + stringSize;\n            // Read the oid\n            var oidBuffer = buffer_1.Buffer.alloc(12);\n            buffer.copy(oidBuffer, 0, index, index + 12);\n            var oid = new objectid_1.ObjectId(oidBuffer);\n            // Update the index\n            index = index + 12;\n            // Upgrade to DBRef type\n            value = new db_ref_1.DBRef(namespace, oid);\n        }\n        else {\n            throw new error_1.BSONError(\"Detected unknown BSON type \".concat(elementType.toString(16), \" for fieldname \\\"\").concat(name, \"\\\"\"));\n        }\n        if (name === '__proto__') {\n            Object.defineProperty(object, name, {\n                value: value,\n                writable: true,\n                enumerable: true,\n                configurable: true\n            });\n        }\n        else {\n            object[name] = value;\n        }\n    }\n    // Check if the deserialization was against a valid array/object\n    if (size !== index - startIndex) {\n        if (isArray)\n            throw new error_1.BSONError('corrupt array bson');\n        throw new error_1.BSONError('corrupt object bson');\n    }\n    // if we did not find \"$ref\", \"$id\", \"$db\", or found an extraneous $key, don't make a DBRef\n    if (!isPossibleDBRef)\n        return object;\n    if ((0, db_ref_1.isDBRefLike)(object)) {\n        var copy = Object.assign({}, object);\n        delete copy.$ref;\n        delete copy.$id;\n        delete copy.$db;\n        return new db_ref_1.DBRef(object.$ref, object.$id, object.$db, copy);\n    }\n    return object;\n}\n/**\n * Ensure eval is isolated, store the result in functionCache.\n *\n * @internal\n */\nfunction isolateEval(functionString, functionCache, object) {\n    // eslint-disable-next-line @typescript-eslint/no-implied-eval\n    if (!functionCache)\n        return new Function(functionString);\n    // Check for cache hit, eval if missing and return cached function\n    if (functionCache[functionString] == null) {\n        // eslint-disable-next-line @typescript-eslint/no-implied-eval\n        functionCache[functionString] = new Function(functionString);\n    }\n    // Set the object\n    return functionCache[functionString].bind(object);\n}\nfunction getValidatedString(buffer, start, end, shouldValidateUtf8) {\n    var value = buffer.toString('utf8', start, end);\n    // if utf8 validation is on, do the check\n    if (shouldValidateUtf8) {\n        for (var i = 0; i < value.length; i++) {\n            if (value.charCodeAt(i) === 0xfffd) {\n                if (!(0, validate_utf8_1.validateUtf8)(buffer, start, end)) {\n                    throw new error_1.BSONError('Invalid UTF-8 string in BSON document');\n                }\n                break;\n            }\n        }\n    }\n    return value;\n}\n//# sourceMappingURL=deserializer.js.map", "\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.validateUtf8 = void 0;\nvar FIRST_BIT = 0x80;\nvar FIRST_TWO_BITS = 0xc0;\nvar FIRST_THREE_BITS = 0xe0;\nvar FIRST_FOUR_BITS = 0xf0;\nvar FIRST_FIVE_BITS = 0xf8;\nvar TWO_BIT_CHAR = 0xc0;\nvar THREE_BIT_CHAR = 0xe0;\nvar FOUR_BIT_CHAR = 0xf0;\nvar CONTINUING_CHAR = 0x80;\n/**\n * Determines if the passed in bytes are valid utf8\n * @param bytes - An array of 8-bit bytes. Must be indexable and have length property\n * @param start - The index to start validating\n * @param end - The index to end validating\n */\nfunction validateUtf8(bytes, start, end) {\n    var continuation = 0;\n    for (var i = start; i < end; i += 1) {\n        var byte = bytes[i];\n        if (continuation) {\n            if ((byte & FIRST_TWO_BITS) !== CONTINUING_CHAR) {\n                return false;\n            }\n            continuation -= 1;\n        }\n        else if (byte & FIRST_BIT) {\n            if ((byte & FIRST_THREE_BITS) === TWO_BIT_CHAR) {\n                continuation = 1;\n            }\n            else if ((byte & FIRST_FOUR_BITS) === THREE_BIT_CHAR) {\n                continuation = 2;\n            }\n            else if ((byte & FIRST_FIVE_BITS) === FOUR_BIT_CHAR) {\n                continuation = 3;\n            }\n            else {\n                return false;\n            }\n        }\n    }\n    return !continuation;\n}\nexports.validateUtf8 = validateUtf8;\n//# sourceMappingURL=validate_utf8.js.map", "\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.serializeInto = void 0;\nvar binary_1 = require(\"../binary\");\nvar constants = require(\"../constants\");\nvar ensure_buffer_1 = require(\"../ensure_buffer\");\nvar error_1 = require(\"../error\");\nvar extended_json_1 = require(\"../extended_json\");\nvar long_1 = require(\"../long\");\nvar map_1 = require(\"../map\");\nvar utils_1 = require(\"./utils\");\nvar regexp = /\\x00/; // eslint-disable-line no-control-regex\nvar ignoreKeys = new Set(['$db', '$ref', '$id', '$clusterTime']);\n/*\n * isArray indicates if we are writing to a BSON array (type 0x04)\n * which forces the \"key\" which really an array index as a string to be written as ascii\n * This will catch any errors in index as a string generation\n */\nfunction serializeString(buffer, key, value, index, isArray) {\n    // Encode String type\n    buffer[index++] = constants.BSON_DATA_STRING;\n    // Number of written bytes\n    var numberOfWrittenBytes = !isArray\n        ? buffer.write(key, index, undefined, 'utf8')\n        : buffer.write(key, index, undefined, 'ascii');\n    // Encode the name\n    index = index + numberOfWrittenBytes + 1;\n    buffer[index - 1] = 0;\n    // Write the string\n    var size = buffer.write(value, index + 4, undefined, 'utf8');\n    // Write the size of the string to buffer\n    buffer[index + 3] = ((size + 1) >> 24) & 0xff;\n    buffer[index + 2] = ((size + 1) >> 16) & 0xff;\n    buffer[index + 1] = ((size + 1) >> 8) & 0xff;\n    buffer[index] = (size + 1) & 0xff;\n    // Update index\n    index = index + 4 + size;\n    // Write zero\n    buffer[index++] = 0;\n    return index;\n}\nvar SPACE_FOR_FLOAT64 = new Uint8Array(8);\nvar DV_FOR_FLOAT64 = new DataView(SPACE_FOR_FLOAT64.buffer, SPACE_FOR_FLOAT64.byteOffset, SPACE_FOR_FLOAT64.byteLength);\nfunction serializeNumber(buffer, key, value, index, isArray) {\n    // We have an integer value\n    // TODO(NODE-2529): Add support for big int\n    if (Number.isInteger(value) &&\n        value >= constants.BSON_INT32_MIN &&\n        value <= constants.BSON_INT32_MAX) {\n        // If the value fits in 32 bits encode as int32\n        // Set int type 32 bits or less\n        buffer[index++] = constants.BSON_DATA_INT;\n        // Number of written bytes\n        var numberOfWrittenBytes = !isArray\n            ? buffer.write(key, index, undefined, 'utf8')\n            : buffer.write(key, index, undefined, 'ascii');\n        // Encode the name\n        index = index + numberOfWrittenBytes;\n        buffer[index++] = 0;\n        // Write the int value\n        buffer[index++] = value & 0xff;\n        buffer[index++] = (value >> 8) & 0xff;\n        buffer[index++] = (value >> 16) & 0xff;\n        buffer[index++] = (value >> 24) & 0xff;\n    }\n    else {\n        // Encode as double\n        buffer[index++] = constants.BSON_DATA_NUMBER;\n        // Number of written bytes\n        var numberOfWrittenBytes = !isArray\n            ? buffer.write(key, index, undefined, 'utf8')\n            : buffer.write(key, index, undefined, 'ascii');\n        // Encode the name\n        index = index + numberOfWrittenBytes;\n        buffer[index++] = 0;\n        // Write float\n        DV_FOR_FLOAT64.setFloat64(0, value, true);\n        buffer.set(SPACE_FOR_FLOAT64, index);\n        // Adjust index\n        index = index + 8;\n    }\n    return index;\n}\nfunction serializeNull(buffer, key, _, index, isArray) {\n    // Set long type\n    buffer[index++] = constants.BSON_DATA_NULL;\n    // Number of written bytes\n    var numberOfWrittenBytes = !isArray\n        ? buffer.write(key, index, undefined, 'utf8')\n        : buffer.write(key, index, undefined, 'ascii');\n    // Encode the name\n    index = index + numberOfWrittenBytes;\n    buffer[index++] = 0;\n    return index;\n}\nfunction serializeBoolean(buffer, key, value, index, isArray) {\n    // Write the type\n    buffer[index++] = constants.BSON_DATA_BOOLEAN;\n    // Number of written bytes\n    var numberOfWrittenBytes = !isArray\n        ? buffer.write(key, index, undefined, 'utf8')\n        : buffer.write(key, index, undefined, 'ascii');\n    // Encode the name\n    index = index + numberOfWrittenBytes;\n    buffer[index++] = 0;\n    // Encode the boolean value\n    buffer[index++] = value ? 1 : 0;\n    return index;\n}\nfunction serializeDate(buffer, key, value, index, isArray) {\n    // Write the type\n    buffer[index++] = constants.BSON_DATA_DATE;\n    // Number of written bytes\n    var numberOfWrittenBytes = !isArray\n        ? buffer.write(key, index, undefined, 'utf8')\n        : buffer.write(key, index, undefined, 'ascii');\n    // Encode the name\n    index = index + numberOfWrittenBytes;\n    buffer[index++] = 0;\n    // Write the date\n    var dateInMilis = long_1.Long.fromNumber(value.getTime());\n    var lowBits = dateInMilis.getLowBits();\n    var highBits = dateInMilis.getHighBits();\n    // Encode low bits\n    buffer[index++] = lowBits & 0xff;\n    buffer[index++] = (lowBits >> 8) & 0xff;\n    buffer[index++] = (lowBits >> 16) & 0xff;\n    buffer[index++] = (lowBits >> 24) & 0xff;\n    // Encode high bits\n    buffer[index++] = highBits & 0xff;\n    buffer[index++] = (highBits >> 8) & 0xff;\n    buffer[index++] = (highBits >> 16) & 0xff;\n    buffer[index++] = (highBits >> 24) & 0xff;\n    return index;\n}\nfunction serializeRegExp(buffer, key, value, index, isArray) {\n    // Write the type\n    buffer[index++] = constants.BSON_DATA_REGEXP;\n    // Number of written bytes\n    var numberOfWrittenBytes = !isArray\n        ? buffer.write(key, index, undefined, 'utf8')\n        : buffer.write(key, index, undefined, 'ascii');\n    // Encode the name\n    index = index + numberOfWrittenBytes;\n    buffer[index++] = 0;\n    if (value.source && value.source.match(regexp) != null) {\n        throw Error('value ' + value.source + ' must not contain null bytes');\n    }\n    // Adjust the index\n    index = index + buffer.write(value.source, index, undefined, 'utf8');\n    // Write zero\n    buffer[index++] = 0x00;\n    // Write the parameters\n    if (value.ignoreCase)\n        buffer[index++] = 0x69; // i\n    if (value.global)\n        buffer[index++] = 0x73; // s\n    if (value.multiline)\n        buffer[index++] = 0x6d; // m\n    // Add ending zero\n    buffer[index++] = 0x00;\n    return index;\n}\nfunction serializeBSONRegExp(buffer, key, value, index, isArray) {\n    // Write the type\n    buffer[index++] = constants.BSON_DATA_REGEXP;\n    // Number of written bytes\n    var numberOfWrittenBytes = !isArray\n        ? buffer.write(key, index, undefined, 'utf8')\n        : buffer.write(key, index, undefined, 'ascii');\n    // Encode the name\n    index = index + numberOfWrittenBytes;\n    buffer[index++] = 0;\n    // Check the pattern for 0 bytes\n    if (value.pattern.match(regexp) != null) {\n        // The BSON spec doesn't allow keys with null bytes because keys are\n        // null-terminated.\n        throw Error('pattern ' + value.pattern + ' must not contain null bytes');\n    }\n    // Adjust the index\n    index = index + buffer.write(value.pattern, index, undefined, 'utf8');\n    // Write zero\n    buffer[index++] = 0x00;\n    // Write the options\n    index = index + buffer.write(value.options.split('').sort().join(''), index, undefined, 'utf8');\n    // Add ending zero\n    buffer[index++] = 0x00;\n    return index;\n}\nfunction serializeMinMax(buffer, key, value, index, isArray) {\n    // Write the type of either min or max key\n    if (value === null) {\n        buffer[index++] = constants.BSON_DATA_NULL;\n    }\n    else if (value._bsontype === 'MinKey') {\n        buffer[index++] = constants.BSON_DATA_MIN_KEY;\n    }\n    else {\n        buffer[index++] = constants.BSON_DATA_MAX_KEY;\n    }\n    // Number of written bytes\n    var numberOfWrittenBytes = !isArray\n        ? buffer.write(key, index, undefined, 'utf8')\n        : buffer.write(key, index, undefined, 'ascii');\n    // Encode the name\n    index = index + numberOfWrittenBytes;\n    buffer[index++] = 0;\n    return index;\n}\nfunction serializeObjectId(buffer, key, value, index, isArray) {\n    // Write the type\n    buffer[index++] = constants.BSON_DATA_OID;\n    // Number of written bytes\n    var numberOfWrittenBytes = !isArray\n        ? buffer.write(key, index, undefined, 'utf8')\n        : buffer.write(key, index, undefined, 'ascii');\n    // Encode the name\n    index = index + numberOfWrittenBytes;\n    buffer[index++] = 0;\n    // Write the objectId into the shared buffer\n    if (typeof value.id === 'string') {\n        buffer.write(value.id, index, undefined, 'binary');\n    }\n    else if ((0, utils_1.isUint8Array)(value.id)) {\n        // Use the standard JS methods here because buffer.copy() is buggy with the\n        // browser polyfill\n        buffer.set(value.id.subarray(0, 12), index);\n    }\n    else {\n        throw new error_1.BSONTypeError('object [' + JSON.stringify(value) + '] is not a valid ObjectId');\n    }\n    // Adjust index\n    return index + 12;\n}\nfunction serializeBuffer(buffer, key, value, index, isArray) {\n    // Write the type\n    buffer[index++] = constants.BSON_DATA_BINARY;\n    // Number of written bytes\n    var numberOfWrittenBytes = !isArray\n        ? buffer.write(key, index, undefined, 'utf8')\n        : buffer.write(key, index, undefined, 'ascii');\n    // Encode the name\n    index = index + numberOfWrittenBytes;\n    buffer[index++] = 0;\n    // Get size of the buffer (current write point)\n    var size = value.length;\n    // Write the size of the string to buffer\n    buffer[index++] = size & 0xff;\n    buffer[index++] = (size >> 8) & 0xff;\n    buffer[index++] = (size >> 16) & 0xff;\n    buffer[index++] = (size >> 24) & 0xff;\n    // Write the default subtype\n    buffer[index++] = constants.BSON_BINARY_SUBTYPE_DEFAULT;\n    // Copy the content form the binary field to the buffer\n    buffer.set((0, ensure_buffer_1.ensureBuffer)(value), index);\n    // Adjust the index\n    index = index + size;\n    return index;\n}\nfunction serializeObject(buffer, key, value, index, checkKeys, depth, serializeFunctions, ignoreUndefined, isArray, path) {\n    if (checkKeys === void 0) { checkKeys = false; }\n    if (depth === void 0) { depth = 0; }\n    if (serializeFunctions === void 0) { serializeFunctions = false; }\n    if (ignoreUndefined === void 0) { ignoreUndefined = true; }\n    if (isArray === void 0) { isArray = false; }\n    if (path === void 0) { path = []; }\n    for (var i = 0; i < path.length; i++) {\n        if (path[i] === value)\n            throw new error_1.BSONError('cyclic dependency detected');\n    }\n    // Push value to stack\n    path.push(value);\n    // Write the type\n    buffer[index++] = Array.isArray(value) ? constants.BSON_DATA_ARRAY : constants.BSON_DATA_OBJECT;\n    // Number of written bytes\n    var numberOfWrittenBytes = !isArray\n        ? buffer.write(key, index, undefined, 'utf8')\n        : buffer.write(key, index, undefined, 'ascii');\n    // Encode the name\n    index = index + numberOfWrittenBytes;\n    buffer[index++] = 0;\n    var endIndex = serializeInto(buffer, value, checkKeys, index, depth + 1, serializeFunctions, ignoreUndefined, path);\n    // Pop stack\n    path.pop();\n    return endIndex;\n}\nfunction serializeDecimal128(buffer, key, value, index, isArray) {\n    buffer[index++] = constants.BSON_DATA_DECIMAL128;\n    // Number of written bytes\n    var numberOfWrittenBytes = !isArray\n        ? buffer.write(key, index, undefined, 'utf8')\n        : buffer.write(key, index, undefined, 'ascii');\n    // Encode the name\n    index = index + numberOfWrittenBytes;\n    buffer[index++] = 0;\n    // Write the data from the value\n    // Prefer the standard JS methods because their typechecking is not buggy,\n    // unlike the `buffer` polyfill's.\n    buffer.set(value.bytes.subarray(0, 16), index);\n    return index + 16;\n}\nfunction serializeLong(buffer, key, value, index, isArray) {\n    // Write the type\n    buffer[index++] =\n        value._bsontype === 'Long' ? constants.BSON_DATA_LONG : constants.BSON_DATA_TIMESTAMP;\n    // Number of written bytes\n    var numberOfWrittenBytes = !isArray\n        ? buffer.write(key, index, undefined, 'utf8')\n        : buffer.write(key, index, undefined, 'ascii');\n    // Encode the name\n    index = index + numberOfWrittenBytes;\n    buffer[index++] = 0;\n    // Write the date\n    var lowBits = value.getLowBits();\n    var highBits = value.getHighBits();\n    // Encode low bits\n    buffer[index++] = lowBits & 0xff;\n    buffer[index++] = (lowBits >> 8) & 0xff;\n    buffer[index++] = (lowBits >> 16) & 0xff;\n    buffer[index++] = (lowBits >> 24) & 0xff;\n    // Encode high bits\n    buffer[index++] = highBits & 0xff;\n    buffer[index++] = (highBits >> 8) & 0xff;\n    buffer[index++] = (highBits >> 16) & 0xff;\n    buffer[index++] = (highBits >> 24) & 0xff;\n    return index;\n}\nfunction serializeInt32(buffer, key, value, index, isArray) {\n    value = value.valueOf();\n    // Set int type 32 bits or less\n    buffer[index++] = constants.BSON_DATA_INT;\n    // Number of written bytes\n    var numberOfWrittenBytes = !isArray\n        ? buffer.write(key, index, undefined, 'utf8')\n        : buffer.write(key, index, undefined, 'ascii');\n    // Encode the name\n    index = index + numberOfWrittenBytes;\n    buffer[index++] = 0;\n    // Write the int value\n    buffer[index++] = value & 0xff;\n    buffer[index++] = (value >> 8) & 0xff;\n    buffer[index++] = (value >> 16) & 0xff;\n    buffer[index++] = (value >> 24) & 0xff;\n    return index;\n}\nfunction serializeDouble(buffer, key, value, index, isArray) {\n    // Encode as double\n    buffer[index++] = constants.BSON_DATA_NUMBER;\n    // Number of written bytes\n    var numberOfWrittenBytes = !isArray\n        ? buffer.write(key, index, undefined, 'utf8')\n        : buffer.write(key, index, undefined, 'ascii');\n    // Encode the name\n    index = index + numberOfWrittenBytes;\n    buffer[index++] = 0;\n    // Write float\n    DV_FOR_FLOAT64.setFloat64(0, value.value, true);\n    buffer.set(SPACE_FOR_FLOAT64, index);\n    // Adjust index\n    index = index + 8;\n    return index;\n}\nfunction serializeFunction(buffer, key, value, index, _checkKeys, _depth, isArray) {\n    if (_checkKeys === void 0) { _checkKeys = false; }\n    if (_depth === void 0) { _depth = 0; }\n    buffer[index++] = constants.BSON_DATA_CODE;\n    // Number of written bytes\n    var numberOfWrittenBytes = !isArray\n        ? buffer.write(key, index, undefined, 'utf8')\n        : buffer.write(key, index, undefined, 'ascii');\n    // Encode the name\n    index = index + numberOfWrittenBytes;\n    buffer[index++] = 0;\n    // Function string\n    var functionString = (0, utils_1.normalizedFunctionString)(value);\n    // Write the string\n    var size = buffer.write(functionString, index + 4, undefined, 'utf8') + 1;\n    // Write the size of the string to buffer\n    buffer[index] = size & 0xff;\n    buffer[index + 1] = (size >> 8) & 0xff;\n    buffer[index + 2] = (size >> 16) & 0xff;\n    buffer[index + 3] = (size >> 24) & 0xff;\n    // Update index\n    index = index + 4 + size - 1;\n    // Write zero\n    buffer[index++] = 0;\n    return index;\n}\nfunction serializeCode(buffer, key, value, index, checkKeys, depth, serializeFunctions, ignoreUndefined, isArray) {\n    if (checkKeys === void 0) { checkKeys = false; }\n    if (depth === void 0) { depth = 0; }\n    if (serializeFunctions === void 0) { serializeFunctions = false; }\n    if (ignoreUndefined === void 0) { ignoreUndefined = true; }\n    if (isArray === void 0) { isArray = false; }\n    if (value.scope && typeof value.scope === 'object') {\n        // Write the type\n        buffer[index++] = constants.BSON_DATA_CODE_W_SCOPE;\n        // Number of written bytes\n        var numberOfWrittenBytes = !isArray\n            ? buffer.write(key, index, undefined, 'utf8')\n            : buffer.write(key, index, undefined, 'ascii');\n        // Encode the name\n        index = index + numberOfWrittenBytes;\n        buffer[index++] = 0;\n        // Starting index\n        var startIndex = index;\n        // Serialize the function\n        // Get the function string\n        var functionString = typeof value.code === 'string' ? value.code : value.code.toString();\n        // Index adjustment\n        index = index + 4;\n        // Write string into buffer\n        var codeSize = buffer.write(functionString, index + 4, undefined, 'utf8') + 1;\n        // Write the size of the string to buffer\n        buffer[index] = codeSize & 0xff;\n        buffer[index + 1] = (codeSize >> 8) & 0xff;\n        buffer[index + 2] = (codeSize >> 16) & 0xff;\n        buffer[index + 3] = (codeSize >> 24) & 0xff;\n        // Write end 0\n        buffer[index + 4 + codeSize - 1] = 0;\n        // Write the\n        index = index + codeSize + 4;\n        //\n        // Serialize the scope value\n        var endIndex = serializeInto(buffer, value.scope, checkKeys, index, depth + 1, serializeFunctions, ignoreUndefined);\n        index = endIndex - 1;\n        // Writ the total\n        var totalSize = endIndex - startIndex;\n        // Write the total size of the object\n        buffer[startIndex++] = totalSize & 0xff;\n        buffer[startIndex++] = (totalSize >> 8) & 0xff;\n        buffer[startIndex++] = (totalSize >> 16) & 0xff;\n        buffer[startIndex++] = (totalSize >> 24) & 0xff;\n        // Write trailing zero\n        buffer[index++] = 0;\n    }\n    else {\n        buffer[index++] = constants.BSON_DATA_CODE;\n        // Number of written bytes\n        var numberOfWrittenBytes = !isArray\n            ? buffer.write(key, index, undefined, 'utf8')\n            : buffer.write(key, index, undefined, 'ascii');\n        // Encode the name\n        index = index + numberOfWrittenBytes;\n        buffer[index++] = 0;\n        // Function string\n        var functionString = value.code.toString();\n        // Write the string\n        var size = buffer.write(functionString, index + 4, undefined, 'utf8') + 1;\n        // Write the size of the string to buffer\n        buffer[index] = size & 0xff;\n        buffer[index + 1] = (size >> 8) & 0xff;\n        buffer[index + 2] = (size >> 16) & 0xff;\n        buffer[index + 3] = (size >> 24) & 0xff;\n        // Update index\n        index = index + 4 + size - 1;\n        // Write zero\n        buffer[index++] = 0;\n    }\n    return index;\n}\nfunction serializeBinary(buffer, key, value, index, isArray) {\n    // Write the type\n    buffer[index++] = constants.BSON_DATA_BINARY;\n    // Number of written bytes\n    var numberOfWrittenBytes = !isArray\n        ? buffer.write(key, index, undefined, 'utf8')\n        : buffer.write(key, index, undefined, 'ascii');\n    // Encode the name\n    index = index + numberOfWrittenBytes;\n    buffer[index++] = 0;\n    // Extract the buffer\n    var data = value.value(true);\n    // Calculate size\n    var size = value.position;\n    // Add the deprecated 02 type 4 bytes of size to total\n    if (value.sub_type === binary_1.Binary.SUBTYPE_BYTE_ARRAY)\n        size = size + 4;\n    // Write the size of the string to buffer\n    buffer[index++] = size & 0xff;\n    buffer[index++] = (size >> 8) & 0xff;\n    buffer[index++] = (size >> 16) & 0xff;\n    buffer[index++] = (size >> 24) & 0xff;\n    // Write the subtype to the buffer\n    buffer[index++] = value.sub_type;\n    // If we have binary type 2 the 4 first bytes are the size\n    if (value.sub_type === binary_1.Binary.SUBTYPE_BYTE_ARRAY) {\n        size = size - 4;\n        buffer[index++] = size & 0xff;\n        buffer[index++] = (size >> 8) & 0xff;\n        buffer[index++] = (size >> 16) & 0xff;\n        buffer[index++] = (size >> 24) & 0xff;\n    }\n    // Write the data to the object\n    buffer.set(data, index);\n    // Adjust the index\n    index = index + value.position;\n    return index;\n}\nfunction serializeSymbol(buffer, key, value, index, isArray) {\n    // Write the type\n    buffer[index++] = constants.BSON_DATA_SYMBOL;\n    // Number of written bytes\n    var numberOfWrittenBytes = !isArray\n        ? buffer.write(key, index, undefined, 'utf8')\n        : buffer.write(key, index, undefined, 'ascii');\n    // Encode the name\n    index = index + numberOfWrittenBytes;\n    buffer[index++] = 0;\n    // Write the string\n    var size = buffer.write(value.value, index + 4, undefined, 'utf8') + 1;\n    // Write the size of the string to buffer\n    buffer[index] = size & 0xff;\n    buffer[index + 1] = (size >> 8) & 0xff;\n    buffer[index + 2] = (size >> 16) & 0xff;\n    buffer[index + 3] = (size >> 24) & 0xff;\n    // Update index\n    index = index + 4 + size - 1;\n    // Write zero\n    buffer[index++] = 0x00;\n    return index;\n}\nfunction serializeDBRef(buffer, key, value, index, depth, serializeFunctions, isArray) {\n    // Write the type\n    buffer[index++] = constants.BSON_DATA_OBJECT;\n    // Number of written bytes\n    var numberOfWrittenBytes = !isArray\n        ? buffer.write(key, index, undefined, 'utf8')\n        : buffer.write(key, index, undefined, 'ascii');\n    // Encode the name\n    index = index + numberOfWrittenBytes;\n    buffer[index++] = 0;\n    var startIndex = index;\n    var output = {\n        $ref: value.collection || value.namespace,\n        $id: value.oid\n    };\n    if (value.db != null) {\n        output.$db = value.db;\n    }\n    output = Object.assign(output, value.fields);\n    var endIndex = serializeInto(buffer, output, false, index, depth + 1, serializeFunctions);\n    // Calculate object size\n    var size = endIndex - startIndex;\n    // Write the size\n    buffer[startIndex++] = size & 0xff;\n    buffer[startIndex++] = (size >> 8) & 0xff;\n    buffer[startIndex++] = (size >> 16) & 0xff;\n    buffer[startIndex++] = (size >> 24) & 0xff;\n    // Set index\n    return endIndex;\n}\nfunction serializeInto(buffer, object, checkKeys, startingIndex, depth, serializeFunctions, ignoreUndefined, path) {\n    if (checkKeys === void 0) { checkKeys = false; }\n    if (startingIndex === void 0) { startingIndex = 0; }\n    if (depth === void 0) { depth = 0; }\n    if (serializeFunctions === void 0) { serializeFunctions = false; }\n    if (ignoreUndefined === void 0) { ignoreUndefined = true; }\n    if (path === void 0) { path = []; }\n    startingIndex = startingIndex || 0;\n    path = path || [];\n    // Push the object to the path\n    path.push(object);\n    // Start place to serialize into\n    var index = startingIndex + 4;\n    // Special case isArray\n    if (Array.isArray(object)) {\n        // Get object keys\n        for (var i = 0; i < object.length; i++) {\n            var key = \"\".concat(i);\n            var value = object[i];\n            // Is there an override value\n            if (typeof (value === null || value === void 0 ? void 0 : value.toBSON) === 'function') {\n                value = value.toBSON();\n            }\n            if (typeof value === 'string') {\n                index = serializeString(buffer, key, value, index, true);\n            }\n            else if (typeof value === 'number') {\n                index = serializeNumber(buffer, key, value, index, true);\n            }\n            else if (typeof value === 'bigint') {\n                throw new error_1.BSONTypeError('Unsupported type BigInt, please use Decimal128');\n            }\n            else if (typeof value === 'boolean') {\n                index = serializeBoolean(buffer, key, value, index, true);\n            }\n            else if (value instanceof Date || (0, utils_1.isDate)(value)) {\n                index = serializeDate(buffer, key, value, index, true);\n            }\n            else if (value === undefined) {\n                index = serializeNull(buffer, key, value, index, true);\n            }\n            else if (value === null) {\n                index = serializeNull(buffer, key, value, index, true);\n            }\n            else if (value['_bsontype'] === 'ObjectId' || value['_bsontype'] === 'ObjectID') {\n                index = serializeObjectId(buffer, key, value, index, true);\n            }\n            else if ((0, utils_1.isUint8Array)(value)) {\n                index = serializeBuffer(buffer, key, value, index, true);\n            }\n            else if (value instanceof RegExp || (0, utils_1.isRegExp)(value)) {\n                index = serializeRegExp(buffer, key, value, index, true);\n            }\n            else if (typeof value === 'object' && value['_bsontype'] == null) {\n                index = serializeObject(buffer, key, value, index, checkKeys, depth, serializeFunctions, ignoreUndefined, true, path);\n            }\n            else if (typeof value === 'object' &&\n                (0, extended_json_1.isBSONType)(value) &&\n                value._bsontype === 'Decimal128') {\n                index = serializeDecimal128(buffer, key, value, index, true);\n            }\n            else if (value['_bsontype'] === 'Long' || value['_bsontype'] === 'Timestamp') {\n                index = serializeLong(buffer, key, value, index, true);\n            }\n            else if (value['_bsontype'] === 'Double') {\n                index = serializeDouble(buffer, key, value, index, true);\n            }\n            else if (typeof value === 'function' && serializeFunctions) {\n                index = serializeFunction(buffer, key, value, index, checkKeys, depth, true);\n            }\n            else if (value['_bsontype'] === 'Code') {\n                index = serializeCode(buffer, key, value, index, checkKeys, depth, serializeFunctions, ignoreUndefined, true);\n            }\n            else if (value['_bsontype'] === 'Binary') {\n                index = serializeBinary(buffer, key, value, index, true);\n            }\n            else if (value['_bsontype'] === 'Symbol') {\n                index = serializeSymbol(buffer, key, value, index, true);\n            }\n            else if (value['_bsontype'] === 'DBRef') {\n                index = serializeDBRef(buffer, key, value, index, depth, serializeFunctions, true);\n            }\n            else if (value['_bsontype'] === 'BSONRegExp') {\n                index = serializeBSONRegExp(buffer, key, value, index, true);\n            }\n            else if (value['_bsontype'] === 'Int32') {\n                index = serializeInt32(buffer, key, value, index, true);\n            }\n            else if (value['_bsontype'] === 'MinKey' || value['_bsontype'] === 'MaxKey') {\n                index = serializeMinMax(buffer, key, value, index, true);\n            }\n            else if (typeof value['_bsontype'] !== 'undefined') {\n                throw new error_1.BSONTypeError(\"Unrecognized or invalid _bsontype: \".concat(String(value['_bsontype'])));\n            }\n        }\n    }\n    else if (object instanceof map_1.Map || (0, utils_1.isMap)(object)) {\n        var iterator = object.entries();\n        var done = false;\n        while (!done) {\n            // Unpack the next entry\n            var entry = iterator.next();\n            done = !!entry.done;\n            // Are we done, then skip and terminate\n            if (done)\n                continue;\n            // Get the entry values\n            var key = entry.value[0];\n            var value = entry.value[1];\n            // Check the type of the value\n            var type = typeof value;\n            // Check the key and throw error if it's illegal\n            if (typeof key === 'string' && !ignoreKeys.has(key)) {\n                if (key.match(regexp) != null) {\n                    // The BSON spec doesn't allow keys with null bytes because keys are\n                    // null-terminated.\n                    throw Error('key ' + key + ' must not contain null bytes');\n                }\n                if (checkKeys) {\n                    if ('$' === key[0]) {\n                        throw Error('key ' + key + \" must not start with '$'\");\n                    }\n                    else if (~key.indexOf('.')) {\n                        throw Error('key ' + key + \" must not contain '.'\");\n                    }\n                }\n            }\n            if (type === 'string') {\n                index = serializeString(buffer, key, value, index);\n            }\n            else if (type === 'number') {\n                index = serializeNumber(buffer, key, value, index);\n            }\n            else if (type === 'bigint' || (0, utils_1.isBigInt64Array)(value) || (0, utils_1.isBigUInt64Array)(value)) {\n                throw new error_1.BSONTypeError('Unsupported type BigInt, please use Decimal128');\n            }\n            else if (type === 'boolean') {\n                index = serializeBoolean(buffer, key, value, index);\n            }\n            else if (value instanceof Date || (0, utils_1.isDate)(value)) {\n                index = serializeDate(buffer, key, value, index);\n            }\n            else if (value === null || (value === undefined && ignoreUndefined === false)) {\n                index = serializeNull(buffer, key, value, index);\n            }\n            else if (value['_bsontype'] === 'ObjectId' || value['_bsontype'] === 'ObjectID') {\n                index = serializeObjectId(buffer, key, value, index);\n            }\n            else if ((0, utils_1.isUint8Array)(value)) {\n                index = serializeBuffer(buffer, key, value, index);\n            }\n            else if (value instanceof RegExp || (0, utils_1.isRegExp)(value)) {\n                index = serializeRegExp(buffer, key, value, index);\n            }\n            else if (type === 'object' && value['_bsontype'] == null) {\n                index = serializeObject(buffer, key, value, index, checkKeys, depth, serializeFunctions, ignoreUndefined, false, path);\n            }\n            else if (type === 'object' && value['_bsontype'] === 'Decimal128') {\n                index = serializeDecimal128(buffer, key, value, index);\n            }\n            else if (value['_bsontype'] === 'Long' || value['_bsontype'] === 'Timestamp') {\n                index = serializeLong(buffer, key, value, index);\n            }\n            else if (value['_bsontype'] === 'Double') {\n                index = serializeDouble(buffer, key, value, index);\n            }\n            else if (value['_bsontype'] === 'Code') {\n                index = serializeCode(buffer, key, value, index, checkKeys, depth, serializeFunctions, ignoreUndefined);\n            }\n            else if (typeof value === 'function' && serializeFunctions) {\n                index = serializeFunction(buffer, key, value, index, checkKeys, depth, serializeFunctions);\n            }\n            else if (value['_bsontype'] === 'Binary') {\n                index = serializeBinary(buffer, key, value, index);\n            }\n            else if (value['_bsontype'] === 'Symbol') {\n                index = serializeSymbol(buffer, key, value, index);\n            }\n            else if (value['_bsontype'] === 'DBRef') {\n                index = serializeDBRef(buffer, key, value, index, depth, serializeFunctions);\n            }\n            else if (value['_bsontype'] === 'BSONRegExp') {\n                index = serializeBSONRegExp(buffer, key, value, index);\n            }\n            else if (value['_bsontype'] === 'Int32') {\n                index = serializeInt32(buffer, key, value, index);\n            }\n            else if (value['_bsontype'] === 'MinKey' || value['_bsontype'] === 'MaxKey') {\n                index = serializeMinMax(buffer, key, value, index);\n            }\n            else if (typeof value['_bsontype'] !== 'undefined') {\n                throw new error_1.BSONTypeError(\"Unrecognized or invalid _bsontype: \".concat(String(value['_bsontype'])));\n            }\n        }\n    }\n    else {\n        if (typeof (object === null || object === void 0 ? void 0 : object.toBSON) === 'function') {\n            // Provided a custom serialization method\n            object = object.toBSON();\n            if (object != null && typeof object !== 'object') {\n                throw new error_1.BSONTypeError('toBSON function did not return an object');\n            }\n        }\n        // Iterate over all the keys\n        for (var key in object) {\n            var value = object[key];\n            // Is there an override value\n            if (typeof (value === null || value === void 0 ? void 0 : value.toBSON) === 'function') {\n                value = value.toBSON();\n            }\n            // Check the type of the value\n            var type = typeof value;\n            // Check the key and throw error if it's illegal\n            if (typeof key === 'string' && !ignoreKeys.has(key)) {\n                if (key.match(regexp) != null) {\n                    // The BSON spec doesn't allow keys with null bytes because keys are\n                    // null-terminated.\n                    throw Error('key ' + key + ' must not contain null bytes');\n                }\n                if (checkKeys) {\n                    if ('$' === key[0]) {\n                        throw Error('key ' + key + \" must not start with '$'\");\n                    }\n                    else if (~key.indexOf('.')) {\n                        throw Error('key ' + key + \" must not contain '.'\");\n                    }\n                }\n            }\n            if (type === 'string') {\n                index = serializeString(buffer, key, value, index);\n            }\n            else if (type === 'number') {\n                index = serializeNumber(buffer, key, value, index);\n            }\n            else if (type === 'bigint') {\n                throw new error_1.BSONTypeError('Unsupported type BigInt, please use Decimal128');\n            }\n            else if (type === 'boolean') {\n                index = serializeBoolean(buffer, key, value, index);\n            }\n            else if (value instanceof Date || (0, utils_1.isDate)(value)) {\n                index = serializeDate(buffer, key, value, index);\n            }\n            else if (value === undefined) {\n                if (ignoreUndefined === false)\n                    index = serializeNull(buffer, key, value, index);\n            }\n            else if (value === null) {\n                index = serializeNull(buffer, key, value, index);\n            }\n            else if (value['_bsontype'] === 'ObjectId' || value['_bsontype'] === 'ObjectID') {\n                index = serializeObjectId(buffer, key, value, index);\n            }\n            else if ((0, utils_1.isUint8Array)(value)) {\n                index = serializeBuffer(buffer, key, value, index);\n            }\n            else if (value instanceof RegExp || (0, utils_1.isRegExp)(value)) {\n                index = serializeRegExp(buffer, key, value, index);\n            }\n            else if (type === 'object' && value['_bsontype'] == null) {\n                index = serializeObject(buffer, key, value, index, checkKeys, depth, serializeFunctions, ignoreUndefined, false, path);\n            }\n            else if (type === 'object' && value['_bsontype'] === 'Decimal128') {\n                index = serializeDecimal128(buffer, key, value, index);\n            }\n            else if (value['_bsontype'] === 'Long' || value['_bsontype'] === 'Timestamp') {\n                index = serializeLong(buffer, key, value, index);\n            }\n            else if (value['_bsontype'] === 'Double') {\n                index = serializeDouble(buffer, key, value, index);\n            }\n            else if (value['_bsontype'] === 'Code') {\n                index = serializeCode(buffer, key, value, index, checkKeys, depth, serializeFunctions, ignoreUndefined);\n            }\n            else if (typeof value === 'function' && serializeFunctions) {\n                index = serializeFunction(buffer, key, value, index, checkKeys, depth, serializeFunctions);\n            }\n            else if (value['_bsontype'] === 'Binary') {\n                index = serializeBinary(buffer, key, value, index);\n            }\n            else if (value['_bsontype'] === 'Symbol') {\n                index = serializeSymbol(buffer, key, value, index);\n            }\n            else if (value['_bsontype'] === 'DBRef') {\n                index = serializeDBRef(buffer, key, value, index, depth, serializeFunctions);\n            }\n            else if (value['_bsontype'] === 'BSONRegExp') {\n                index = serializeBSONRegExp(buffer, key, value, index);\n            }\n            else if (value['_bsontype'] === 'Int32') {\n                index = serializeInt32(buffer, key, value, index);\n            }\n            else if (value['_bsontype'] === 'MinKey' || value['_bsontype'] === 'MaxKey') {\n                index = serializeMinMax(buffer, key, value, index);\n            }\n            else if (typeof value['_bsontype'] !== 'undefined') {\n                throw new error_1.BSONTypeError(\"Unrecognized or invalid _bsontype: \".concat(String(value['_bsontype'])));\n            }\n        }\n    }\n    // Remove the path\n    path.pop();\n    // Final padding byte for object\n    buffer[index++] = 0x00;\n    // Final size\n    var size = index - startingIndex;\n    // Write the size of the object\n    buffer[startingIndex++] = size & 0xff;\n    buffer[startingIndex++] = (size >> 8) & 0xff;\n    buffer[startingIndex++] = (size >> 16) & 0xff;\n    buffer[startingIndex++] = (size >> 24) & 0xff;\n    return index;\n}\nexports.serializeInto = serializeInto;\n//# sourceMappingURL=serializer.js.map"]}