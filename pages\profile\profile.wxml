<view class="profile-container">
  <view class="custom-nav-bar" style="padding-top: {{menuTop}}px; height: {{menuHeight}}px;">
      <image class="back-icon" src="/images/back.png" mode="aspectFit" bindtap="onBack" />
      <view class="nav-title">我的</view>
  </view>
  <!-- 用户信息区域 -->
  <view class="{{vipStatus === 'active'? 'profile-vip-content' : 'profile-content'}}"  bindtap="toProfile">
      <view class="profile-header">
        <view class="custom-nav-bar-2">
            <button class="avatar-wrapper" open-type="chooseAvatar" bindchooseavatar="onChooseAvatar">
              <image src="{{babyInfo.avatarUrl || '/images/default_avatar.png'}}" mode="aspectFill" />
            </button>
            <view class="nickname-wrapper">
              <view class="nickname-input" bindtap="onNicknameChange">{{babyInfo.nickname}}</view>
              <image class="arrow" src="/images/arrow_right_white.png" mode="aspectFit" />
            </view>
        </view>
        <view class="membership-row">
          <view class="vip-expired">
            <text class="vip-text-expired" wx:if="{{vipStatus === 'never'}}">您还不是会员！请立即开通会员！</text>
            <text class="vip-text-expired" wx:elif="{{vipStatus === 'expired'}}">您当前会员已到期：{{vipExpireDate}}。</text>
            <text class="vip-text" wx:elif="{{vipStatus === 'active'}}">您是VIP会员，有效期至: {{vipExpireDate}}</text>
          </view>
          <view class="vip-btn" catchtap="vipBuy" data-type="vip">
            <text>{{buttonText}}</text>
          </view>
        </view>
      </view>
  </view>

  <!-- 宝宝信息区域 -->
  <view class="baby-info">
    <view class="section-title">宝宝信息</view>
    <view class="info-list">
      <view class="info-item" bindtap="onEditBabyInfo" data-type="gender">
        <text class="label">性别</text>
        <view class="value-wrapper">
          <text class="value">{{babyInfo.gender === 'boy' ? '男宝宝' : '女宝宝'}}</text>
          <image class="arrow" src="/images/arrow_right.png" mode="aspectFit" />
        </view>
      </view>
      <view class="info-item">
        <text class="label">生日</text>
        <picker mode="date" value="{{babyInfo.birthday}}" start="2000-01-01" end="{{today}}" bindchange="onBirthdayChange">
          <view class="value-wrapper">
            <text class="value">{{babyInfo.birthday || '未设置'}}</text>
            <image class="arrow" src="/images/arrow_right.png" mode="aspectFit" />
          </view>
        </picker>
      </view>
      <view class="info-item" bindtap="onEditBabyInfo" data-type="voice">
        <text class="label">朗读声音</text>
        <view class="value-wrapper">
          <text class="value">{{voiceTypes[babyInfo.voiceType] || '未设置'}}</text>
          <image class="arrow" src="/images/arrow_right.png" mode="aspectFit" />
        </view>
      </view>
    </view>
  </view>

  <!-- 其他功能区域 -->
  <view class="other-functions">
    <view class="info-list">
      <view class="info-item" bindtap="onAbout">
        <text class="label">联系我们</text>
        <image class="arrow" src="/images/arrow_right.png" mode="aspectFit" />
      </view>
    </view>
  </view>

  <!-- 退出登录按钮 -->
  <view class="logout-container">
    <button class="logout-btn" bindtap="onLogout">退出登录</button>
  </view>

  <!-- 版本信息 -->
  <view class="version-info">
    <text>V 1.0.0</text>
    <text>沪ICP备2025127586号-1X</text>
  </view>
</view>