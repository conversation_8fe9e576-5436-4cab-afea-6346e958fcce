// 云函数入口文件
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

// 云函数入口函数
exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext()
  const db = cloud.database()
  
  // 获取要创建的集合名称
  const { collectionName } = event
  
  if (!collectionName) {
    return {
      success: false,
      message: '缺少集合名称参数'
    }
  }
  
  try {
    // 检查集合是否已存在
    const collections = await db.collections()
    const collectionNames = collections.map(collection => collection.name)
    
    if (collectionNames.includes(collectionName)) {
      return {
        success: true,
        message: `集合 ${collectionName} 已存在`,
        existed: true
      }
    }
    
    // 创建集合
    await db.createCollection(collectionName)
    
    // 如果是userUsage集合，创建索引
    if (collectionName === 'userUsage') {
      try {
        await db.collection('userUsage').createIndexes([
          {
            key: {
              _openid: 1
            },
            name: 'openid_index'
          }
        ])
      } catch (indexErr) {
        console.error('创建索引失败', indexErr)
      }
    }
    
    return {
      success: true,
      message: `成功创建集合 ${collectionName}`,
      existed: false
    }
  } catch (error) {
    console.error(`创建集合 ${collectionName} 失败:`, error)
    return {
      success: false,
      error: error.message || error
    }
  }
}