const cloud = require('wx-server-sdk');
cloud.init({ env: cloud.DYNAMIC_CURRENT_ENV });
const db = cloud.database();
const crypto = require('crypto');

// 验证支付签名
const verifySign = (data, sign, key) => {
  const message = Object.keys(data)
    .filter(key => key !== 'sign' && data[key] !== undefined && data[key] !== '')
    .sort()
    .map(key => `${key}=${data[key]}`)
    .join('&') + `&key=${key}`;

  const generatedSign = crypto
    .createHash('md5')
    .update(message)
    .digest('hex')
    .toUpperCase();

  return generatedSign === sign;
};

// 计算新的到期时间（限制最大不超过10年）
const calculateNewExpireDate = (baseTime, days) => {
  const maxDays = 365 * 10; // 最大10年
  const actualDays = Math.min(days, maxDays);
  return new Date(baseTime + actualDays * 24 * 60 * 60 * 1000);
};

exports.main = async (event, context) => {
  try {
    const { returnCode, resultCode, outTradeNo, transactionId, sign } = event;

    // 验证支付签名
    if (!verifySign(event, sign, process.env.PAYMENT_KEY)) {
      console.error('支付签名验证失败');
      return { return_code: 'FAIL', return_msg: '签名验证失败' };
    }

    if (returnCode !== 'SUCCESS' || resultCode !== 'SUCCESS') {
      return { return_code: 'FAIL', return_msg: '支付失败' };
    }

    const now = Date.now();  // ✅ 统一使用同一个时间戳
    const orderRes = await db.collection('orders').where({ orderId: outTradeNo }).get();

    if (orderRes.data.length === 0) {
      return { return_code: 'FAIL', return_msg: '订单不存在' };
    }

    const order = orderRes.data[0];

    // 更新订单状态
    await db.collection('orders').doc(order._id).update({
      data: {
        status: 'SUCCESS',
        transactionId,
        payTime: db.serverDate()
      }
    });

    const userRes = await db.collection('users').where({ _openid: order._openid }).get();

    let newExpire;
    if (userRes.data.length === 0) {
      // 用户不存在，直接设置新过期时间
      newExpire = new Date(now + order.duration * 24 * 60 * 60 * 1000);
      await db.collection('users').add({
        data: {
          _openid: order._openid,
          vipExpireDate: newExpire,
          createTime: db.serverDate()
        }
      });
    } else {
      const user = userRes.data[0];
      const oldExpire = new Date(user.vipExpireDate);
      const baseTime = (user.vipExpireDate && oldExpire > new Date(now)) ? oldExpire.getTime() : now;
      newExpire = calculateNewExpireDate(baseTime, order.duration);

      await db.collection('users').doc(user._id).update({
        data: {
          vipExpireDate: newExpire
        }
      });
    }

    return { return_code: 'SUCCESS', return_msg: 'OK' };
  } catch (error) {
    console.error('支付回调处理失败', error);
    return { return_code: 'FAIL', return_msg: '服务器错误' };
  }
};
