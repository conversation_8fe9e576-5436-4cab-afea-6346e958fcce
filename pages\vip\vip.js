Page({
  data: {
    menuTop: 0,
    menuHeight: 0,
    selectedPlan: 'yearly',  // 默认选中年度套餐
    plans: {
      yearly: {
        price: 98.00,
        duration: '1年',
        dailyPrice: 0.26,
        days: 365
      },
      monthly: {
        price: 16.00,
        duration: '1个月',
        dailyPrice: 0.53,
        days: 30
      }
    },
    vipStatus: '',  // 会员状态：never(从未开通), expired(已过期), active(有效)
    vipExpireDate: null,  // 会员过期时间
    buttonText: '立即开通',  // 按钮文案
    isIOS: false // 是否是iOS系统
  },

  onLoad() {
    const app = getApp();
    this.setData({
      menuTop: app.globalData.menuTop,
      menuHeight: app.globalData.menuHeight,
    });

    // 获取系统信息
    wx.getSystemInfo({
      success: (res) => {
        this.setData({
          isIOS: res.platform === 'ios'
        });
      },
    });

    this.getUserVipInfo();
  },

  // 获取用户会员信息
  getUserVipInfo() {
    // 获取用户openid
    const openid = wx.getStorageSync('openid');
    if (!openid) {
      console.error('未找到用户openid');
      this.setData({
        vipStatus: 'never',
        buttonText: '立即开通'
      });
      return;
    }

    // 调用云函数获取用户信息，如果用户不存在会自动创建
    wx.cloud.callFunction({
      name: 'getUserInfo'
    }).then(res => {
      if (res.result.code !== 0 || !res.result.data) {
        console.log('获取用户信息失败:', res.result.message);
        this.setData({
          vipStatus: 'never',
          buttonText: '立即开通'
        });
        return;
      }

      const user = res.result.data;
      // 检查user对象是否存在
      if (!user) {
        console.error('用户数据为空');
        this.setData({
          vipStatus: 'never',
          buttonText: '立即开通'
        });
        return;
      }

      // 检查vipExpireDate属性
      if (!user.vipExpireDate) {
        // vipExpireDate为空，说明从未开通过会员
        this.setData({
          vipStatus: 'never',
          buttonText: '立即开通'
        });
      } else {
        const currentDate = new Date();
        const expireDate = new Date(user.vipExpireDate);
        
        // 将当前日期和到期日期都设置为当天的0点，只比较日期而不比较具体时间
        currentDate.setHours(0, 0, 0, 0);
        expireDate.setHours(0, 0, 0, 0);
        
        if (expireDate >= currentDate) {
          // 会员未过期（包括当天到期的情况）
          this.setData({
            vipStatus: 'active',
            vipExpireDate: this.formatDate(expireDate),
            buttonText: '立即续费'
          });
        } else {
          // 会员已过期
          this.setData({
            vipStatus: 'expired',
            vipExpireDate: this.formatDate(expireDate),
            buttonText: '立即续费'
          });
        }
      }
    }).catch(err => {
      console.error('获取会员信息失败', err);
      // 发生错误时设置默认状态
      this.setData({
        vipStatus: 'never',
        buttonText: '立即开通'
      });
    });
  },

  // 格式化日期
  formatDate(date) {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  },

  // 返回上一页
  onBack() {
    wx.navigateBack({
      delta: 1
    });
  },

  // 选择套餐
  selectPlan(e) {
    const plan = e.currentTarget.dataset.plan;
    this.setData({
      selectedPlan: plan
    });
  },

  // 处理支付
  handlePayment() {
    const selectedPlan = this.data.plans[this.data.selectedPlan];
    
    // 调用支付接口
    wx.showLoading({
      title: '正在创建订单'
    });

    // 调用云函数创建订单
    wx.cloud.callFunction({
      name: 'createOrder',
      data: {
        amount: selectedPlan.price * 100, // 转换为分
        duration: selectedPlan.days
      }
    }).then(res => {
      wx.hideLoading();
      
      if (res.result.code !== 0) {
        // 订单创建失败
        wx.showToast({
          title: res.result.message || '创建订单失败',
          icon: 'none'
        });
        return;
      }
      
      // 调起微信支付
      wx.requestPayment({
        timeStamp: res.result.payment.timeStamp,
        nonceStr: res.result.payment.nonceStr,
        package: res.result.payment.package,
        signType: res.result.payment.signType,
        paySign: res.result.payment.paySign,
        success: () => {
          // 支付成功后更新会员有效期
          this.updateMembership(selectedPlan.days);
        },
        fail: (err) => {
          console.error('支付失败', err);
          if (err.errMsg === 'requestPayment:fail cancel') {
            wx.showToast({
              title: '支付已取消',
              icon: 'none'
            });
          } else {
            wx.showToast({
              title: '支付失败',
              icon: 'none'
            });
          }
        },
        complete: () => {
          console.log('支付流程结束');
        }
      });
    }).catch(err => {
      console.error('创建订单失败', err);
      wx.hideLoading();
      wx.showToast({
        title: '创建订单失败',
        icon: 'none'
      });
    });
  },

  // 更新会员有效期
  updateMembership(days) {
    wx.showLoading({
      title: '正在更新会员信息'
    });
    
    wx.cloud.callFunction({
      name: 'updateMembership',
      data: { days }
    }).then(res => {
      wx.hideLoading();
      
      if (res.result && res.result.code === 0) {
        // 更新本地会员状态
        this.getUserVipInfo();
        
        wx.showToast({
          title: '开通成功',
          icon: 'success'
        });
        
        // // 延迟返回上一页
        // setTimeout(() => {
        //   wx.navigateBack({
        //     delta: 1
        //   });
        // }, 1500);
      } else {
        wx.showToast({
          title: res.result?.message || '更新会员信息失败',
          icon: 'none'
        });
      }
    }).catch(err => {
      console.error('更新会员信息失败', err);
      wx.hideLoading();
      wx.showToast({
        title: '更新会员信息失败',
        icon: 'none'
      });
    });
  }
})