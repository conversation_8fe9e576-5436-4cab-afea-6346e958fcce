// pages/babyinfo/voiceselect.js
Page({
  data: {
    menuTop: 0,
    menuHeight: 0,
    selectedVoice: 'mom',
    momRecorded: false,
    dadRecorded: false,
    isRecording: false,
    recordingText: '亲爱的宝贝！今天我来给你讲一个有趣的故事。从前，有一只小狐狸发现了一个神奇的蘑菇，蘑菇告诉它："我是魔法蘑菇，来一起冒险吧！"小狐狸勇敢地说："好呀！"',
    tempFilePath: {
      mom: '',
      dad: ''
    },
    cloudVoiceUrl: { // 新增：用于存储云存储的fileID
      mom: '',
      dad: ''
    },
    recorderManager: null,
    audioContext: null,
    isPlaying: false,
    currentPlayingVoice: '',
    hasRecordAuth: false,
    phoneNumber: '',
    fromPage: '', // 添加场景标识，用于区分来源页面
    initVoice: '' // 新增参数接收初始选择
  },

  onLoad(options) {
    const app = getApp();
    const babyInfo = wx.getStorageSync('babyInfo'); // Read babyInfo early

    let initialSelectedVoice = 'mom'; // Default value

    // Prioritize saved voiceType from babyInfo
    if (babyInfo && babyInfo.voiceType) {
      initialSelectedVoice = babyInfo.voiceType;
    } else if (options.selectedVoice) {
      // Fallback to options.selectedVoice if available
      switch (options.selectedVoice) {
        case '妈妈':
          initialSelectedVoice = 'mom';
          break;
        case '爸爸':
          initialSelectedVoice = 'dad';
          break;
        case '叔叔':
          initialSelectedVoice = 'male';
          break;
        case '阿姨':
          initialSelectedVoice = 'female';
          break;
        default:
          initialSelectedVoice = 'mom'; // Default if options.selectedVoice is unexpected
      }
    }

    this.setData({
      menuTop: app.globalData.menuTop,
      menuHeight: app.globalData.menuHeight,
      fromPage: options.from || '', // 如果没有传入来源页面，默认为首次设置
      initVoice: initialSelectedVoice, // Use the determined initial voice
      selectedVoice: initialSelectedVoice // Use the determined initial voice
    });

    console.log('页面加载，来源:', options.from, '初始声音选择:', initialSelectedVoice);
    // 初始化录音管理器
    this.recorderManager = wx.getRecorderManager();
    this.audioContext = wx.createInnerAudioContext(); // Create once

    // 页面加载时请求录音权限
    this.requestRecordAuth();

    // 获取宝宝信息，检查是否有已保存的录音
    // 注意：这里只将云文件ID存储到 cloudVoiceUrl，tempFilePath 仍为空，待播放时下载
    if (babyInfo) {
      if (babyInfo.momVoiceUrl) {
        this.setData({
          'cloudVoiceUrl.mom': babyInfo.momVoiceUrl,
          momRecorded: true
        });
      }
      if (babyInfo.dadVoiceUrl) {
        this.setData({
          'cloudVoiceUrl.dad': babyInfo.dadVoiceUrl,
          dadRecorded: true
        });
      }
    }

    // 监听录音结束事件
    this.recorderManager.onStop((res) => {
      const { tempFilePath } = res;
      const currentVoice = this.data.currentRecordingVoice;
      
      console.log('录音结束，文件路径:', tempFilePath, '当前录音类型:', currentVoice);
      
      if (tempFilePath) {
        // 确保路径正确保存
        this.setData({
          [`tempFilePath.${currentVoice}`]: tempFilePath,
          [`${currentVoice}Recorded`]: true,
          isRecording: false
        });
         
        // 确认数据已更新
        console.log('录音已保存:', this.data.tempFilePath[currentVoice]);
      } else {
        console.error('录音结束但未生成文件路径:', res); // Added log
        wx.showToast({
          title: '录音保存失败',
          icon: 'none'
        });
        this.setData({
          isRecording: false
        });
      }
    });

    // 监听录音错误事件
    this.recorderManager.onError((res) => {
      console.error('录音失败：', res);
      // Check if the error is due to an ongoing recording being stopped by navigation
      if (res.errMsg && res.errMsg.includes('operateRecorder:fail recorder has been stop')) {
        console.warn('录音器已停止，可能是页面卸载导致，不显示错误提示。');
        // Do not show toast if it's a benign stop error
      } else {
        wx.showToast({
          title: '录音失败',
          icon: 'none'
        });
      }
      this.setData({
        isRecording: false
      });
    });
    
    // Unified audioContext event listeners
    this.audioContext.onEnded(() => {
      console.log('音频播放结束');
      this.setData({
        isPlaying: false,
        currentPlayingVoice: ''
      });
    });

    this.audioContext.onCanplay(() => {
      console.log('音频已准备就绪');
    });

    this.audioContext.onError((err) => {
      console.error('音频播放错误:', err);
      wx.showToast({
        title: '音频播放失败',
        icon: 'none'
      });
      this.setData({
        isPlaying: false,
        currentPlayingVoice: ''
      });
    });

    this.audioContext.onPlay(() => {
      console.log('音频开始播放');
    });
  },
  // 新增：带重试的上传API方法
  // 修复后的带重试的上传API方法
async retryUploadVoiceToAPI(filePath, voiceType) {
  let cloudFilePath = filePath;
  
  // 如果是临时文件路径，先上传到云存储
  if (filePath.startsWith('wxfile://') || filePath.startsWith('http://tmp/')) {
    console.log('检测到临时文件路径，先上传到云存储');
    try {
      cloudFilePath = await this.uploadVoiceFile(filePath, voiceType);
      console.log('临时文件已上传到云存储，fileID:', cloudFilePath);
    } catch (uploadError) {
      console.error('上传临时文件到云存储失败:', uploadError);
      throw new Error('上传录音文件失败，请重试');
    }
  }
  
  // 重试上传到API
  while (true) {
    try {
      const uri = await this.uploadVoiceToAPIWithCloudPath(cloudFilePath, voiceType);
      if (uri) return uri;
      
      const res = await wx.showModal({
        title: '上传失败',
        content: '上传失败，请点"确定"按钮，重试！',
        showCancel: false,
        confirmText: '确定'
      });
    } catch (e) {
      console.error('上传到API失败:', e);
      const res = await wx.showModal({
        title: '上传失败',
        content: '上传失败，请点"确定"按钮，重试！',
        showCancel: false,
        confirmText: '确定'
      });
    }
  }
},

// 新增：使用云存储路径上传到API的方法
async uploadVoiceToAPIWithCloudPath(cloudFilePath, voiceType) {
  try {
    // 获取用户openid作为customName
    const openid = wx.getStorageSync('openid');
    if (!openid) {
      throw new Error('未找到用户openid');
    }
    
    // 获取录音对应的文本内容
    const voiceText = this.data.recordingText;
    
    // 调用云函数上传录音文件到API
    const result = await wx.cloud.callFunction({
      name: 'uploadVoiceToAPI',
      data: {
        filePath: cloudFilePath, // 使用云存储路径
        voiceType: voiceType,
        openid: openid,
        text: voiceText,
        model: 'FunAudioLLM/CosyVoice2-0.5B'
      }
    });
    
    if (!result || !result.result || !result.result.success) {
      const errorMsg = result?.result?.error || '录音上传到API失败';
      throw new Error(errorMsg);
    }
    
    return result.result.uri;
  } catch (error) {
    console.error('上传录音到API失败：', error);
    throw error;
  }
},
  onUnload() {
    console.log('voicesetting 页面卸载');
    // 页面卸载时释放资源
    if (this.audioContext) {
      this.audioContext.stop();
      this.audioContext.destroy();
      console.log('audioContext 已停止并销毁');
    }
    if (this.recorderManager && this.data.isRecording) { // Only stop if recording is active
      this.recorderManager.stop();
      console.log('recorderManager 已停止');
    } else if (this.recorderManager) {
      console.log('recorderManager 存在但未在录音状态，不执行停止操作。');
    }
  },

  loadLocalData() {
    const babyInfo = wx.getStorageSync('babyInfo') || {};
    this.setData({
      momRecorded: !!babyInfo.momUri,
      dadRecorded: !!babyInfo.dadUri,
      'tempFilePath.mom': babyInfo.momVoiceUrl || '',
      'tempFilePath.dad': babyInfo.dadVoiceUrl || ''
    });
  },

  // 选择声音
  selectVoice(e) {
    const voice = e.currentTarget.dataset.voice;
    this.setData({
      selectedVoice: voice
    });
  },

  // 返回上一页
  onBack() {
    wx.navigateBack({
      delta: 1
    });
  },

  // 请求录音权限
  requestRecordAuth() {
    wx.authorize({
      scope: 'scope.record',
      success: () => {
        // 用户已经同意小程序使用录音功能
        console.log('已获取录音权限');
        this.setData({
          hasRecordAuth: true
        });
      },
      fail: () => {
        console.log('未获取录音权限');
        this.setData({
          hasRecordAuth: false
        });
      }
    });
  },
  
  // 检查并请求录音权限
  checkAndRequestAuth() {
    return new Promise((resolve) => {
      wx.getSetting({
        success: (res) => {
          if (res.authSetting['scope.record']) {
            // 用户已授权
            this.setData({ hasRecordAuth: true });
            resolve(true);
          } else if (res.authSetting['scope.record'] === false) {
            // 用户已拒绝授权，引导用户到设置页面开启
            wx.showModal({
              title: '提示',
              content: '您已拒绝录音授权，将无法使用录音功能。请点击“去设置”开启权限。' + 
                       '如果您想再次录音，请在设置中开启麦克风权限。' + 
                       '否则，您将无法使用录音功能。',
              confirmText: '去设置',
              cancelText: '取消',
              success: (modalRes) => {
                if (modalRes.confirm) {
                  wx.openSetting({
                    success: (settingRes) => {
                      if (settingRes.authSetting['scope.record']) {
                        this.setData({ hasRecordAuth: true });
                        resolve(true);
                      } else {
                        wx.showToast({
                          title: '授权失败',
                          icon: 'none'
                        });
                        resolve(false);
                      }
                    },
                    fail: () => {
                      wx.showToast({
                        title: '打开设置失败',
                        icon: 'none'
                      });
                      resolve(false);
                    }
                  });
                } else {
                  wx.showToast({
                    title: '已取消授权',
                    icon: 'none'
                  });
                  resolve(false);
                }
              }
            });
          } else {
            // 首次请求授权
            wx.authorize({
              scope: 'scope.record',
              success: () => {
                this.setData({ hasRecordAuth: true });
                resolve(true);
              },
              fail: () => {
                wx.showToast({
                  title: '授权失败',
                  icon: 'none'
                });
                resolve(false);
              }
            });
          }
        },
        fail: () => {
          wx.showToast({
            title: '获取设置失败',
            icon: 'none'
          });
          resolve(false);
        }
      });
    });
  },

  // 开始录音
  async startRecording(e) {
    const voice = e.currentTarget.dataset.voice;
    
    // 检查录音权限
    const hasAuth = await this.checkAndRequestAuth();
    if (!hasAuth) {
      return;
    }
    
    // 如果正在播放音频，先停止
    if (this.data.isPlaying) {
      this.audioContext.stop();
      this.setData({
        isPlaying: false,
        currentPlayingVoice: ''
      });
    }
    
    this.setData({
      currentRecordingVoice: voice,
      isRecording: true
    });

    this.recorderManager.start({
      duration: 60000, // 最长录音时间，单位ms
      sampleRate: 44100,
      numberOfChannels: 1,
      encodeBitRate: 192000,
      format: 'mp3'
    });
  },

  // 停止录音
  stopRecording() {
    if (this.data.isRecording) {
      this.recorderManager.stop();
    }
  },

  // 播放录音
  playRecording(e) {
    const voice = e.currentTarget.dataset.voice;
    const filePath = this.data.tempFilePath[voice];
    
    // 添加调试日志
    console.log('播放录音:', voice, '文件路径:', filePath);
  
    // 如果正在播放，先停止
    if (this.data.isPlaying) {
      this.audioContext.stop();
      
      // 如果是同一个录音，则停止播放并返回
      if (this.data.currentPlayingVoice === voice) {
        this.setData({
          isPlaying: false,
          currentPlayingVoice: ''
        });
        return;
      }
    }

    // 获取宝宝信息
    const babyInfo = wx.getStorageSync('babyInfo');
    let audioUrl = filePath;

    // 如果本地没有录音文件，但有云存储的录音URL
    if (!filePath && babyInfo && babyInfo.voiceUrl && babyInfo.voice === voice) {
      // 从云存储下载录音文件
      wx.cloud.downloadFile({
        fileID: babyInfo.voiceUrl,
        success: res => {
          const tempFilePath = res.tempFilePath;
          // 更新本地缓存
          this.setData({
            [`tempFilePath.${voice}`]: tempFilePath,
            [`${voice}Recorded`]: true
          });
          // 播放下载的录音
          this.playAudioFile(tempFilePath, voice);
        },
        fail: err => {
          console.error('下载录音文件失败：', err);
          wx.showToast({
            title: '播放失败',
            icon: 'none'
          });
        }
      });
      return;
    }

    // 播放本地录音文件
    if (audioUrl) {
      // 确保文件路径有效
      console.log('准备播放本地录音:', audioUrl);
      this.playAudioFile(audioUrl, voice);
    } else {
      wx.showToast({
        title: '没有录音文件',
        icon: 'none'
      });
    }
  },

  // 播放音频文件
  playAudioFile(audioUrl, voice) {
    try {
      console.log('播放音频文件，路径:', audioUrl);
      
      // 设置音频属性
      this.audioContext.obeyMuteSwitch = false; // 忽略静音开关
      this.audioContext.autoplay = false; // 不自动播放，手动控制
      
      // 设置音频源
      this.audioContext.src = audioUrl;
      
      // 先设置状态
      this.setData({
        isPlaying: true,
        currentPlayingVoice: voice
      });
      
      // 延迟播放，确保音频源设置完成
      setTimeout(() => {
        this.audioContext.play();
        console.log('尝试播放音频');
      }, 200);
    } catch (error) {
      console.error('播放录音失败：', error);
      wx.showToast({
        title: '播放录音失败',
        icon: 'none'
      });
    }
  },

  // 播放系统默认声音
  playSystemVoice(e) {
    const voice = e.currentTarget.dataset.voice;
    let audioSrc = '';
    
    // 如果正在播放，先停止
    if (this.data.isPlaying) {
      this.audioContext.stop();
      
      // 如果是同一个声音，则停止播放并返回
      if (this.data.currentPlayingVoice === voice) {
        this.setData({
          isPlaying: false,
          currentPlayingVoice: ''
        });
        return;
      }
    }
    
    // 设置音频源
    if (voice === 'male') {
      audioSrc = 'cloud://aistory-4gk5r2e795f4c526.6169-aistory-4gk5r2e795f4c526-1304780203/voices/moren/male_sample.wav';
    } else if (voice === 'female') {
      audioSrc = 'cloud://aistory-4gk5r2e795f4c526.6169-aistory-4gk5r2e795f4c526-1304780203/voices/moren/female_sample.wav';
    } else {
      return;
    }
    
    try {
      // 设置音频属性
      this.audioContext.obeyMuteSwitch = false; // 忽略静音开关
      
      // 设置音频源并播放
      this.audioContext.src = audioSrc;
      
      // 先设置状态
      this.setData({
        isPlaying: true,
        currentPlayingVoice: voice
      });
      
      // 延迟播放，确保音频源设置完成
      setTimeout(() => {
        this.audioContext.play();
        console.log('尝试播放系统声音');
      }, 300);
    } catch (error) {
      console.error('播放系统声音失败：', error);
      wx.showToast({
        title: '播放系统声音失败',
        icon: 'none'
      });
      this.setData({
        isPlaying: false,
        currentPlayingVoice: ''
      });
    }
  },

  // 上传录音文件到云存储
  async uploadVoiceFile(filePath, voiceType) {
    if (!filePath) {
      throw new Error('录音文件不存在');
    }

    try {
      wx.showLoading({
        title: '上传录音中...',
        mask: true
      });

      const result = await wx.cloud.uploadFile({
        cloudPath: `voices/${voiceType}/${Date.now()}-${Math.random().toString(36).substr(2)}.mp3`,
        filePath: filePath
      });

      wx.hideLoading();

      if (!result || !result.fileID) {
        throw new Error('录音上传失败');
      }

      return result.fileID;
    } catch (error) {
      wx.hideLoading();
      console.error('上传录音失败：', error);
      throw new Error('录音上传失败，请重试');
    }
  },
  
  // 上传录音文件到API
  async uploadVoiceToAPI(filePath, voiceType) {
    if (!filePath) {
      throw new Error('录音文件不存在');
    }
  
    try {
      wx.showLoading({
        title: '上传录音中...',
        mask: true
      });
      
      // 获取用户openid作为customName
      const openid = wx.getStorageSync('openid');
      if (!openid) {
        throw new Error('未找到用户openid');
      }
      
      // 获取录音对应的文本内容
      const voiceText = this.data.recordingText;
      
      let cloudFilePath = filePath;
      
      // 检查是否为临时文件路径，如果是则先上传到云存储
      if (filePath.startsWith('wxfile://') || filePath.startsWith('http://tmp/')) {
        console.log('检测到临时文件路径，先上传到云存储');
        try {
          // 使用已有的上传函数将文件上传到云存储
          cloudFilePath = await this.uploadVoiceFile(filePath, voiceType);
          console.log('临时文件已上传到云存储，fileID:', cloudFilePath);
        } catch (uploadError) {
          console.error('上传临时文件到云存储失败:', uploadError);
          throw new Error('上传录音文件失败，请重试');
        }
      }
      
      // 调用云函数上传录音文件到API
      const result = await wx.cloud.callFunction({
        name: 'uploadVoiceToAPI',
        data: {
          filePath: cloudFilePath, // 使用云存储路径
          voiceType: voiceType,
          openid: openid,
          text: voiceText,
          model: 'FunAudioLLM/CosyVoice2-0.5B'
        }
      });
      
      wx.hideLoading();
      
      if (!result || !result.result || !result.result.success) {
        const errorMsg = result?.result?.error || '录音上传到API失败';
        throw new Error(errorMsg);
      }
      
      return result.result.uri;
    } catch (error) {
      wx.hideLoading();
      console.error('上传录音到API失败：', error);
      throw new Error('录音上传到API失败，请重试');
    }
  },

  // 确认按钮点击事件
  // 修复后的确认按钮点击事件
async onConfirm() {
  try {
    // 检查是否选择了声音
    if (!this.data.selectedVoice) {
      wx.showToast({
        title: '请选择声音',
        icon: 'none'
      });
      return;
    }

    // 检查是否需要录音但未录音
    if ((this.data.selectedVoice === 'mom' || this.data.selectedVoice === 'dad') &&
        !this.data.tempFilePath[this.data.selectedVoice]) {
      wx.showToast({
        title: '请录音',
        icon: 'none'
      });
      return;
    }
    
    // 如果正在播放音频，先停止
    if (this.data.isPlaying && this.audioContext) {
      this.audioContext.stop();
      this.setData({
        isPlaying: false,
        currentPlayingVoice: ''
      });
    }

    // 显示加载提示
    wx.showLoading({
      title: '处理中...',
      mask: true
    });

    // 检查云开发环境是否初始化
    if (!wx.cloud) {
      throw new Error('请使用 2.2.3 或以上的基础库以使用云能力');
    }

    // 获取宝宝信息
    const babyInfo = wx.getStorageSync('babyInfo');
    if (!babyInfo) {
      throw new Error('未找到宝宝信息');
    }

    // 验证babyInfo对象
    if (!babyInfo || !babyInfo.nickname || !babyInfo.gender || !babyInfo.birthday) {
      throw new Error('请完善宝宝信息');
    }

    // 设置声音信息
    let voiceUrl = '';
    const voiceType = this.data.selectedVoice;

    // 获取现有的录音信息
    let momVoiceUrl = babyInfo.momVoiceUrl || '';
    let dadVoiceUrl = babyInfo.dadVoiceUrl || '';
    let momUri = babyInfo.momUri || '';
    let dadUri = babyInfo.dadUri || '';
    
    // 上传新录制的妈妈录音
    if (this.data.tempFilePath.mom && (!babyInfo.momVoiceUrl || this.data.tempFilePath.mom !== babyInfo.momVoiceUrl)) {
      // 先上传到云存储
      momVoiceUrl = await this.uploadVoiceFile(this.data.tempFilePath.mom, 'mom');
      if (!momVoiceUrl) {
        throw new Error('妈妈的录音文件上传失败');
      }
      
      // 再上传到API
      try {
        momUri = await this.retryUploadVoiceToAPI(this.data.tempFilePath.mom, 'mom');
        console.log('妈妈录音上传到API成功，uri:', momUri);
      } catch (apiError) {
        console.error('妈妈录音上传到API失败:', apiError);
        // 继续执行，不中断流程
      }
    }
    
    // 上传新录制的爸爸录音
    if (this.data.tempFilePath.dad && (!babyInfo.dadVoiceUrl || this.data.tempFilePath.dad !== babyInfo.dadVoiceUrl)) {
      // 先上传到云存储
      dadVoiceUrl = await this.uploadVoiceFile(this.data.tempFilePath.dad, 'dad');
      if (!dadVoiceUrl) {
        throw new Error('爸爸的录音文件上传失败');
      }
      
      // 再上传到API
      try {
        dadUri = await this.retryUploadVoiceToAPI(this.data.tempFilePath.dad, 'dad');
        console.log('爸爸录音上传到API成功，uri:', dadUri);
      } catch (apiError) {
        console.error('爸爸录音上传到API失败:', apiError);
        // 继续执行，不中断流程
      }
    }

    // 调用云函数保存宝宝信息和声音选择
    const resultSave = await wx.cloud.callFunction({
      name: 'saveBabyInfo',
      data: {
        nickname: babyInfo.nickname,
        gender: babyInfo.gender,
        birthday: babyInfo.birthday,
        avatarUrl: babyInfo.avatarUrl,
        voiceType: voiceType,
        voiceUrl: voiceUrl,
        momVoiceUrl: momVoiceUrl,
        dadVoiceUrl: dadVoiceUrl,
        momUri: momUri,
        dadUri: dadUri
      }
    });

    // 处理保存结果
    if (!resultSave || !resultSave.result) {
      throw new Error('保存失败，请稍后重试');
    }

    wx.hideLoading();
    
    // 保存voiceSetting标记到本地存储
    wx.setStorageSync('voiceSetting', true);

    // Update babyInfo.voiceType in local storage for all cases
    babyInfo.voiceType = voiceType;
    wx.setStorageSync('babyInfo', babyInfo);

    // 如果是从custom-story页面进入，需要返回并传递uri
    if (this.data.fromPage === 'custom-story') {
      const pages = getCurrentPages();
      const prevPage = pages[pages.length - 2];
      
      // 根据选择的声音类型，设置对应的voiceUri
      let voiceUri = '';
      if (this.data.selectedVoice === 'mom') {
        voiceUri = momUri;
        // 更新babyInfo中的momUri
        babyInfo.momUri = momUri;
      } else if (this.data.selectedVoice === 'dad') {
        voiceUri = dadUri;
        // 更新babyInfo中的dadUri
        babyInfo.dadUri = dadUri;
      }
      
      // 更新本地存储的babyInfo
      babyInfo.voiceType = this.data.selectedVoice; // Ensure voiceType is updated before saving
      wx.setStorageSync('babyInfo', babyInfo);
      
      // 设置上一页面的数据
      if (prevPage) {
        prevPage.setData({
          voiceUri: voiceUri,
          selectedVoice: this.data.selectedVoice === 'mom' ? '妈妈' : '爸爸' // 转换为中文，与custom-story页面匹配
        });
      }
      
      console.log('返回custom-story页面，传递voiceUri:', voiceUri);
      wx.navigateBack({ delta: 1 });
      return; // 直接返回，不显示额外提示
    }
    
    // 其他情况显示成功提示
    wx.showToast({
      title: this.data.fromPage === 'profile' ? '声音设置已更新' : '太棒了！宝贝信息已保存！',
      icon: 'success',
      duration: 2000
    });
    
    setTimeout(() => {
      // 从profile页面或custom-story页面进入时，返回上一页
      if (this.data.fromPage === 'profile' || this.data.fromPage === 'custom-story') {
        wx.navigateBack({ delta: 1 });
      } else {
        // 首次设置时，跳转到首页
        wx.setStorageSync('hasSelectedAge', true);
        wx.reLaunch({
          url: '/pages/home/<USER>'
        });
      }
    }, 2000);
  } catch (error) {
    wx.hideLoading();
    console.error('操作失败：', error);
    wx.showToast({
      title: error.message || '操作失败，请重试',
      icon: 'none',
      duration: 2000
    });
  }
},

  // 保存宝宝信息
  async saveBabyInfo(userInfo) {
    try {
      // 获取本地存储的宝宝信息
      const babyInfo = wx.getStorageSync('babyInfo');
      if (!babyInfo) {
        throw new Error('未找到宝宝信息');
      }

      // 验证babyInfo对象
      if (!babyInfo.nickname || !babyInfo.gender || !babyInfo.birthday) {
        throw new Error('请完善宝宝信息');
      }

      let voiceUrl = '';
      const voiceType = this.data.selectedVoice;
      
      // 获取现有的录音URL
      let momVoiceUrl = babyInfo.momVoiceUrl || '';
      let dadVoiceUrl = babyInfo.dadVoiceUrl || '';
      let momUri = babyInfo.momUri || '';
      let dadUri = babyInfo.dadUri || '';
      
      // 如果选择的是录音，则上传录音文件
      if ((voiceType === 'mom' || voiceType === 'dad') && this.data.tempFilePath[voiceType]) {
        try {
          // 上传到云存储
          if (voiceType === 'mom') {
            momVoiceUrl = await this.uploadVoiceFile(this.data.tempFilePath[voiceType], voiceType);
            voiceUrl = momVoiceUrl;
            
            // 上传到API
            try {
              momUri = await this.uploadVoiceToAPI(this.data.tempFilePath[voiceType], voiceType);
              console.log('妈妈录音上传到API成功，uri:', momUri);
            } catch (apiError) {
              console.error('妈妈录音上传到API失败:', apiError);
              // 继续执行，不中断流程
            }
          } else if (voiceType === 'dad') {
            dadVoiceUrl = await this.uploadVoiceFile(this.data.tempFilePath[voiceType], voiceType);
            voiceUrl = dadVoiceUrl;
            
            // 上传到API
            try {
              dadUri = await this.uploadVoiceToAPI(this.data.tempFilePath[voiceType], voiceType);
              console.log('爸爸录音上传到API成功，uri:', dadUri);
            } catch (apiError) {
              console.error('爸爸录音上传到API失败:', apiError);
              // 继续执行，不中断流程
            }
          }
        } catch (uploadError) {
          console.error('上传录音文件失败:', uploadError);
          throw new Error('上传录音文件失败，请重试');
        }
      }

      // 调用云函数保存宝宝信息和声音选择
      const result = await wx.cloud.callFunction({
        name: 'saveBabyInfo',
        data: {
          nickname: babyInfo.nickname,
          gender: babyInfo.gender,
          birthday: babyInfo.birthday,
          avatarUrl: babyInfo.avatarUrl,
          voiceType: voiceType,
          voiceUrl: voiceUrl,
          momVoiceUrl: momVoiceUrl,
          dadVoiceUrl: dadVoiceUrl,
          momUri: momUri,
          dadUri: dadUri
        }
      });

      if (result && result.result && result.result.success) {
        // 保存成功，跳转到首页
        wx.showToast({
          title: '太棒了！宝贝信息已保存！',
          icon: 'success',
          duration: 2000
        });
        
        // 延迟跳转，让用户看到提示
        setTimeout(() => {
          wx.navigateBack({ delta: 1 });
        }, 2000);
      } else {
        throw new Error(result.result?.error || '保存失败');
      }
    } catch (error) {
      console.error('保存失败：', error);
      wx.showToast({
        title: error.message || '保存失败，请重试',
        icon: 'none',
        duration: 2000
      });
      throw error; // 向上传递错误，由调用者处理
    }
  }
})