// pages/story/story.js
import { setupPreloadedStory } from '../../utils/preloadedStoryPlayer.js';

Page({
  data: {
    content: '',
    voiceUrl: '',
    isPlaying: false,
    progress: 0,
    currentTime: '00:00',
    duration: '00:00',
    status: 'generating',
    audioStatus: 'waiting', // 'waiting', 'generating', 'playing', 'buffering', 'completed', 'failed'
    audioSegments: [], // fileIDs
    currentSegmentIndex: 0,
    isPlayingSegments: false,
    segmentUrls: [], // 存储已下载的音频临时URL
    totalDuration: 0, // 所有音频片段的总时长
    showProgress: false, // 是否显示总进度条
    segmentDurations: [], // 存储每个音频片段的时长
    isLoading: true, // 用于显示加载图标（初始加载或缓冲时）
    lastPlayPosition: 0, // 记录上次播放位置
    allSegmentsDownloaded: false, // 标记是否所有音频片段都已下载完成
    showSpeedSetting: false, // 是否显示音速设置弹窗
    playbackRate: 1.0, // 播放速度，默认为1.0
    storyId: '', // 当前故事ID
    fromHistory: false, // 是否从历史记录进入
    fromHome: false, // 是否从首页进入
    retryCount: 0, // 重试计数器
    maxRetries: 3, // 最大重试次数
    isLiked: false, // 收藏状态
    title: '', // 故事标题
    subtitle: '', // 故事副标题
    question: '', // 故事问题
    answer: '', // 故事答案
    type: '', // 故事类型
    image: '', // 故事图片
    pollingInterval: null, // 用于轮询音频生成状态
    nextSegmentCheckInterval: null, // 用于检查下一片段是否下载完成
  },

  onLoad(options) {
    this.isPageUnloaded = false;
    this.watcher = null;

    if (options.fromCustom === 'true') {
      // 场景B：从自定义页面进入，创建新故事
      setupCustomStory(this, options);
    } else if (options.storyId) {
      // 场景A：从首页、历史等页面进入，加载已有故事
      setupPreloadedStory(this, options);
    }
  },

  // 关键修复：页面隐藏时立即销毁音频管理器
  onHide() {
    console.log('页面隐藏，立即销毁音频播放器');
    this.isPageHidden = true;
  
    // 先停止所有网络请求
    if (this.siliconFlowRequestTask) {
      try {
        this.siliconFlowRequestTask.abort();
        this.siliconFlowRequestTask = null;
        console.log('硅基流动请求已中断');
      } catch (e) {
        console.error('中断硅基流动请求时出错:', e);
      }
    }
  
    // 然后销毁音频管理器
    if (this.audioManager) {
      this.audioManager.destroy();
      this.audioManager = null;
      console.log('音频管理器已销毁');
    }
  
    // 最后清理旧的音频上下文
    if (this.audioCtx) {
      try {
        this.audioCtx.stop();
        this.audioCtx.destroy();
        this.audioCtx = null;
        console.log('旧音频上下文已销毁');
      } catch (e) {
        console.error('销毁旧音频上下文时出错:', e);
      }
    }
  
    // 重置所有播放相关状态
    this.setData({
      isPlaying: false,
      audioStatus: 'stopped',
      isLoading: false,
      showProgress: false,
      allSegmentsDownloaded: false,
      streamingComplete: false
    });
  
    console.log('页面隐藏处理完成');
  },
  
  onUnload() {
    this.isPageUnloaded = true;
    
    // 与onHide相同的清理逻辑
    this.onHide();
    
    // 额外清理轮询定时器
    if (this.data.pollingInterval) {
      clearInterval(this.data.pollingInterval);
      this.setData({ pollingInterval: null });
    }
    if (this.data.nextSegmentCheckInterval) {
      clearInterval(this.data.nextSegmentCheckInterval);
      this.setData({ nextSegmentCheckInterval: null });
    }
    
    // 清理数据库监听器
    if (this.watcher) {
      this.watcher.close();
      this.watcher = null;
    }
    
    // 清理临时文件
    if (this.currentTempFilePath) {
      this.cleanupTempFile(this.currentTempFilePath);
      this.currentTempFilePath = null;
    }
  },

  // 页面显示时重置隐藏标志
  onShow() {
    this.isPageHidden = false;
    console.log('页面显示，重置隐藏标志');
  },

  // 🔥 根据用户需求重新设计的硅基流动流式播放方法
  async startSiliconFlowStreaming(storyData, voiceType, ticket) {
    try {
      console.log(`开始硅基流动流式播放: 故事=${storyData.title}, 声音=${voiceType}`);

      // 设置初始状态：显示加载图标，不显示进度条
      this.setData({
        isLoading: true,
        audioStatus: 'connecting',
        showProgress: false,
        streamingComplete: false
      });

      if (!ticket) {
        throw new Error("流式播放票据缺失");
      }

      const fullText = this.constructFullText(storyData);
      if (!fullText) throw new Error('故事内容为空');

      const requestPayload = await this.buildSiliconFlowRequestPayload(fullText, voiceType);

      // 启动真正的流式播放
      await this.initiateTrueStreamingPlayback(ticket, requestPayload, storyData, voiceType);

    } catch (error) {
      console.error('硅基流动流式播放失败：', error);
      this.setData({
        isLoading: false,
        audioStatus: 'failed',
        showProgress: false
      });
      wx.showToast({ title: '播放失败，请重试', icon: 'none' });
    }
  },

  // 构建故事完整文本
  constructFullText(storyData) {
    const { title, subtitle, content, question } = storyData;
    let text = `${title || ''} ${subtitle || ''} ${content || ''}`;
    if (question && question.trim().length > 0) {
      text += ` ${question.trim()}`;
    }
    // 清理文本，确保API安全
    return text.replace(/[\n\r\t◉]+/g, ' ').replace(/ +/g, ' ').trim();
  },

  // 构建硅基流动请求载荷
  async buildSiliconFlowRequestPayload(text, voiceType) {
    const babyInfo = wx.getStorageSync('babyInfo');
    let voiceParamForAPI = '';

    const BASE_MODEL_PATH = 'FunAudioLLM/CosyVoice2-0.5B';

    switch (voiceType) {
      case 'auntie':
      case 'female':
        voiceParamForAPI = `${BASE_MODEL_PATH}:claire`;
        break;
      case 'uncle':
      case 'male':
        voiceParamForAPI = `${BASE_MODEL_PATH}:benjamin`;
        break;
      case 'mom':
        // 如果有自定义妈妈声音，使用自定义声音，否则使用默认女声
        if (babyInfo && babyInfo.momUri) {
          voiceParamForAPI = babyInfo.momUri;
        } else {
          voiceParamForAPI = `${BASE_MODEL_PATH}:claire`;
        }
        break;
      case 'dad':
        // 如果有自定义爸爸声音，使用自定义声音，否则使用默认男声
        if (babyInfo && babyInfo.dadUri) {
          voiceParamForAPI = babyInfo.dadUri;
        } else {
          voiceParamForAPI = `${BASE_MODEL_PATH}:benjamin`;
        }
        break;
      default:
        voiceParamForAPI = `${BASE_MODEL_PATH}:claire`;
        break;
    }

    return {
      model: BASE_MODEL_PATH,
      input: text,
      response_format: "mp3",
      sample_rate: 32000,
      stream: true,
      speed: 1,
      gain: 0,
      voice: voiceParamForAPI,
    };
  },

  // 🔥 根据用户需求重新设计的真正流式播放方法
  async initiateTrueStreamingPlayback(ticket, payload, storyData, voiceType) {
    console.log(`启动真正的流式播放: 故事=${storyData.title}, 声音=${voiceType}`);

    // 清理之前的音频上下文
    if (this.audioCtx) {
      this.audioCtx.stop();
      this.audioCtx.destroy();
      this.audioCtx = null;
    }

    let audioChunks = [];
    let totalSize = 0;
    let isStreamingComplete = false;
    let currentAudioUrl = null;

    try {
      // 发起HTTP流请求
      const requestTask = wx.request({
        url: ticket.url,
        method: 'POST',
        header: {
          'Authorization': `Bearer ${ticket.apiKey}`,
          'Content-Type': 'application/json',
        },
        data: payload,
        responseType: 'arraybuffer',
        enableChunked: true,
        success: () => {
          console.log('硅基流动HTTP流完成，总大小:', totalSize, 'bytes');
          isStreamingComplete = true;

          // 流式播放完成，显示进度条，重置播放进度到开头
          this.setData({
            streamingComplete: true,
            isLoading: false,
            showProgress: true,
            progress: 0,
            currentTime: '00:00'
          });

          // 后台保存完整音频到对应的数据库字段
          this.saveStreamedAudioToDatabase(audioChunks, storyData, voiceType);
        },
        fail: (err) => {
          console.error('硅基流动HTTP流请求失败:', err);
          this.setData({
            isLoading: false,
            audioStatus: 'failed',
            showProgress: false
          });
          wx.showToast({ title: '音频生成失败', icon: 'none' });
        }
      });

      // 处理流数据块 - 实现连续播放，不重头开始
      requestTask.onChunkReceived((res) => {
        try {
          audioChunks.push(res.data);
          totalSize += res.data.byteLength;
          console.log(`收到音频块: ${res.data.byteLength} bytes, 总计: ${totalSize} bytes`);

          // 当收到足够数据时开始播放（约32KB）
          if (!currentAudioUrl && totalSize >= 32768) {
            console.log('收到足够音频数据，开始连续播放');
            this.startContinuousStreamingPlayback(audioChunks);
          }

        } catch (error) {
          console.error('处理音频块失败:', error);
        }
      });

      // 保存请求任务引用以便清理
      this.siliconFlowRequestTask = requestTask;

    } catch (error) {
      console.error('启动流式播放失败:', error);
      this.setData({
        isLoading: false,
        audioStatus: 'failed',
        showProgress: false
      });
      wx.showToast({ title: '音频生成失败', icon: 'none' });
    }
  },

  // 🔥 新的连续流式播放方法 - 不重头开始播放
  async startContinuousStreamingPlayback(audioChunks) {
    try {
      console.log('开始连续流式播放，当前数据量:', audioChunks.reduce((sum, chunk) => sum + chunk.byteLength, 0), 'bytes');

      // 合并音频块
      const totalSize = audioChunks.reduce((sum, chunk) => sum + chunk.byteLength, 0);
      const mergedBuffer = new ArrayBuffer(totalSize);
      const mergedView = new Uint8Array(mergedBuffer);

      let offset = 0;
      audioChunks.forEach(chunk => {
        const chunkView = new Uint8Array(chunk);
        mergedView.set(chunkView, offset);
        offset += chunk.byteLength;
      });

      // 转换为Base64播放，避免文件大小限制
      const base64 = wx.arrayBufferToBase64(mergedBuffer);
      const dataUrl = `data:audio/mpeg;base64,${base64}`;

      // 创建音频上下文并开始播放
      this.audioCtx = wx.createInnerAudioContext({ obeyMuteSwitch: false });
      this.audioCtx.preservesPitch = true;
      this.audioCtx.playbackRate = this.data.playbackRate || 1.0;
      this.audioCtx.src = dataUrl;

      // 设置音频事件监听器
      this.setupStreamingAudioListeners();

      // 更新状态 - 开始播放，但不显示进度条（等流式完成后再显示）
      this.setData({
        isLoading: true,  // 保持加载状态，表示还在接收数据
        audioStatus: 'playing',
        isPlaying: true,
        showProgress: false,  // 重要：流式播放期间不显示进度条
        streamingComplete: false
      });

      this.audioCtx.play();
      console.log('连续流式播放已开始');

    } catch (error) {
      console.error('连续流式播放失败:', error);
      this.setData({
        isLoading: false,
        audioStatus: 'failed',
        isPlaying: false,
        showProgress: false
      });
      wx.showToast({ title: '播放失败', icon: 'none' });
    }
  },

  // 🔥 设置流式播放音频监听器
  setupStreamingAudioListeners() {
    if (!this.audioCtx) return;

    this.audioCtx.onPlay(() => {
      console.log('流式音频开始播放');
      this.setData({
        isPlaying: true,
        audioStatus: 'playing'
      });
    });

    this.audioCtx.onPause(() => {
      console.log('流式音频暂停');
      this.setData({
        isPlaying: false,
        audioStatus: 'paused'
      });
    });

    this.audioCtx.onEnded(() => {
      console.log('流式音频播放完成');
      // 流式播放完成后，重置进度到开头，显示进度条
      this.setData({
        isPlaying: false,
        audioStatus: 'completed',
        progress: 0,
        currentTime: '00:00',
        showProgress: true  // 现在可以显示进度条了
      });
    });

    this.audioCtx.onError((error) => {
      console.error('流式音频播放错误:', error);
      this.setData({
        isPlaying: false,
        audioStatus: 'error',
        isLoading: false,
        showProgress: false
      });
      wx.showToast({ title: '播放失败', icon: 'none' });
    });

    // 注意：流式播放期间不设置onTimeUpdate，避免显示进度条
  },

  // 🔥 保存流式音频到对应的数据库字段
  async saveStreamedAudioToDatabase(audioChunks, storyData, voiceType) {
    try {
      console.log(`开始保存流式音频到数据库: 故事=${storyData.title}, 声音=${voiceType}`);

      // 合并所有音频块为完整的音频数据
      const completeAudioBuffer = this.mergeAudioChunks(audioChunks);
      console.log('完整音频大小:', completeAudioBuffer.byteLength, 'bytes');

      // 调用云函数保存音频
      const saveRes = await wx.cloud.callFunction({
        name: 'saveStoryAudio',
        data: {
          storyId: storyData._id,
          voiceType: voiceType,
          audioBuffer: wx.arrayBufferToBase64(completeAudioBuffer),
          storyTitle: storyData.title
        }
      });

      if (saveRes.result && saveRes.result.errCode === 0) {
        console.log('流式音频保存成功:', saveRes.result.message);
      } else {
        console.error('流式音频保存失败:', saveRes.result?.errMsg || '未知错误');
        // 保存失败不影响播放，只记录错误
      }

    } catch (error) {
      console.error('保存流式音频失败:', error);
      // 保存失败不影响播放，只记录错误
    }
  },

  // 切换到最新的完整音频
  switchToLatestAudio(currentPosition) {
    if (!this.latestCompleteAudioUrl || !this.audioCtx) return;

    try {
      console.log(`从位置 ${currentPosition.toFixed(1)}s 切换到最新音频`);

      // 保存当前播放状态
      const wasPlaying = !this.audioCtx.paused;

      // 切换到最新音频
      this.audioCtx.src = this.latestCompleteAudioUrl;

      // 从当前位置继续播放
      if (currentPosition > 0) {
        this.audioCtx.seek(currentPosition);
      }

      if (wasPlaying) {
        this.audioCtx.play();
      }

      // 清除标记
      this.latestCompleteAudioUrl = null;

    } catch (error) {
      console.error('切换音频失败:', error);
    }
  },

  // 合并音频块为完整的ArrayBuffer
  mergeAudioChunks(audioChunks) {
    const totalSize = audioChunks.reduce((sum, chunk) => sum + chunk.byteLength, 0);
    const audioBuffer = new ArrayBuffer(totalSize);
    const uint8Array = new Uint8Array(audioBuffer);

    let offset = 0;
    audioChunks.forEach(chunk => {
      uint8Array.set(new Uint8Array(chunk), offset);
      offset += chunk.byteLength;
    });

    return audioBuffer;
  },

  // 切换到完整音频播放并保存到云存储（保留用于非流式播放场景）
  async switchToCompleteAudio(audioChunks, storyData, voiceType) {
    try {
      console.log('切换到完整音频播放...');

      // 合并所有音频块
      const totalSize = audioChunks.reduce((sum, chunk) => sum + chunk.byteLength, 0);
      console.log(`完整音频大小: ${totalSize} bytes (${(totalSize/1024/1024).toFixed(2)}MB)`);

      const mergedBuffer = new ArrayBuffer(totalSize);
      const mergedView = new Uint8Array(mergedBuffer);

      let offset = 0;
      audioChunks.forEach(chunk => {
        const chunkView = new Uint8Array(chunk);
        mergedView.set(chunkView, offset);
        offset += chunk.byteLength;
      });

      // 记录当前播放位置和播放状态
      let currentTime = 0;
      let wasPlaying = false;
      if (this.audioCtx) {
        currentTime = this.audioCtx.currentTime || 0;
        wasPlaying = !this.audioCtx.paused;
        console.log('当前播放位置:', currentTime, '秒, 播放状态:', wasPlaying);
        this.audioCtx.stop();
        this.audioCtx.destroy();
      }

      // 创建完整音频的Base64播放
      const base64 = wx.arrayBufferToBase64(mergedBuffer);
      const dataUrl = `data:audio/mpeg;base64,${base64}`;

      // 创建新的音频上下文播放完整音频
      this.audioCtx = wx.createInnerAudioContext({ obeyMuteSwitch: false });
      this.audioCtx.preservesPitch = true;
      this.audioCtx.playbackRate = this.data.playbackRate;
      this.audioCtx.src = dataUrl;

      this.setupSiliconFlowAudioListeners();

      // 更新状态 - 完整音频已准备好，标记流式完成
      this.setData({
        isLoading: false,
        audioStatus: 'playing',
        streamingComplete: true  // 标记流式播放完成，可以显示进度条
      });

      // 设置播放位置并继续播放
      if (currentTime > 0) {
        // 使用seek方法设置播放位置
        this.audioCtx.seek(currentTime);
        console.log('设置播放位置到:', currentTime, '秒');
      }

      // 如果之前在播放，继续播放；否则暂停
      if (wasPlaying) {
        this.audioCtx.play();
        console.log('完整音频继续播放');
      } else {
        console.log('完整音频已准备好，等待用户操作');
      }

      // 后台保存到云存储（使用更轻量的方式）
      this.saveCompleteAudioToCloudLightweight(mergedBuffer, storyData, voiceType);

    } catch (error) {
      console.error('切换到完整音频失败:', error);
      this.setData({ isLoading: false });
    }
  },

  // 轻量级云存储保存方法 - 使用云函数避免本地文件限制
  async saveCompleteAudioToCloudLightweight(audioBuffer, storyData, voiceType) {
    try {
      console.log('后台保存音频到云存储（云函数方式）...');

      const totalSize = audioBuffer.byteLength;
      console.log(`音频大小: ${totalSize} bytes (${(totalSize/1024/1024).toFixed(2)}MB)`);

      // 如果文件较小（<1MB），尝试直接Base64上传
      if (totalSize < 1024 * 1024) {
        console.log('小文件，尝试Base64上传...');
        const base64Data = wx.arrayBufferToBase64(audioBuffer);

        try {
          const uploadResult = await wx.cloud.callFunction({
            name: 'uploadAudioFromBase64',
            data: {
              storyId: storyData._id || this.data.storyId,
              voiceType: voiceType,
              audioBase64: base64Data,
              filename: `siliconflow_${voiceType}_${Date.now()}.mp3`
            }
          });

          if (uploadResult.result && uploadResult.result.success) {
            console.log('小文件上传成功:', uploadResult.result.fileID);
            this.saveAudioToDatabase(
              storyData._id || this.data.storyId,
              uploadResult.result.fileID,
              voiceType
            );
            return;
          }
        } catch (uploadError) {
          console.log('Base64上传失败，音频仅在本次会话中可用:', uploadError);
        }
      } else {
        console.log('大文件，跳过上传以避免超时');
      }

      // 大文件或上传失败时，音频仅在本次会话中可用
      console.log('音频未保存到云存储，仅在本次会话中可用');

    } catch (error) {
      console.error('后台保存音频失败:', error);
    }
  },

  // 保存完整音频到云存储（后台进行）- 旧方法保留作为备用
  async saveCompleteAudioToCloud(audioChunks, storyData, voiceType) {
    try {
      console.log('后台保存完整音频到云存储...');

      // 合并所有音频块
      const totalSize = audioChunks.reduce((sum, chunk) => sum + chunk.byteLength, 0);
      console.log(`完整音频大小: ${totalSize} bytes (${(totalSize/1024/1024).toFixed(2)}MB)`);

      const mergedBuffer = new ArrayBuffer(totalSize);
      const mergedView = new Uint8Array(mergedBuffer);

      let offset = 0;
      audioChunks.forEach(chunk => {
        const chunkView = new Uint8Array(chunk);
        mergedView.set(chunkView, offset);
        offset += chunk.byteLength;
      });

      // 使用云函数上传，避免本地文件限制
      console.log('通过云函数上传音频...');

      try {
        // 将ArrayBuffer转换为Base64
        const base64Data = wx.arrayBufferToBase64(mergedBuffer);

        // 调用云函数处理上传
        const uploadResult = await wx.cloud.callFunction({
          name: 'uploadAudioFromBase64',
          data: {
            storyId: storyData._id || this.data.storyId,
            voiceType: voiceType,
            audioBase64: base64Data,
            filename: `siliconflow_${voiceType}_${Date.now()}.mp3`
          }
        });

        if (uploadResult.result && uploadResult.result.success) {
          console.log('音频通过云函数上传成功:', uploadResult.result.fileID);

          // 保存到数据库
          this.saveAudioToDatabase(
            storyData._id || this.data.storyId,
            uploadResult.result.fileID,
            voiceType
          );
        } else {
          console.log('云函数上传失败，音频仅在本次会话中可用');
        }

      } catch (uploadError) {
        console.log('后台上传失败，音频仅在本次会话中可用:', uploadError);
      }

    } catch (error) {
      console.error('后台保存音频失败:', error);
    }
  },

  // 备选播放方案 - 当文件太大无法本地存储时
  fallbackToBase64Play(audioBuffer) {
    try {
      console.log('使用备选播放方案...');

      // 转换为base64 data URL
      const uint8Array = new Uint8Array(audioBuffer);
      let binary = '';
      for (let i = 0; i < uint8Array.byteLength; i++) {
        binary += String.fromCharCode(uint8Array[i]);
      }
      const base64 = wx.arrayBufferToBase64(audioBuffer);
      const dataUrl = `data:audio/mpeg;base64,${base64}`;

      // 创建音频上下文
      this.audioCtx = wx.createInnerAudioContext({ obeyMuteSwitch: false });
      this.audioCtx.preservesPitch = true;
      this.audioCtx.playbackRate = this.data.playbackRate;
      this.audioCtx.src = dataUrl;

      this.setupSiliconFlowAudioListeners();

      this.setData({
        isLoading: false,
        audioStatus: 'playing',
        showProgress: false
      });

      this.audioCtx.play();

    } catch (error) {
      console.error('备选播放方案失败:', error);
      this.setData({ isLoading: false, audioStatus: 'failed' });
      wx.showToast({ title: '播放失败', icon: 'none' });
    }
  },

  // 简化的数据库保存方法
  async saveAudioToDatabase(storyId, fileID, voiceType) {
    try {
      console.log('保存音频到数据库...', { storyId, fileID, voiceType });

      // 处理复杂的voice格式，提取简单的voice名称
      let simpleVoice = voiceType;
      if (typeof voiceType === 'string' && voiceType.includes('/')) {
        // 如果是 "FunAudioLLM/CosyVoice2-0.5B:benjamin" 格式，提取最后部分
        const parts = voiceType.split('/');
        if (parts.length > 1) {
          const lastPart = parts[parts.length - 1];
          if (lastPart.includes(':')) {
            simpleVoice = lastPart.split(':')[1]; // 提取 "benjamin"
          } else {
            simpleVoice = lastPart;
          }
        }
      }

      console.log('处理后的voice:', simpleVoice);

      const saveResult = await wx.cloud.callFunction({
        name: 'saveStoryAudio',
        data: {
          storyId: storyId,
          voice: simpleVoice,
          audioUrl: [fileID],
          source: 'siliconflow',
          originalVoice: voiceType, // 保留原始voice信息
          createdAt: new Date()
        },
      });

      if (saveResult.result && saveResult.result.success) {
        console.log('音频已成功保存到数据库');
        this.setData({
          [`audioCache_${simpleVoice}`]: [fileID]
        });
      } else {
        console.error('保存音频记录到数据库失败:', saveResult.result);
      }
    } catch (error) {
      console.error('保存音频到数据库失败:', error);
    }
  },



  // 清理临时文件的辅助方法
  cleanupTempFile(filePath) {
    try {
      wx.getFileSystemManager().unlink({
        filePath: filePath,
        success: () => console.log('临时文件清理成功'),
        fail: (err) => console.log('清理临时文件失败:', err)
      });
    } catch (e) {
      console.log('清理临时文件时出错:', e);
    }
  },

  // 格式化时间的辅助方法
  formatTime(seconds) {
    if (!seconds || isNaN(seconds)) return '00:00';
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  },

  // 设置硅基流动音频播放器监听器
  setupSiliconFlowAudioListeners() {
    if (!this.audioCtx) return;

    this.audioCtx.onPlay(() => {
      console.log('硅基流动音频开始播放');
      this.setData({
        isPlaying: true,
        audioStatus: 'playing'
      });
    });

    this.audioCtx.onPause(() => {
      console.log('硅基流动音频暂停');
      this.setData({
        isPlaying: false,
        audioStatus: 'paused'
      });
    });

    this.audioCtx.onStop(() => {
      console.log('硅基流动音频停止');
      this.setData({
        isPlaying: false,
        audioStatus: 'stopped',
        currentTime: 0,
        progress: 0
      });
    });

    this.audioCtx.onEnded(() => {
      console.log('硅基流动音频播放完成');

      // 检查是否有更新的音频需要继续播放
      if (this.latestCompleteAudioUrl) {
        console.log('播放完成，但有更新的音频，继续播放');
        this.audioCtx.src = this.latestCompleteAudioUrl;
        this.audioCtx.play();
        this.latestCompleteAudioUrl = null;
      } else {
        // 真正的播放结束，重置状态
        console.log('音频播放正常结束');
        this.setData({
          isPlaying: false,
          audioStatus: 'stopped',
          progress: 0,  // 重置进度条到开头
          currentTime: '00:00'  // 重置时间显示
        });
      }
    });

    this.audioCtx.onCanplay(() => {
      console.log('硅基流动音频可以播放，时长:', this.audioCtx.duration);

      // 只有在完整音频切换后才显示进度条
      // 通过检查是否已经完成流式接收来判断
      if (this.data.streamingComplete) {
        const duration = this.audioCtx.duration || 0;
        if (duration > 0 && !isNaN(duration)) {
          this.setData({
            duration: this.formatTime(duration),
            showProgress: true,
            isLoading: false  // 停止加载状态
          });
        } else {
          // 音频无效，不显示进度条
          this.setData({
            isLoading: false,
            showProgress: false
          });
        }
      } else {
        // 流式播放阶段，不显示进度条，保持加载状态
        console.log('流式播放阶段，暂不显示进度条');
      }
    });

    this.audioCtx.onTimeUpdate(() => {
      if (this.isPageUnloaded) return;

      const currentTime = this.audioCtx.currentTime || 0;
      const duration = this.audioCtx.duration || 0;

      this.setData({
        currentTime: this.formatTime(currentTime),
        duration: this.formatTime(duration),
        progress: duration > 0 ? (currentTime / duration) * 100 : 0
      });
    });

    this.audioCtx.onError((res) => {
      console.error('硅基流动音频播放失败:', res.errMsg);
      this.setData({
        isLoading: false,
        audioStatus: 'failed',
        isPlaying: false
      });
      wx.showToast({ title: '播放失败', icon: 'none' });
    });
  },



  // 新增：渐进式轮询方法
startProgressivePolling(storyId, voice) {
  if (this.data.pollingInterval) clearInterval(this.data.pollingInterval);
  
  let lastSegmentCount = this.data.audioSegments.length;
  
  const poll = async () => {
      if (this.isPageUnloaded) { 
          clearInterval(this.data.pollingInterval); 
          return; 
      }
      
      try {
          // 使用新的进度查询函数
          const res = await wx.cloud.callFunction({ 
              name: 'getAudioProgress', 
              data: { storyId, voice } 
          });
          
          if (res.result && res.result.errCode === 0) {
              const { status, audioSegments, generatedCount, totalCount, progress, hasNewSegments } = res.result;
              
              // 更新生成进度信息
              console.log(`音频生成进度: ${generatedCount}/${totalCount} (${progress.toFixed(1)}%)`);
              
              // 如果有新的音频片段
              if (hasNewSegments && audioSegments.length > lastSegmentCount) {
                  console.log(`检测到新音频片段: ${audioSegments.length - lastSegmentCount} 个`);
                  
                  // 更新音频片段数组
                  this.setData({ audioSegments: audioSegments });
                  
                  // 如果是第一次有音频且还未开始播放
                  if (lastSegmentCount === 0 && !this.data.isPlayingSegments) {
                      console.log('开始渐进式播放第一个片段');
                      this.startProgressivePlayback();
                  } else {
                      // 继续下载新片段
                      this.downloadNewSegments(lastSegmentCount, audioSegments.length - 1);
                  }
                  
                  lastSegmentCount = audioSegments.length;
              }
              
              // 检查是否完成
              if (status === 'completed') {
                  console.log('所有音频片段生成完成');
                  clearInterval(this.data.pollingInterval);
                  this.setData({ 
                      pollingInterval: null, 
                      audioStatus: 'completed',
                      audioSegments: audioSegments 
                  });
                  
                  // 确保所有片段都下载完成
                  this.downloadRemainingSegmentsInBackground();
              } else if (status === 'failed') {
                  console.log('音频生成失败');
                  clearInterval(this.data.pollingInterval);
                  this.setData({ 
                      pollingInterval: null, 
                      audioStatus: 'failed' 
                  });
                  wx.showToast({ title: '音频生成失败', icon: 'none' });
              }
          }
      } catch (error) { 
          console.error('轮询失败:', error); 
      }
  };
  
  // 更频繁的轮询，以便及时获取新片段
  this.setData({ pollingInterval: setInterval(poll, 2000) }); // 2秒轮询一次
  
  // 立即执行一次
  poll();
},
// 🔥 新增：下载新片段的方法
async downloadNewSegments(startIndex, endIndex) {
  const { audioSegments } = this.data;
  
  for (let i = startIndex; i <= endIndex; i++) {
      if (this.isPageUnloaded) break;
      
      if (audioSegments[i] && !this.data.segmentUrls[i]) {
          try {
              console.log(`开始下载第 ${i + 1} 个音频片段`);
              
              const { fileList } = await wx.cloud.getTempFileURL({ 
                  fileList: [audioSegments[i]] 
              });
              
              if (fileList && fileList.length > 0) {
                  const url = fileList[0].tempFileURL;
                  const newSegmentUrls = [...this.data.segmentUrls];
                  newSegmentUrls[i] = url;
                  this.setData({ segmentUrls: newSegmentUrls });
                  
                  // 获取音频时长
                  const duration = await this.getAudioDuration(url);
                  const newSegmentDurations = [...this.data.segmentDurations];
                  newSegmentDurations[i] = duration;
                  this.setData({ segmentDurations: newSegmentDurations });
                  
                  console.log(`第 ${i + 1} 个音频片段下载完成，时长: ${duration.toFixed(1)}s`);
                  
                  // 更新总时长
                  const totalDuration = newSegmentDurations.reduce((acc, cur) => acc + (cur || 0), 0);
                  this.setData({ 
                      totalDuration: totalDuration,
                      duration: this.formatTime(totalDuration),
                  });
              }
          } catch (error) { 
              console.error(`下载第 ${i + 1} 个音频片段失败:`, error); 
          }
      }
  }
},
async startProgressivePlayback() {
  const { audioSegments } = this.data;
  if (!audioSegments || audioSegments.length === 0) {
      console.log('没有可播放的音频片段');
      return;
  }
  
  // 重置播放状态
  this.setData({ 
      isLoading: true, 
      showProgress: false, 
      progress: 0,
      currentSegmentIndex: 0 
  });

  try {
      console.log('开始渐进式播放，准备第一个片段');
      
      const firstFileID = audioSegments[0];
      const { fileList } = await wx.cloud.getTempFileURL({ fileList: [firstFileID] });
      
      if (!fileList || fileList.length === 0) {
          throw new Error('无法获取第一个音频片段的URL');
      }
      
      const url = fileList[0].tempFileURL;
      const newSegmentUrls = [...this.data.segmentUrls];
      newSegmentUrls[0] = url;
      this.setData({ segmentUrls: newSegmentUrls });

      const duration = await this.getAudioDuration(url);
      const newSegmentDurations = [...this.data.segmentDurations];
      newSegmentDurations[0] = duration;
      this.setData({ 
          segmentDurations: newSegmentDurations,
          totalDuration: duration,
          duration: this.formatTime(duration),
      });

      console.log('第一个音频片段准备完成，开始播放');
      this.setData({ isPlayingSegments: true });
      await this.playSegment(0);

      // 在背景下载其他片段
      if (audioSegments.length > 1) {
          this.downloadRemainingSegmentsInBackground();
      }
  } catch (error) {
      console.error('启动渐进式播放失败:', error);
      this.setData({ isLoading: false, audioStatus: 'failed' });
      wx.showToast({ title: '播放失败', icon: 'none' });
  }
},

  async downloadRemainingSegmentsInBackground() {
      const { audioSegments } = this.data;
      let calculatedTotalDuration = this.data.segmentDurations[0] || 0;
      for (let i = 1; i < audioSegments.length; i++) {
          if (this.isPageUnloaded) break;
          if (!this.data.segmentUrls[i]) {
              try {
                  const { fileList } = await wx.cloud.getTempFileURL({ fileList: [audioSegments[i]] });
                  if (fileList && fileList.length > 0) {
                      const url = fileList[0].tempFileURL;
                      const newSegmentUrls = [...this.data.segmentUrls];
                      newSegmentUrls[i] = url;
                      this.setData({ segmentUrls: newSegmentUrls });
                      const duration = await this.getAudioDuration(url);
                      const newSegmentDurations = [...this.data.segmentDurations];
                      newSegmentDurations[i] = duration;
                      this.setData({ segmentDurations: newSegmentDurations });
                      calculatedTotalDuration += duration;
                  }
              } catch (error) { console.error(`下载或获取片段 ${i} 时长失败:`, error); }
          } else {
            calculatedTotalDuration += this.data.segmentDurations[i] || 0;
          }
      }
      if (!this.isPageUnloaded) {
          this.setData({
              allSegmentsDownloaded: true,
              totalDuration: calculatedTotalDuration,
              duration: this.formatTime(calculatedTotalDuration),
              showProgress: true
          });
      }
  },

  async playSegment(index) {
      if (this._isPlaying || !this.data.segmentUrls[index]) return;
      this._isPlaying = true;
      this.setData({ isLoading: false, currentSegmentIndex: index });
      try {
          await this.initAudio(this.data.segmentUrls[index]);
          if (this.data.isPlayingSegments) this.audioCtx.play();
      } catch (error) {
          console.error(`播放音频片段 ${index} 失败:`, error);
          this._isPlaying = false;
          if (this.data.isPlayingSegments) this.playNextSegment();
      }
  },

  playNextSegment() {
      if (this.data.nextSegmentCheckInterval) {
          clearInterval(this.data.nextSegmentCheckInterval);
          this.setData({ nextSegmentCheckInterval: null });
      }
      const nextIndex = this.data.currentSegmentIndex + 1;
      if (nextIndex >= this.data.audioSegments.length) {
          this.setData({ isPlayingSegments: false, isPlaying: false, currentSegmentIndex: 0, progress: 0, currentTime: '00:00' });
          this._isPlaying = false;
          if (this.audioCtx) this.audioCtx.stop();
          return;
      }
      if (this.data.segmentUrls[nextIndex]) {
          this.playSegment(nextIndex);
      } else {
          this.setData({ isLoading: true, isPlaying: false });
          const checkInterval = setInterval(() => {
              if (this.isPageUnloaded) { clearInterval(checkInterval); return; }
              if (this.data.segmentUrls[nextIndex]) {
                  clearInterval(this.data.nextSegmentCheckInterval);
                  this.setData({ nextSegmentCheckInterval: null, isLoading: false });
                  this.playSegment(nextIndex);
              }
          }, 500);
          this.setData({ nextSegmentCheckInterval: checkInterval });
      }
  },

  startPolling(storyId, voice) {
    if (this.data.pollingInterval) clearInterval(this.data.pollingInterval);
    const poll = async () => {
        if (this.isPageUnloaded) { clearInterval(this.data.pollingInterval); return; }
        try {
            const res = await wx.cloud.callFunction({ name: 'getOrCreateStoryAudio', data: { storyId, voice } });
            if (res.result && res.result.errCode === 0 && res.result.audioUrl && res.result.audioUrl.length > 0) {
                clearInterval(this.data.pollingInterval);
                this.setData({ pollingInterval: null, audioSegments: res.result.audioUrl, audioStatus: 'completed' });
                this.startProgressivePlayback();
            }
        } catch (error) { console.error('轮询失败:', error); }
    };
    this.setData({ pollingInterval: setInterval(poll, 5000) });
  },

  onUnload() {
      this.isPageUnloaded = true;

      // 清理数据库监听器
      if (this.watcher) {
          this.watcher.close();
          this.watcher = null;
      }

      // 清理轮询定时器
      if (this.data.pollingInterval) {
          clearInterval(this.data.pollingInterval);
          this.setData({ pollingInterval: null });
      }
      if (this.data.nextSegmentCheckInterval) {
          clearInterval(this.data.nextSegmentCheckInterval);
          this.setData({ nextSegmentCheckInterval: null });
      }

      // 清理硅基流动相关资源
      if (this.siliconFlowRequestTask) {
          try {
              this.siliconFlowRequestTask.abort();
              this.siliconFlowRequestTask = null;
              console.log('硅基流动请求任务已中止');
          } catch (e) {
              console.error("中止硅基流动请求任务时出错", e);
          }
      }

      // 清理临时文件
      if (this.currentTempFilePath) {
          this.cleanupTempFile(this.currentTempFilePath);
          this.currentTempFilePath = null;
      }

      // 清理新的音频管理器
      if (this.audioManager) {
          this.audioManager.destroy();
          this.audioManager = null;
      }

      // 清理音频上下文
      if (this.audioCtx) {
          try {
              this.audioCtx.stop();
              this.audioCtx.destroy();
              this.audioCtx = null;
          } catch (e) {
              console.error("销毁音频上下文时出错", e);
          }
      }
  },

  togglePlay() {
      if (this.data.isLoading) return;

      // 使用新的音频管理器
      if (this.audioManager) {
          this.audioManager.togglePlayback();
      } else {
          // 兼容旧的播放逻辑
          if (this.data.isPlaying) {
              // 暂停播放
              if (this.audioCtx) {
                  this.lastPlayPosition = this.audioCtx.currentTime;
                  this.audioCtx.pause();
              }
              this.setData({
                  isPlaying: false,
                  isPlayingSegments: false,
                  audioStatus: 'paused'
              });
          } else {
              // 开始播放
              if (this.audioCtx && (this.data.audioStatus === 'playing' || this.data.audioStatus === 'paused' || this.data.audioStatus === 'stopped')) {
                  // 硅基流动音频播放模式
                  console.log('恢复硅基流动音频播放');
                  if (this.lastPlayPosition > 0) {
                      this.audioCtx.seek(this.lastPlayPosition);
                      this.lastPlayPosition = 0;
                  }
                  this.audioCtx.play();
                  this.setData({
                      isPlaying: true,
                      audioStatus: 'playing'
                  });
              } else if (this.data.audioSegments.length > 0) {
                  // 传统分段播放模式
                  const currentSegmentUrl = this.data.segmentUrls[this.data.currentSegmentIndex];
                  if (!currentSegmentUrl) return;

                  const startPlayback = async () => {
                      try {
                          if (!this.audioCtx || this.audioCtx.paused) await this.initAudio(currentSegmentUrl);
                          if (this.lastPlayPosition > 0) {
                              this.audioCtx.seek(this.lastPlayPosition);
                              this.lastPlayPosition = 0;
                          }
                          this.audioCtx.play();
                          this.setData({ isPlayingSegments: true, isPlaying: true });
                      } catch (error) {
                          console.error('播放音频失败:', error);
                      }
                  };
                  startPlayback();
              }
          }
      }
  },

  async initAudio(url) {
      if (this.audioCtx) { this.audioCtx.stop(); this.audioCtx.destroy(); }
      this.audioCtx = wx.createInnerAudioContext({ obeyMuteSwitch: false });
      this.audioCtx.preservesPitch = true;
      this.audioCtx.playbackRate = this.data.playbackRate;
      this.audioCtx.src = url;
      this.audioCtx.onPlay(() => this.setData({ isPlaying: true, isLoading: false }));
      this.audioCtx.onPause(() => this.setData({ isPlaying: false }));
      this.audioCtx.onStop(() => this.setData({ isPlaying: false }));
      this.audioCtx.onEnded(() => { this._isPlaying = false; this.playNextSegment(); });
      this.audioCtx.onError(res => { console.error('音频加载失败:', res.errMsg); this.playNextSegment(); });
      this.audioCtx.onTimeUpdate(() => {
          if (this.isPageUnloaded) return;
          const segmentCurrentTime = this.audioCtx.currentTime;
          const currentIndex = this.data.currentSegmentIndex;
          const previousSegmentsDuration = this.data.segmentDurations.slice(0, currentIndex).reduce((acc, cur) => acc + (cur || 0), 0);
          const overallTime = previousSegmentsDuration + segmentCurrentTime;
          const dataToUpdate = { currentTime: this.formatTime(overallTime) };
          if (this.data.showProgress && this.data.totalDuration > 0) {
              dataToUpdate.progress = (overallTime / this.data.totalDuration) * 100;
          }
          this.setData(dataToUpdate);
      });
      return new Promise((resolve, reject) => {
          this.audioCtx.onCanplay(() => resolve());
          this.audioCtx.onError(res => reject(res.errMsg));
      });
  },

  async recordStoryHistory(storyId) {
    try {
      const openid = wx.getStorageSync('openid');
      if (!openid) return;
      const db = wx.cloud.database();
      const _ = db.command;
      const res = await db.collection('myStories').where({ _openid: openid }).get();
      const timestamp = new Date();
      const historyItem = { storyId: storyId, timestamp: timestamp };
      if (res.data.length === 0) {
        await db.collection('myStories').add({ data: { historyStories: [historyItem], likedStories: [], createTime: new Date(), updateTime: new Date() } });
      } else {
        const userRecord = res.data[0];
        if (userRecord.historyId) {
          await db.collection('myStories').doc(userRecord._id).update({ data: { historyStories: [historyItem], historyId: null, updateTime: new Date() } });
        } else {
          const historyStories = userRecord.historyStories || [];
          const existingIndex = historyStories.findIndex(item => item.storyId === storyId);
          if (existingIndex === -1) {
            await db.collection('myStories').doc(userRecord._id).update({ data: { historyStories: _.push(historyItem), updateTime: new Date() } });
          } else {
            historyStories.splice(existingIndex, 1);
            historyStories.push(historyItem);
            await db.collection('myStories').doc(userRecord._id).update({ data: { historyStories: historyStories, updateTime: new Date() } });
          }
        }
      }
    } catch (err) { console.error('记录历史失败', err); }
  },

  async checkIfLiked(storyId) {
    try {
      const openid = wx.getStorageSync('openid');
      if (!openid) return;
      const db = wx.cloud.database();
      const res = await db.collection('myStories').where({ _openid: openid }).get();
      let isLiked = false;
      if (res.data && res.data.length > 0) {
        const hasOldLike = res.data.some(item => item.likedId === storyId);
        const hasNewLike = res.data.some(item => item.likedStories && item.likedStories.some(story => story.storyId === storyId));
        isLiked = hasOldLike || hasNewLike;
      }
      this.setData({ isLiked: isLiked });
    } catch (err) { console.error('检查收藏状态失败', err); }
  },

  async toggleLike() {
    try {
      const { storyId } = this.data;
      const openid = wx.getStorageSync('openid');
      if (!openid) { wx.showToast({ title: '请先登录', icon: 'none' }); return; }
      const db = wx.cloud.database();
      const _ = db.command;
      const res = await db.collection('myStories').where({ _openid: openid }).get();
      if (this.data.isLiked) {
        if (res.data.length > 0) {
          const userRecord = res.data[0];
          if (userRecord.likedId === storyId) {
            await db.collection('myStories').doc(userRecord._id).update({ data: { likedId: null, updateTime: new Date() } });
          } else {
            const likedStories = userRecord.likedStories || [];
            const existingIndex = likedStories.findIndex(item => item.storyId === storyId);
            if (existingIndex !== -1) {
              likedStories.splice(existingIndex, 1);
              await db.collection('myStories').doc(userRecord._id).update({ data: { likedStories: likedStories, updateTime: new Date() } });
            }
          }
        }
        this.setData({ isLiked: false });
        wx.showToast({ title: '已取消收藏', icon: 'success' });
        return;
      }
      const permission = require('../../utils/permission.js');
      const checkResult = await permission.checkPermission(permission.PERMISSION_TYPES.FAVORITE);
      if (!checkResult.hasPermission) { permission.showVipModal(checkResult.message); return; }
      const timestamp = new Date();
      const likedItem = { storyId: storyId, timestamp: timestamp };
      if (res.data.length === 0) {
        await db.collection('myStories').add({ data: { likedStories: [likedItem], historyStories: [], createTime: new Date(), updateTime: new Date() } });
        this.setData({ isLiked: true });
        permission.updateUsageRecord(permission.PERMISSION_TYPES.FAVORITE);
        wx.showToast({ title: '收藏成功', icon: 'success' });
      } else {
        const userRecord = res.data[0];
        if (userRecord.likedId) {
          await db.collection('myStories').doc(userRecord._id).update({ data: { likedStories: [likedItem], likedId: null, updateTime: new Date() } });
        } else {
          await db.collection('myStories').doc(userRecord._id).update({ data: { likedStories: _.push(likedItem), updateTime: new Date() } });
        }
        this.setData({ isLiked: true });
        permission.updateUsageRecord(permission.PERMISSION_TYPES.FAVORITE);
        wx.showToast({ title: '收藏成功', icon: 'success' });
      }
    } catch (err) { console.error('收藏操作失败', err); wx.showToast({ title: '操作失败', icon: 'none' }); }
  },



  checkNetworkStatus() { return new Promise((resolve) => { wx.getNetworkType({ success: (res) => resolve(res.networkType), fail: () => resolve('none') }); }); },
  toggleSpeedSetting() { this.setData({ showSpeedSetting: !this.data.showSpeedSetting }); },
  setPlaybackRate(e) {
      const rate = parseFloat(e.currentTarget.dataset.rate);
      this.setData({ playbackRate: rate, showSpeedSetting: false });
      wx.setStorageSync('playbackRate', rate);
      if (this.audioCtx) {
          const wasPlaying = this.data.isPlaying;
          const position = this.audioCtx.currentTime;
          this.audioCtx.pause();
          this.audioCtx.preservesPitch = true;
          this.audioCtx.playbackRate = rate;
          setTimeout(() => {
              if (this.audioCtx) {
                  this.audioCtx.seek(position);
                  if (wasPlaying) { this.audioCtx.play(); }
              }
          }, 100);
      }
  },

 // 修复后的 getAudioDuration 函数，增强 iOS 兼容性
getAudioDuration(url) {
  return new Promise((resolve) => {
      const audio = wx.createInnerAudioContext();
      audio.src = url;
      let resolved = false;
      
      // 设置超时时间，防止无限等待
      const timeout = setTimeout(() => {
          if (!resolved) {
              resolved = true;
              audio.destroy();
              console.warn('获取音频时长超时，使用默认值');
              resolve(10);
          }
      }, 8000); // 增加超时时间到8秒，给iOS更多时间加载

      // iOS兼容性：监听多个事件来获取时长
      const tryGetDuration = () => {
          if (resolved) return;
          
          const duration = audio.duration;
          if (duration && !isNaN(duration) && duration > 0) {
              clearTimeout(timeout);
              resolved = true;
              audio.destroy();
              resolve(duration);
              return true;
          }
          return false;
      };

      // onCanplay 事件 - 主要事件
      audio.onCanplay(() => {
          if (tryGetDuration()) return;
          
          // iOS上可能需要稍微延迟再尝试
          setTimeout(() => {
              tryGetDuration();
          }, 100);
      });

      // onLoadedMetadata 事件 - iOS上更可靠
      audio.onLoadedMetadata && audio.onLoadedMetadata(() => {
          tryGetDuration();
      });

      // onTimeUpdate 事件 - 作为备选方案
      let timeUpdateCount = 0;
      audio.onTimeUpdate(() => {
          timeUpdateCount++;
          // 只在前几次timeUpdate时尝试，避免性能问题
          if (timeUpdateCount <= 3) {
              tryGetDuration();
          }
      });

      // 错误处理
      audio.onError((err) => {
          if (!resolved) {
              clearTimeout(timeout);
              resolved = true;
              audio.destroy();
              console.error('获取音频时长失败:', err);
              resolve(10); // 返回默认时长
          }
      });

      // 对于某些iOS版本，直接尝试播放一小段来触发元数据加载
      // 但立即暂停以避免实际播放
      setTimeout(() => {
          if (!resolved && audio) {
              try {
                  audio.play();
                  setTimeout(() => {
                      if (audio && !resolved) {
                          audio.pause();
                          tryGetDuration();
                      }
                  }, 50);
              } catch (e) {
                  console.warn('尝试播放获取时长时出错:', e);
              }
          }
      }, 200);
  });
},

// 可选：添加一个备用的批量获取时长的方法，提高效率
async getBatchAudioDurations(urls) {
  const durations = [];
  for (let i = 0; i < urls.length; i++) {
      try {
          const duration = await this.getAudioDuration(urls[i]);
          durations.push(duration);
      } catch (error) {
          console.error(`获取第${i}个音频时长失败:`, error);
          durations.push(10); // 使用默认值
      }
      
      // 添加小延迟，避免同时创建过多音频上下文
      if (i < urls.length - 1) {
          await new Promise(resolve => setTimeout(resolve, 100));
      }
  }
  return durations;
},

  getSegmentByProgress(progress) {
      if (!this.data.segmentDurations.length) return { index: 0, time: 0 };
      const totalTime = (progress / 100) * this.data.totalDuration;
      let accumulatedTime = 0;
      for (let i = 0; i < this.data.segmentDurations.length; i++) {
          const segmentDuration = this.data.segmentDurations[i] || 0;
          if (accumulatedTime + segmentDuration >= totalTime) {
              return { index: i, time: totalTime - accumulatedTime };
          }
          accumulatedTime += segmentDuration;
      }
      const lastIndex = this.data.segmentDurations.length - 1;
      return { index: lastIndex, time: this.data.segmentDurations[lastIndex] || 0 };
  },

  onSliderChange(e) {
    const progress = e.detail.value;

    // 使用新的音频管理器
    if (this.audioManager && this.data.totalDuration > 0) {
      const targetTime = (progress / 100) * this.data.totalDuration;
      console.log(`进度条拖动到: ${progress}%, 目标时间: ${targetTime}秒`);
      this.audioManager.seekToTime(targetTime);
      return;
    }

    // 兼容旧的播放逻辑
    if (!this.data.showProgress || !this.audioCtx) return;

    if (this.data.audioStatus === 'playing' && this.audioCtx.duration) {
        // 硅基流动流式播放模式 - 直接控制单个音频文件
        const targetTime = (progress / 100) * this.audioCtx.duration;
        this.audioCtx.seek(targetTime);
        this.setData({
            progress: progress,
            currentTime: this.formatTime(targetTime)
        });
    } else if (this.data.audioSegments.length > 0) {
        // 传统分段播放模式
        const { index: targetSegment, time: segmentTime } = this.getSegmentByProgress(progress);
        const totalTime = (progress / 100) * this.data.totalDuration;
        this.setData({ currentTime: this.formatTime(totalTime), progress: progress });

        if (this.data.currentSegmentIndex !== targetSegment) {
            this.setData({ currentSegmentIndex: targetSegment }, async () => {
                await this.initAudio(this.data.segmentUrls[targetSegment]);
                this.audioCtx.seek(targetTime);
                if (this.data.isPlayingSegments) this.audioCtx.play();
            });
        } else {
            if (this.audioCtx) this.audioCtx.seek(segmentTime);
        }
    }
  },

  onBack() { wx.navigateBack({ delta: 1 }); },

  async onAITap() {
    if (!this.data.answer) { wx.showToast({ title: '本故事没有思考问题', icon: 'none' }); return; }
    const permission = require('../../utils/permission.js');
    const checkResult = await permission.checkPermission(permission.PERMISSION_TYPES.VIEW_ANSWER);
    if (!checkResult.hasPermission) { permission.showVipModal(checkResult.message); return; }
    await permission.updateUsageRecord(permission.PERMISSION_TYPES.VIEW_ANSWER);
    wx.showModal({ title: '问题答案', content: this.data.answer, showCancel: false, confirmText: '我知道了' });
  },

  formatTime(seconds) {
      const minutes = Math.floor(seconds / 60);
      const remainingSeconds = Math.floor(seconds % 60);
      return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
  }
});