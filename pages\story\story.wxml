<view class="story-container">
  <scroll-view class="story-content" scroll-y>
    <!-- 标题已经显示 -->
    <view class="story-title">{{title}}</view>
    <text user-select="true" class="story-subtitle">{{subtitle}}</text>
    
    <!-- 故事配图区域 -->
    <view class="story-image-container {{!image ? 'default-container-style' : ''}}">
      <image class="story-image {{!image ? 'default-image-style' : ''}}" src="{{image || '/images/default_story.png'}}" mode="aspectFill" lazy-load="true"></image>
    </view>
    
    <!-- 内容显示区域 -->
    <block wx:if="{{content}}">
      <text user-select="true" class="story-body">{{content}}</text>
    </block>
    <block wx:else>
      <view class="loading-hint">AI正在努力编写故事，请稍候…</view>
    </block>
    
    <!-- 添加问题显示区域 -->
    <view class="story-question" wx:if="{{question}}">
      <view class="question-title">思考问题：</view>
      <text user-select="true" class="question-text">{{question}}</text>
    </view>
  </scroll-view>
  
  <view class="player-bar">
    <block wx:if="{{audioStatus === 'generating'}}">
      <view class="audio-generating-hint">
        <text>AI正在合成“{{voiceName}}”的声音，请稍候...</text>
      </view>
    </block>
    <block wx:else>
      <view class="progress-bar" wx:if="{{showProgress}}">
        <slider value="{{progress}}" bindchange="onSliderChange" activeColor="#4CAF50" backgroundColor="#E0E0E0" block-size="12"/>
        <view class="time-info">
          <text>{{currentTime}}</text>
          <text>{{duration}}</text>
        </view>
      </view>
    </block>
    <view class="control-buttons">
      <image class="back-btn" src="/images/back.png" mode="aspectFit" bindtap="onBack"/>
      <image class="like-btn" wx:if="{{status === 'completed'}}" src="{{isLiked ? '/images/like_filled.png' : '/images/like.png'}}" mode="aspectFit" bindtap="toggleLike"/>
       <block wx:if="{{isLoading && audioSegments.length === 0}}">
        <image class="loading-icon" src="/images/loading.png" mode="aspectFit"/>
      </block>
      <block wx:else>
        <image class="play-btn" src="{{isPlaying ? '/images/pause.png' : '/images/play.png'}}" mode="aspectFit" bindtap="togglePlay"/>
      </block>
      <image class="answer-btn" wx:if="{{status === 'completed'}}" src="/images/answer.png" mode="aspectFit" bindtap="onAITap"/>
      <image class="speed-btn" src="/images/speed.png" mode="aspectFit" bindtap="toggleSpeedSetting"/>
    </view>
    <!-- 音速设置弹窗 -->
    <view class="speed-setting-popup" wx:if="{{showSpeedSetting}}">
      <view class="speed-options">
        <view class="speed-option {{playbackRate === 0.5 ? 'active' : ''}}" bindtap="setPlaybackRate" data-rate="0.5">最慢</view>
        <view class="speed-option {{playbackRate === 0.75 ? 'active' : ''}}" bindtap="setPlaybackRate" data-rate="0.75">慢一点</view>
        <view class="speed-option {{playbackRate === 1.0 ? 'active' : ''}}" bindtap="setPlaybackRate" data-rate="1.0">正常</view>
        <view class="speed-option {{playbackRate === 1.25 ? 'active' : ''}}" bindtap="setPlaybackRate" data-rate="1.25">快一点</view>
        <view class="speed-option {{playbackRate === 1.5 ? 'active' : ''}}" bindtap="setPlaybackRate" data-rate="1.5">最快</view>
      </view>
    </view>
  </view>
</view>