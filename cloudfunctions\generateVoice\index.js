// 云函数入口文件
const cloud = require('wx-server-sdk');
const axios = require('axios');

cloud.init({ env: cloud.DYNAMIC_CURRENT_ENV });

const db = cloud.database();

// Load configuration from environment variables
const BASE_URL = process.env.BASE_URL || '';
const API_KEY = process.env.API_KEY || '';
const VOICE_BASE_URL = process.env.VOICE_BASE_URL || '';

if (!BASE_URL || !API_KEY || !VOICE_BASE_URL) {
  console.warn('One or more environment variables (BASE_URL, API_KEY, VOICE_BASE_URL) are not set.');
}

// Fetches the voice URI from the database for 'mom' or 'dad' voices.
const getVoiceUri = async (voiceType, openid) => {
  if (voiceType !== 'mom' && voiceType !== 'dad') {
    return ''; // No URI needed for default voices
  }

  const userInfo = await db.collection('baby_info').where({ _openid: openid }).get();
  if (!userInfo.data || userInfo.data.length === 0) {
    throw new Error('User info not found, cannot get voice URI.');
  }

  const babyInfo = userInfo.data[0];
  const uriField = `${voiceType}Uri`; // e.g., momUri or dadUri
  const voiceUri = babyInfo[uriField];

  if (!voiceUri) {
    throw new Error(`Voice URI for '${voiceType}' not found. Please upload a voice sample.`);
  }
  return voiceUri;
};

// Calls the external API to generate the voice audio.
const callWorkflowApi = async (text, voiceType, voiceUri, user) => {
  if (!BASE_URL || !API_KEY) {
    throw new Error('API configuration (BASE_URL, API_KEY) is missing.');
  }
  const url = `${BASE_URL}/workflows/run`;
  const headers = {
    'Authorization': `Bearer ${API_KEY}`,
    'Content-Type': 'application/json',
  };

  const voiceTypeMapping = {
    'auntie': 'female',
    'uncle': 'male',
    'mom': 'female',
    'dad': 'male'
  };

  const inputs = {
    text: text,
    voiceType: voiceTypeMapping[voiceType] || voiceType,
  };

  // Only add voiceUri if it's a custom voice (mom/dad)
  if (voiceUri) {
    inputs.voiceUri = voiceUri;
  }

  const data = {
    user: user,
    workflow_id: 'story_voice',
    inputs: inputs,
    response_mode: 'blocking',
  };

  console.log(`Calling Workflow API with data: ${JSON.stringify(data)}`);

  try {
    const response = await axios.post(url, data, { headers, timeout: 90000 });
    console.log('Workflow API call successful.');
    return response.data;
  } catch (error) {
    const errorMsg = error.response ? JSON.stringify(error.response.data) : error.message;
    console.error(`Workflow API call failed: ${errorMsg}`);
    throw new Error(`Failed to call workflow API: ${errorMsg}`);
  }
};

// Downloads the generated audio and uploads it to cloud storage.
const processAudioResponse = async (response, storyId) => {
  if (!response.data || !response.data.outputs || !response.data.outputs.file || response.data.outputs.file.length === 0) {
    throw new Error('Invalid API response: No audio file found.');
  }

  let audioUrl = response.data.outputs.file[0].url;
  if (audioUrl.startsWith('/')) {
    if (!VOICE_BASE_URL) {
      throw new Error('VOICE_BASE_URL is not configured to resolve relative audio URL.');
    }
    audioUrl = VOICE_BASE_URL + audioUrl;
  }

  console.log(`Downloading audio from: ${audioUrl}`);
  const audioResponse = await axios.get(audioUrl, {
    responseType: 'arraybuffer',
    headers: { 'Authorization': `Bearer ${API_KEY}` },
    timeout: 120000,
  });

  const audioBuffer = Buffer.from(audioResponse.data);
  const cloudPath = `stories/${storyId}/audio_${Date.now()}.mp3`;

  console.log(`Uploading audio to cloud storage at: ${cloudPath}`);
  const uploadResult = await cloud.uploadFile({
    cloudPath,
    fileContent: audioBuffer,
  });

  return uploadResult.fileID;
};

// Main cloud function entry point.
exports.main = async (event, context) => {
  const { storyId, text, voice, openid } = event; // Receive openid from the caller

  if (!storyId || !text || !voice || !openid) {
    return { errCode: 1, errMsg: 'Missing required parameters: storyId, text, voice, or openid.' };
  }

  try {
    console.log(`Generating voice for storyId: ${storyId}, voice: ${voice}`);
    
    const voiceUri = await getVoiceUri(voice, openid);
    
    const workflowResult = await callWorkflowApi(text, voice, voiceUri, openid);
    
    const fileID = await processAudioResponse(workflowResult, storyId);

    return {
      errCode: 0,
      audioUrl: fileID, // Return the fileID in the 'audioUrl' field to match expectations
      message: 'Audio segment generated successfully.',
    };
  } catch (error) {
    console.error('Error in generateVoice function:', error);
    return {
      errCode: 500,
      errMsg: `Failed to generate voice: ${error.message}`,
      error: {
        message: error.message,
        stack: error.stack,
      },
    };
  }
};