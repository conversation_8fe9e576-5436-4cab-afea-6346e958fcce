// 云函数入口文件
const cloud = require('wx-server-sdk')

cloud.init({ env: cloud.DYNAMIC_CURRENT_ENV });

const db = cloud.database()

// 云函数入口函数
exports.main = async (event, context) => {
  console.log('接收到的数据：', event)
  
  if (!event.nickname || !event.gender || !event.birthday) {
    return {
      success: false,
      error: '缺少必要的信息'
    }
  }

  const wxContext = cloud.getWXContext()
  const openid = wxContext.OPENID

  try {
    // 检查用户是否存在于users集合中，如果不存在则创建一个新记录
    const userResult = await db.collection('users').where({
      _openid: openid
    }).get()
    
    // 如果用户不存在，创建一个新的用户记录，vipExpireDate为空
    if (!userResult.data || userResult.data.length === 0) {
      await db.collection('users').add({
        data: {
          _openid: openid,
          createTime: db.serverDate(),
          updateTime: db.serverDate(),
          // vipExpireDate字段不设置，保持为空
        }
      })
      console.log('已为新用户创建users记录')
    }
    
    // 获取宝宝信息
    const { data } = await db.collection('baby_info').where({
      _openid: openid
    }).get()

    let result
    if (data && data.length > 0) {
      result = await db.collection('baby_info').where({
        _openid: openid
      }).update({
        data: {
          nickname: event.nickname,
          gender: event.gender,
          birthday: event.birthday,
          voiceType: event.voiceType,
          voiceUrl: event.voiceUrl || '',
          momVoiceUrl: event.momVoiceUrl || '',
          dadVoiceUrl: event.dadVoiceUrl || '',
          momUri: event.momUri || '',
          dadUri: event.dadUri || '',
          avatarUrl: event.avatarUrl || '',
          updateTime: db.serverDate()
        }
      })
    } else {
      result = await db.collection('baby_info').add({
        data: {
          _openid: openid,
          nickname: event.nickname,
          gender: event.gender,
          birthday: event.birthday,
          voiceType: event.voiceType,
          voiceUrl: event.voiceUrl || '',
          momVoiceUrl: event.momVoiceUrl || '',
          dadVoiceUrl: event.dadVoiceUrl || '',
          momUri: event.momUri || '',
          dadUri: event.dadUri || '',
          avatarUrl: event.avatarUrl || '',
          createTime: db.serverDate(),
          updateTime: db.serverDate()
        }
      })
    }

    console.log('保存结果：', result)

    if (result && result._id) {
      return {
        success: true,
        data: result
      }
    } else {
      return {
        success: false,
        error: '保存失败'
      }
    }

  } catch (error) {
    console.error('保存失败：', error)
    return {
      success: false,
      error: error.message || '保存失败'
    }
  }
}
