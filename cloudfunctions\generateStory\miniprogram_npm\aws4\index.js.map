{"version": 3, "sources": ["aws4.js", "lru.js"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;AACA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.js", "sourcesContent": ["var aws4 = exports,\n    url = require('url'),\n    querystring = require('querystring'),\n    crypto = require('crypto'),\n    lru = require('./lru'),\n    credentialsCache = lru(1000)\n\n// http://docs.amazonwebservices.com/general/latest/gr/signature-version-4.html\n\nfunction hmac(key, string, encoding) {\n  return crypto.createHmac('sha256', key).update(string, 'utf8').digest(encoding)\n}\n\nfunction hash(string, encoding) {\n  return crypto.createHash('sha256').update(string, 'utf8').digest(encoding)\n}\n\n// This function assumes the string has already been percent encoded\nfunction encodeRfc3986(urlEncodedString) {\n  return urlEncodedString.replace(/[!'()*]/g, function(c) {\n    return '%' + c.charCodeAt(0).toString(16).toUpperCase()\n  })\n}\n\nfunction encodeRfc3986Full(str) {\n  return encodeRfc3986(encodeURIComponent(str))\n}\n\n// A bit of a combination of:\n// https://github.com/aws/aws-sdk-java-v2/blob/dc695de6ab49ad03934e1b02e7263abbd2354be0/core/auth/src/main/java/software/amazon/awssdk/auth/signer/internal/AbstractAws4Signer.java#L59\n// https://github.com/aws/aws-sdk-js/blob/****************************************/lib/signers/v4.js#L191-L199\n// https://github.com/mhart/aws4fetch/blob/b3aed16b6f17384cf36ea33bcba3c1e9f3bdfefd/src/main.js#L25-L34\nvar HEADERS_TO_IGNORE = {\n  'authorization': true,\n  'connection': true,\n  'x-amzn-trace-id': true,\n  'user-agent': true,\n  'expect': true,\n  'presigned-expires': true,\n  'range': true,\n}\n\n// request: { path | body, [host], [method], [headers], [service], [region] }\n// credentials: { accessKeyId, secretAccessKey, [sessionToken] }\nfunction RequestSigner(request, credentials) {\n\n  if (typeof request === 'string') request = url.parse(request)\n\n  var headers = request.headers = Object.assign({}, (request.headers || {})),\n      hostParts = (!this.service || !this.region) && this.matchHost(request.hostname || request.host || headers.Host || headers.host)\n\n  this.request = request\n  this.credentials = credentials || this.defaultCredentials()\n\n  this.service = request.service || hostParts[0] || ''\n  this.region = request.region || hostParts[1] || 'us-east-1'\n\n  // SES uses a different domain from the service name\n  if (this.service === 'email') this.service = 'ses'\n\n  if (!request.method && request.body)\n    request.method = 'POST'\n\n  if (!headers.Host && !headers.host) {\n    headers.Host = request.hostname || request.host || this.createHost()\n\n    // If a port is specified explicitly, use it as is\n    if (request.port)\n      headers.Host += ':' + request.port\n  }\n  if (!request.hostname && !request.host)\n    request.hostname = headers.Host || headers.host\n\n  this.isCodeCommitGit = this.service === 'codecommit' && request.method === 'GIT'\n\n  this.extraHeadersToIgnore = request.extraHeadersToIgnore || Object.create(null)\n  this.extraHeadersToInclude = request.extraHeadersToInclude || Object.create(null)\n}\n\nRequestSigner.prototype.matchHost = function(host) {\n  var match = (host || '').match(/([^\\.]{1,63})\\.(?:([^\\.]{0,63})\\.)?amazonaws\\.com(\\.cn)?$/)\n  var hostParts = (match || []).slice(1, 3)\n\n  // ES's hostParts are sometimes the other way round, if the value that is expected\n  // to be region equals ‘es’ switch them back\n  // e.g. search-cluster-name-aaaa00aaaa0aaa0aaaaaaa0aaa.us-east-1.es.amazonaws.com\n  if (hostParts[1] === 'es' || hostParts[1] === 'aoss')\n    hostParts = hostParts.reverse()\n\n  if (hostParts[1] == 's3') {\n    hostParts[0] = 's3'\n    hostParts[1] = 'us-east-1'\n  } else {\n    for (var i = 0; i < 2; i++) {\n      if (/^s3-/.test(hostParts[i])) {\n        hostParts[1] = hostParts[i].slice(3)\n        hostParts[0] = 's3'\n        break\n      }\n    }\n  }\n\n  return hostParts\n}\n\n// http://docs.aws.amazon.com/general/latest/gr/rande.html\nRequestSigner.prototype.isSingleRegion = function() {\n  // Special case for S3 and SimpleDB in us-east-1\n  if (['s3', 'sdb'].indexOf(this.service) >= 0 && this.region === 'us-east-1') return true\n\n  return ['cloudfront', 'ls', 'route53', 'iam', 'importexport', 'sts']\n    .indexOf(this.service) >= 0\n}\n\nRequestSigner.prototype.createHost = function() {\n  var region = this.isSingleRegion() ? '' : '.' + this.region,\n      subdomain = this.service === 'ses' ? 'email' : this.service\n  return subdomain + region + '.amazonaws.com'\n}\n\nRequestSigner.prototype.prepareRequest = function() {\n  this.parsePath()\n\n  var request = this.request, headers = request.headers, query\n\n  if (request.signQuery) {\n\n    this.parsedPath.query = query = this.parsedPath.query || {}\n\n    if (this.credentials.sessionToken)\n      query['X-Amz-Security-Token'] = this.credentials.sessionToken\n\n    if (this.service === 's3' && !query['X-Amz-Expires'])\n      query['X-Amz-Expires'] = 86400\n\n    if (query['X-Amz-Date'])\n      this.datetime = query['X-Amz-Date']\n    else\n      query['X-Amz-Date'] = this.getDateTime()\n\n    query['X-Amz-Algorithm'] = 'AWS4-HMAC-SHA256'\n    query['X-Amz-Credential'] = this.credentials.accessKeyId + '/' + this.credentialString()\n    query['X-Amz-SignedHeaders'] = this.signedHeaders()\n\n  } else {\n\n    if (!request.doNotModifyHeaders && !this.isCodeCommitGit) {\n      if (request.body && !headers['Content-Type'] && !headers['content-type'])\n        headers['Content-Type'] = 'application/x-www-form-urlencoded; charset=utf-8'\n\n      if (request.body && !headers['Content-Length'] && !headers['content-length'])\n        headers['Content-Length'] = Buffer.byteLength(request.body)\n\n      if (this.credentials.sessionToken && !headers['X-Amz-Security-Token'] && !headers['x-amz-security-token'])\n        headers['X-Amz-Security-Token'] = this.credentials.sessionToken\n\n      if (this.service === 's3' && !headers['X-Amz-Content-Sha256'] && !headers['x-amz-content-sha256'])\n        headers['X-Amz-Content-Sha256'] = hash(this.request.body || '', 'hex')\n\n      if (headers['X-Amz-Date'] || headers['x-amz-date'])\n        this.datetime = headers['X-Amz-Date'] || headers['x-amz-date']\n      else\n        headers['X-Amz-Date'] = this.getDateTime()\n    }\n\n    delete headers.Authorization\n    delete headers.authorization\n  }\n}\n\nRequestSigner.prototype.sign = function() {\n  if (!this.parsedPath) this.prepareRequest()\n\n  if (this.request.signQuery) {\n    this.parsedPath.query['X-Amz-Signature'] = this.signature()\n  } else {\n    this.request.headers.Authorization = this.authHeader()\n  }\n\n  this.request.path = this.formatPath()\n\n  return this.request\n}\n\nRequestSigner.prototype.getDateTime = function() {\n  if (!this.datetime) {\n    var headers = this.request.headers,\n      date = new Date(headers.Date || headers.date || new Date)\n\n    this.datetime = date.toISOString().replace(/[:\\-]|\\.\\d{3}/g, '')\n\n    // Remove the trailing 'Z' on the timestamp string for CodeCommit git access\n    if (this.isCodeCommitGit) this.datetime = this.datetime.slice(0, -1)\n  }\n  return this.datetime\n}\n\nRequestSigner.prototype.getDate = function() {\n  return this.getDateTime().substr(0, 8)\n}\n\nRequestSigner.prototype.authHeader = function() {\n  return [\n    'AWS4-HMAC-SHA256 Credential=' + this.credentials.accessKeyId + '/' + this.credentialString(),\n    'SignedHeaders=' + this.signedHeaders(),\n    'Signature=' + this.signature(),\n  ].join(', ')\n}\n\nRequestSigner.prototype.signature = function() {\n  var date = this.getDate(),\n      cacheKey = [this.credentials.secretAccessKey, date, this.region, this.service].join(),\n      kDate, kRegion, kService, kCredentials = credentialsCache.get(cacheKey)\n  if (!kCredentials) {\n    kDate = hmac('AWS4' + this.credentials.secretAccessKey, date)\n    kRegion = hmac(kDate, this.region)\n    kService = hmac(kRegion, this.service)\n    kCredentials = hmac(kService, 'aws4_request')\n    credentialsCache.set(cacheKey, kCredentials)\n  }\n  return hmac(kCredentials, this.stringToSign(), 'hex')\n}\n\nRequestSigner.prototype.stringToSign = function() {\n  return [\n    'AWS4-HMAC-SHA256',\n    this.getDateTime(),\n    this.credentialString(),\n    hash(this.canonicalString(), 'hex'),\n  ].join('\\n')\n}\n\nRequestSigner.prototype.canonicalString = function() {\n  if (!this.parsedPath) this.prepareRequest()\n\n  var pathStr = this.parsedPath.path,\n      query = this.parsedPath.query,\n      headers = this.request.headers,\n      queryStr = '',\n      normalizePath = this.service !== 's3',\n      decodePath = this.service === 's3' || this.request.doNotEncodePath,\n      decodeSlashesInPath = this.service === 's3',\n      firstValOnly = this.service === 's3',\n      bodyHash\n\n  if (this.service === 's3' && this.request.signQuery) {\n    bodyHash = 'UNSIGNED-PAYLOAD'\n  } else if (this.isCodeCommitGit) {\n    bodyHash = ''\n  } else {\n    bodyHash = headers['X-Amz-Content-Sha256'] || headers['x-amz-content-sha256'] ||\n      hash(this.request.body || '', 'hex')\n  }\n\n  if (query) {\n    var reducedQuery = Object.keys(query).reduce(function(obj, key) {\n      if (!key) return obj\n      obj[encodeRfc3986Full(key)] = !Array.isArray(query[key]) ? query[key] :\n        (firstValOnly ? query[key][0] : query[key])\n      return obj\n    }, {})\n    var encodedQueryPieces = []\n    Object.keys(reducedQuery).sort().forEach(function(key) {\n      if (!Array.isArray(reducedQuery[key])) {\n        encodedQueryPieces.push(key + '=' + encodeRfc3986Full(reducedQuery[key]))\n      } else {\n        reducedQuery[key].map(encodeRfc3986Full).sort()\n          .forEach(function(val) { encodedQueryPieces.push(key + '=' + val) })\n      }\n    })\n    queryStr = encodedQueryPieces.join('&')\n  }\n  if (pathStr !== '/') {\n    if (normalizePath) pathStr = pathStr.replace(/\\/{2,}/g, '/')\n    pathStr = pathStr.split('/').reduce(function(path, piece) {\n      if (normalizePath && piece === '..') {\n        path.pop()\n      } else if (!normalizePath || piece !== '.') {\n        if (decodePath) piece = decodeURIComponent(piece.replace(/\\+/g, ' '))\n        path.push(encodeRfc3986Full(piece))\n      }\n      return path\n    }, []).join('/')\n    if (pathStr[0] !== '/') pathStr = '/' + pathStr\n    if (decodeSlashesInPath) pathStr = pathStr.replace(/%2F/g, '/')\n  }\n\n  return [\n    this.request.method || 'GET',\n    pathStr,\n    queryStr,\n    this.canonicalHeaders() + '\\n',\n    this.signedHeaders(),\n    bodyHash,\n  ].join('\\n')\n}\n\nRequestSigner.prototype.filterHeaders = function() {\n  var headers = this.request.headers,\n      extraHeadersToInclude = this.extraHeadersToInclude,\n      extraHeadersToIgnore = this.extraHeadersToIgnore\n  this.filteredHeaders = Object.keys(headers)\n    .map(function(key) { return [key.toLowerCase(), headers[key]] })\n    .filter(function(entry) {\n      return extraHeadersToInclude[entry[0]] ||\n        (HEADERS_TO_IGNORE[entry[0]] == null && !extraHeadersToIgnore[entry[0]])\n    })\n    .sort(function(a, b) { return a[0] < b[0] ? -1 : 1 })\n}\n\nRequestSigner.prototype.canonicalHeaders = function() {\n  if (!this.filteredHeaders) this.filterHeaders()\n\n  return this.filteredHeaders.map(function(entry) {\n    return entry[0] + ':' + entry[1].toString().trim().replace(/\\s+/g, ' ')\n  }).join('\\n')\n}\n\nRequestSigner.prototype.signedHeaders = function() {\n  if (!this.filteredHeaders) this.filterHeaders()\n\n  return this.filteredHeaders.map(function(entry) { return entry[0] }).join(';')\n}\n\nRequestSigner.prototype.credentialString = function() {\n  return [\n    this.getDate(),\n    this.region,\n    this.service,\n    'aws4_request',\n  ].join('/')\n}\n\nRequestSigner.prototype.defaultCredentials = function() {\n  var env = process.env\n  return {\n    accessKeyId: env.AWS_ACCESS_KEY_ID || env.AWS_ACCESS_KEY,\n    secretAccessKey: env.AWS_SECRET_ACCESS_KEY || env.AWS_SECRET_KEY,\n    sessionToken: env.AWS_SESSION_TOKEN,\n  }\n}\n\nRequestSigner.prototype.parsePath = function() {\n  var path = this.request.path || '/'\n\n  // S3 doesn't always encode characters > 127 correctly and\n  // all services don't encode characters > 255 correctly\n  // So if there are non-reserved chars (and it's not already all % encoded), just encode them all\n  if (/[^0-9A-Za-z;,/?:@&=+$\\-_.!~*'()#%]/.test(path)) {\n    path = encodeURI(decodeURI(path))\n  }\n\n  var queryIx = path.indexOf('?'),\n      query = null\n\n  if (queryIx >= 0) {\n    query = querystring.parse(path.slice(queryIx + 1))\n    path = path.slice(0, queryIx)\n  }\n\n  this.parsedPath = {\n    path: path,\n    query: query,\n  }\n}\n\nRequestSigner.prototype.formatPath = function() {\n  var path = this.parsedPath.path,\n      query = this.parsedPath.query\n\n  if (!query) return path\n\n  // Services don't support empty query string keys\n  if (query[''] != null) delete query['']\n\n  return path + '?' + encodeRfc3986(querystring.stringify(query))\n}\n\naws4.RequestSigner = RequestSigner\n\naws4.sign = function(request, credentials) {\n  return new RequestSigner(request, credentials).sign()\n}\n", "module.exports = function(size) {\n  return new LruCache(size)\n}\n\nfunction LruCache(size) {\n  this.capacity = size | 0\n  this.map = Object.create(null)\n  this.list = new DoublyLinkedList()\n}\n\nLruCache.prototype.get = function(key) {\n  var node = this.map[key]\n  if (node == null) return undefined\n  this.used(node)\n  return node.val\n}\n\nLruCache.prototype.set = function(key, val) {\n  var node = this.map[key]\n  if (node != null) {\n    node.val = val\n  } else {\n    if (!this.capacity) this.prune()\n    if (!this.capacity) return false\n    node = new DoublyLinkedNode(key, val)\n    this.map[key] = node\n    this.capacity--\n  }\n  this.used(node)\n  return true\n}\n\nLruCache.prototype.used = function(node) {\n  this.list.moveToFront(node)\n}\n\nLruCache.prototype.prune = function() {\n  var node = this.list.pop()\n  if (node != null) {\n    delete this.map[node.key]\n    this.capacity++\n  }\n}\n\n\nfunction DoublyLinkedList() {\n  this.firstNode = null\n  this.lastNode = null\n}\n\nDoublyLinkedList.prototype.moveToFront = function(node) {\n  if (this.firstNode == node) return\n\n  this.remove(node)\n\n  if (this.firstNode == null) {\n    this.firstNode = node\n    this.lastNode = node\n    node.prev = null\n    node.next = null\n  } else {\n    node.prev = null\n    node.next = this.firstNode\n    node.next.prev = node\n    this.firstNode = node\n  }\n}\n\nDoublyLinkedList.prototype.pop = function() {\n  var lastNode = this.lastNode\n  if (lastNode != null) {\n    this.remove(lastNode)\n  }\n  return lastNode\n}\n\nDoublyLinkedList.prototype.remove = function(node) {\n  if (this.firstNode == node) {\n    this.firstNode = node.next\n  } else if (node.prev != null) {\n    node.prev.next = node.next\n  }\n  if (this.lastNode == node) {\n    this.lastNode = node.prev\n  } else if (node.next != null) {\n    node.next.prev = node.prev\n  }\n}\n\n\nfunction DoublyLinkedNode(key, val) {\n  this.key = key\n  this.val = val\n  this.prev = null\n  this.next = null\n}\n"]}