// 云函数入口文件
const cloud = require('wx-server-sdk')
const axios = require('axios')

cloud.init({ env: cloud.DYNAMIC_CURRENT_ENV });

const db = cloud.database()
// 获取配置参数
async function getConfig() {
  const res = await db.collection('config').doc('story_config').get()
  return res.data
}

// 云函数入口函数
exports.main = async (event, context) => {
  const { storyId, title, openid } = event
  
  // 参数完整性校验
  if (!storyId || !title) {
    return {
      code: -1,
      data: null,
      message: '参数不完整'
    }
  }

  try {
    // 调用图片生成API
    console.log(`[generateImage] 开始生成图片，title: ${title}`);
    const imageUrl = await generateImage(title);
    
    if (!imageUrl) {
      console.error(`[generateImage] 图片生成失败，未返回URL`);
      throw new Error('图片生成失败');
    }
    console.log(`[generateImage] 外部API返回的图片URL: ${imageUrl}`);

    // 将图片URL保存到故事数据中
    // 注意：这里imageUrl已经是云存储的fileID了，因为generateImage函数内部已经处理了上传
    await updateStoryImage(storyId, imageUrl);
    console.log(`[generateImage] 已调用updateStoryImage更新故事ID ${storyId} 的图片为 ${imageUrl}`);
    
    return {
      code: 0,
      data: { imageUrl },
      message: '图片生成成功'
    }
  } catch (error) {
    console.error('生成图片失败：', error)
    return {
      code: -1,
      data: null,
      message: `生成图片失败: ${error.message}`
    }
  }
}

// 图片生成API调用
const generateImage = async (title) => {
  try {
    // 构建提示词，根据故事标题生成适合儿童的插图
    const config = await getConfig()
    const API_URL = config.API_URL
    const IMAGE_API_KEY = config.IMAGE_API_KEY
    const BASE_URL = config.BASE_URL
    const url = `${API_URL}/workflows/run`
    const headers = {
      'Authorization': `Bearer ${IMAGE_API_KEY}`,
      'Content-Type': 'application/json'
    }
    
    const data = {
      user: 'system',
      workflow_id: 'image_generation',
      inputs: {
        prompt: title
      },
      response_mode: 'blocking'
    }

    console.log('调用图片生成API，参数:', JSON.stringify(data))
    const response = await axios.post(url, data, { headers })
    
    // 打印API响应结构，用于调试
    console.log('图片生成API响应:', JSON.stringify(response.data, null, 2))
    
    // 检查API响应格式，尝试多种可能的响应结构
    let generatedImageUrl = null;
    // 检查workflow输出格式 (outputs.images)
    if (response.data && response.data.data && response.data.data.outputs && 
        response.data.data.outputs.images && response.data.data.outputs.images.length > 0) {
      generatedImageUrl = response.data.data.outputs.images[0].url;
      console.log('从workflow输出格式获取到图片URL');
    } 
    // 检查data.outputs.file格式 (根据日志显示的实际返回格式)
    else if (response.data && response.data.data && response.data.data.outputs && 
        response.data.data.outputs.file) {
      let fileField = response.data.data.outputs.file;
      // 兼容字符串和数组两种情况
      if (typeof fileField === 'string') {
        // 去除首尾空格和反引号
        generatedImageUrl = fileField.trim().replace(/^`|`$/g, '');
        console.log('从data.outputs.file字符串格式获取到图片URL');
      } else if (Array.isArray(fileField) && fileField.length > 0) {
        generatedImageUrl = fileField[0].url;
        console.log('从data.outputs.file数组格式获取到图片URL');
      }
    }
    // 检查直接输出格式 (file数组)
    else if (response.data && response.data.file && response.data.file.length > 0) {
      generatedImageUrl = response.data.file[0].url;
      console.log('从直接输出格式获取到图片URL');
    }
    // 检查task_id格式 (根据日志显示的实际返回格式)
    else if (response.data && response.data.task_id && response.data.data && 
        response.data.data.outputs && response.data.data.outputs.file && 
        response.data.data.outputs.file.length > 0) {
      generatedImageUrl = response.data.data.outputs.file[0].url;
      console.log('从task_id响应格式获取到图片URL');
    }
    // 检查其他可能的输出格式
    else if (response.data && response.data.outputs && response.data.outputs.url) {
      generatedImageUrl = response.data.outputs.url;
      console.log('从outputs.url格式获取到图片URL');
    }
    
    if (generatedImageUrl) {
      // 如果URL是相对路径，添加基础URL前缀
      if (generatedImageUrl.startsWith('/')) {
        generatedImageUrl = BASE_URL + generatedImageUrl;
      } else if (!generatedImageUrl.startsWith('http')) {
        generatedImageUrl = BASE_URL + '/' + generatedImageUrl;
      }
      
      console.log('获取到的图片URL:', generatedImageUrl);
      
      // 将图片上传到云存储
      const imageRes = await uploadImageToCloud(generatedImageUrl, title);
      console.log(`[generateImage] 图片上传到云存储成功，fileID: ${imageRes.fileID}`);
      return imageRes.fileID;
    } else {
      console.error('无法从API响应中提取图片URL，完整响应:', JSON.stringify(response.data));
      throw new Error('API返回格式错误或未包含图片URL');
    }
  } catch (error) {
    console.error('图片生成API调用失败：', error.response?.data || error.message);
    throw new Error('图片生成服务暂时不可用');
  }
}

// 将图片上传到云存储
const uploadImageToCloud = async (imageUrl, title) => {
  try {
    console.log('开始下载图片:', imageUrl)
    // 下载图片，添加必要的请求头
    const config = await getConfig()
    const IMAGE_API_KEY = config.IMAGE_API_KEY
    const result = await axios.get(imageUrl, { 
      responseType: 'arraybuffer',
      headers: {
        'Authorization': `Bearer ${IMAGE_API_KEY}`,
        'Accept': '*/*'
      },
      timeout: 120000 // 120秒超时
    })
    const buffer = Buffer.from(result.data, 'binary')
    
    // 生成唯一文件名
    const timestamp = Date.now()
    const fileName = `images/users/${timestamp}_${title.substring(0, 10)}.jpg`
    
    // 上传到云存储
    return await cloud.uploadFile({
      cloudPath: fileName,
      fileContent: buffer,
    })
  } catch (error) {
    console.error('上传图片到云存储失败：', error)
    throw new Error('图片上传失败')
  }
}

// 更新故事图片URL
const updateStoryImage = async (storyId, imageUrl) => {
  console.log(`[updateStoryImage] 尝试更新故事 ${storyId} 的图片为: ${imageUrl}`);
  try {
    // 先获取当前故事状态，确保不会覆盖已经设置好的状态
    const { data: storyData } = await db.collection('stories').doc(storyId).get()
    
    // 只更新图片URL和更新时间，不修改status和audioStatus
    await db.collection('stories').doc(storyId).update({
      data: {
        image: imageUrl,
        updateTime: db.serverDate()
      }
    })
    console.log(`[updateStoryImage] 故事 ${storyId} 的图片更新成功`);
    
    return true
  } catch (error) {
    console.error(`[updateStoryImage] 更新故事图片失败: ${error}`);
    // 捕获错误但不抛出，避免因图片更新失败而影响整个流程
    // 记录错误但返回成功
    return true
  }
}