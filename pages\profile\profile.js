// pages/profile/profile.js
Page({
  data: {
    menuTop: 0,
    menuHeight: 0,
    babyInfo: {
      avatarUrl: '',
      nickname: '',
      gender: '',
      birthday: '',
      voice: ''
    },
    voiceTypes: {
      'mom': '妈妈',
      'dad': '爸爸',
      'female': '阿姨',
      'male': '叔叔'
    },
    today: new Date().toISOString().split('T')[0]
  },

  onLoad() {
    const app = getApp();
    this.setData({
      menuTop: app.globalData.menuTop,
      menuHeight: app.globalData.menuHeight,
    });
    this.getBabyInfo();
  },

  onShow() {
    // 每次显示页面时重新获取宝宝信息，以便显示最新修改
    this.getBabyInfo();
    this.getUserVipInfo();
  },

  // 获取宝宝信息
  getBabyInfo() {
    // 先尝试从云端获取
    wx.cloud.callFunction({
      name: 'getBabyInfo',
      success: res => {
        if (res.result.code === 0 && res.result.data && res.result.data.length > 0) {
          const babyInfo = res.result.data[0];
          this.setData({
            babyInfo: babyInfo
          });
          // 同步到本地存储
          wx.setStorageSync('babyInfo', babyInfo);
        } else {
          // 如果云端没有，则从本地获取
          const babyInfo = wx.getStorageSync('babyInfo');
          if (babyInfo) {
            this.setData({
              babyInfo: babyInfo
            });
          }
        }
      },
      fail: err => {
        console.error('获取宝宝信息失败', err);
        // 从本地获取
        const babyInfo = wx.getStorageSync('babyInfo');
        if (babyInfo) {
          this.setData({
            babyInfo: babyInfo
          });
        }
      }
    });
  },

  //获取会员信息
  getUserVipInfo() {
    // 获取用户openid
    const openid = wx.getStorageSync('openid');
    if (!openid) {
      console.error('未找到用户openid');
      this.setData({
        vipStatus: 'never',
        buttonText: '立即开通'
      });
      return;
    }

    // 调用云函数获取用户信息，如果用户不存在会自动创建
    wx.cloud.callFunction({
      name: 'getUserInfo'
    }).then(res => {
      if (res.result.code !== 0 || !res.result.data) {
        console.log('获取用户信息失败:', res.result.message);
        this.setData({
          vipStatus: 'never',
          buttonText: '立即开通'
        });
        return;
      }

      const user = res.result.data;
      // 检查user对象是否存在
      if (!user) {
        console.error('用户数据为空');
        this.setData({
          vipStatus: 'never',
          buttonText: '立即开通'
        });
        return;
      }

      // 检查vipExpireDate属性
      if (!user.vipExpireDate) {
        // vipExpireDate为空，说明从未开通过会员
        this.setData({
          vipStatus: 'never',
          buttonText: '立即开通'
        });
      } else {
        const currentDate = new Date();
        const expireDate = new Date(user.vipExpireDate);
        
        // 将当前日期和到期日期都设置为当天的0点，只比较日期而不比较具体时间
        currentDate.setHours(0, 0, 0, 0);
        expireDate.setHours(0, 0, 0, 0);
        
        if (expireDate >= currentDate) {
          // 会员未过期（包括当天到期的情况）
          this.setData({
            vipStatus: 'active',
            vipExpireDate: this.formatDate(expireDate),
            buttonText: '立即续费'
          });
        } else {
          // 会员已过期
          this.setData({
            vipStatus: 'expired',
            vipExpireDate: this.formatDate(expireDate),
            buttonText: '立即续费'
          });
        }
      }
    }).catch(err => {
      console.error('获取会员信息失败', err);
      // 发生错误时设置默认状态
      this.setData({
        vipStatus: 'never',
        buttonText: '立即开通'
      });
    });

  },

  // 格式化日期
  formatDate(date) {
    const year = date.getFullYear();
    const month = date.getMonth() + 1;
    const day = date.getDate();
    return `${year}-${month}-${day}`;
  },

  // 选择头像
  onChooseAvatar(e) {
    const avatarUrl = e.detail.avatarUrl;
    // 上传头像到云存储
    wx.cloud.uploadFile({
      cloudPath: `avatars/${Date.now()}.jpg`,
      filePath: avatarUrl,
      success: res => {
        const fileID = res.fileID;
        // 更新宝宝信息
        const babyInfo = this.data.babyInfo;
        babyInfo.avatarUrl = fileID;
        this.setData({ babyInfo });
        wx.setStorageSync('babyInfo', babyInfo);

        // 保存到云数据库
        wx.cloud.callFunction({
          name: 'saveBabyInfo',
          data: babyInfo,
          success: () => {
            wx.showToast({
              title: '头像更新成功',
              icon: 'success'
            });
          },
          fail: err => {
            console.error('保存头像失败', err);
            wx.showToast({
              title: '头像保存失败',
              icon: 'none'
            });
          }
        });
      },
      fail: err => {
        console.error('上传头像失败', err);
        wx.showToast({
          title: '上传头像失败',
          icon: 'none'
        });
      }
    });
  },

  // 修改昵称
  onNicknameChange() {
    wx.showModal({
      title: '修改昵称',
      content: '请输入新的昵称',
      editable: true,
      placeholderText: '请输入新的昵称',
      success: (res) => {
        if (res.confirm && res.content) {
          const babyInfo = this.data.babyInfo;
          babyInfo.nickname = res.content;
          this.updateBabyInfo(babyInfo);
        }
      }
    });
  },

  // 编辑宝宝信息
  onEditBabyInfo(e) {
    const type = e.currentTarget.dataset.type;
    
    switch (type) {
      case 'gender':
        this.editBabyGender();
        break;
      case 'birthday':
        this.editBabyBirthday();
        break;
      case 'voice':
        this.editBabyVoice();
        break;
    }
  },

  // 编辑宝宝性别
  editBabyGender() {
    wx.showActionSheet({
      itemList: ['男宝宝', '女宝宝'],
      success: res => {
        const gender = res.tapIndex === 0 ? 'boy' : 'girl';
        const babyInfo = this.data.babyInfo;
        babyInfo.gender = gender;
        this.updateBabyInfo(babyInfo);
      }
    });
  },

  // 编辑宝宝生日
  editBabyBirthday() {
    // 日期选择器已经在WXML中实现，不需要在这里处理
    // 选择后的逻辑在onBirthdayChange中处理
  },

  // 编辑宝宝声音
  editBabyVoice() {
    wx.navigateTo({
      url: '/pages/voicesetting/voicesetting?from=profile'
    });
  },

  // 更新宝宝信息
  updateBabyInfo(babyInfo) {
    // 更新本地数据
    this.setData({
      babyInfo: babyInfo
    });
    
    // 保存到本地存储
    wx.setStorageSync('babyInfo', babyInfo);
    
    // 上传到云端
    wx.cloud.callFunction({
      name: 'saveBabyInfo',
      data: babyInfo,
      success: res => {
        console.log('保存成功', res);
        wx.showToast({
          title: '修改成功',
          icon: 'success'
        });
      },
      fail: err => {
        console.error('保存失败', err);
        wx.showToast({
          title: '保存失败，请重试',
          icon: 'none'
        });
      }
    });
  },

  // 关于我们
  onAbout() {
    wx.navigateTo({
      url: '/pages/about/about'
    });
  },

  // 退出登录
  onLogout() {
    wx.showModal({
      title: '提示',
      content: '确定要退出登录吗？',
      success: (res) => {
        if (res.confirm) {
          // 清除本地存储的用户信息
          wx.clearStorageSync();
          
          // 跳转到登录页面
          wx.reLaunch({
            url: '/pages/login/login'
          });
        }
      }
    });
  },

  onBack() {
    wx.switchTab({
      url: '/pages/home/<USER>'
    });
  },
  
  vipBuy() {
    console.log("立即续费按钮被点击了");
    // 这里可以添加跳转到会员购买页面的逻辑
    wx.navigateTo({
      url: '/pages/vip/vip'
    });
  },
  // 处理生日选择变更
  onBirthdayChange(e) {
    const birthday = e.detail.value;
    const babyInfo = { ...this.data.babyInfo, birthday };
    
    // 更新到云端
    wx.cloud.callFunction({
      name: 'saveBabyInfo',
      data: babyInfo,
      success: res => {
        // 移除不必要的结果码检查，因为云函数调用成功就意味着数据保存成功
        this.setData({ babyInfo });
        wx.setStorageSync('babyInfo', babyInfo);
        wx.showToast({
          title: '生日更新成功',
          icon: 'success'
        });
      },
      fail: err => {
        console.error('更新生日失败：', err);
        wx.showToast({
          title: '更新失败',
          icon: 'error'
        });
      }
    });
  }
})