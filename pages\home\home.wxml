
<view class="container">
  <view class="custom-nav-bar" style="padding-top: {{menuTop}}px; height: {{menuHeight}}px;">

      <image class="avatar-wrapper" src="{{babyInfo.avatarUrl || '/images/default_avatar.png'}}" mode="aspectFill" bindtap="toProfile"  />
            <image class="vip-image" src="{{vipStatus === 'active'? '/images/vip.png' : '/images/not_vip.png'}}" mode="aspectFill" bindtap="vipBuy"  />
      <view class="nav-title">AI 小语盒</view>
  </view>
  <!-- 推荐故事卡片 -->
  <view wx:if="{{!isLoadingStories}}" class="recommend-title">
      <view class="card-header">
        <view class="title-container">
          <text class="card-title">今日AI推荐</text>
        </view>
        <view class="refresh-btn {{isGenerating ? 'loading' : ''}}" bindtap="onRefresh">
          <image wx:if="{{!isGenerating}}" src="/images/refresh_white.png" mode="aspectFit"></image>
          <image wx:else src="/images/loading.png" mode="aspectFit"></image>
        </view>
      </view>
  </view>

  <!-- 年龄选择弹窗 -->
<view wx:if="{{showAgeSelector}}" class="age-selector-fullscreen" catchtouchmove="noop">
  <view class="custom-nav-bar-feature" style="padding-top: {{menuTop}}px;">
    <view class="nav-bar-content-feature" style="height: {{menuHeight}}px;">
      <view class="features-title">AI小语盒-特色</view>
    </view>
  </view>
  <view class="features-section" style="padding-top: calc({{menuTop}}px + {{menuHeight}}px);">
    <view class="benefits-list">
      <view class="benefit-item">
        <image class="benefit-icon" src="cloud://cloudbase-1gahfeezccaf7d37.636c-cloudbase-1gahfeezccaf7d37-1354986900/images/app/quanyi1.png" mode="aspectFit"></image>
        <view class="benefit-content">
          <text class="benefit-name">个性化定制故事</text>
          <text class="benefit-desc">故事听腻了咋办？只需说出您想听的故事，AI随时随地为您生成您想听���故事。生成的故事还会根据孩子的年龄、喜好自动匹配。</text>
        </view>
      </view>
      <view class="benefit-item">
        <image class="benefit-icon" src="cloud://cloudbase-1gahfeezccaf7d37.636c-cloudbase-1gahfeezccaf7d37-1354986900/images/app/quanyi2.png" mode="aspectFit"></image>
        <view class="benefit-content">
          <text class="benefit-name">人声复刻讲故事</text>
          <text class="benefit-desc">您没时间给孩子讲故事咋办？只需录音，即可复刻您的声音，给孩子讲故事，让孩子感受您的温暖。</text>
        </view>
      </view>
      <view class="benefit-item">
        <image class="benefit-icon" src="cloud://cloudbase-1gahfeezccaf7d37.636c-cloudbase-1gahfeezccaf7d37-1354986900/images/app/quanyi3.png" mode="aspectFit"></image>
        <view class="benefit-content">
          <text class="benefit-name">分龄故事推荐</text>
          <text class="benefit-desc">给孩子听什么合适？AI将给不同年龄，自动生成适合孩子年龄段的故事类型和内容。每个故事都设有问题？让孩子真正学到知识。</text>
        </view>
      </view>
      <view class="benefit-item">
        <image class="benefit-icon" src="cloud://cloudbase-1gahfeezccaf7d37.636c-cloudbase-1gahfeezccaf7d37-1354986900/images/app/quanyi4.png" mode="aspectFit"></image>
        <view class="benefit-content">
          <text class="benefit-name">故事内容以学习为主</text>
          <text class="benefit-desc">还在担心孩子听不好的故事内容吗？推荐的故事内容，全部以提升孩子的知识学习、语言表达、思维能力以及社交能力为准。</text>
        </view>
      </view>
      <view class="benefit-item">
        <image class="benefit-icon" src="cloud://cloudbase-1gahfeezccaf7d37.636c-cloudbase-1gahfeezccaf7d37-1354986900/images/app/quanyi5.png" mode="aspectFit"></image>
        <view class="benefit-content">
          <text class="benefit-name">丰富实用功能</text>
          <text class="benefit-desc">收藏、多种声音自定义、个性化推荐等丰富实用功能等您来体验！</text>
        </view>
      </view>
    </view>
  </view>
  <view class="age-selection-section">
    <view class="age-selection-container">
      <view class="modal-title">请先选择宝贝的年龄段</view>
      <view class="modal-subtitle">以便AI根据您宝贝的年龄段撰写故事</view>
      <view class="age-options">
        <view class="age-option {{tempBabyAge === 3 ? 'selected' : ''}}" bindtap="onAgeSelect" data-age="3">1-3岁</view>
        <view class="age-option {{tempBabyAge === 5 ? 'selected' : ''}}" bindtap="onAgeSelect" data-age="5">4-6岁</view>
        <view class="age-option {{tempBabyAge === 7 ? 'selected' : ''}}" bindtap="onAgeSelect" data-age="7">7-9岁</view>
        <view class="age-option {{tempBabyAge === 10 ? 'selected' : ''}}" bindtap="onAgeSelect" data-age="10">9岁以上</view>
      </view>
      <button class="confirm-btn" bindtap="confirmAgeSelection">立即体验</button>
      <text class="login-link" bindtap="goToLogin">已是会员，请登录 ></text>
    </view>
  </view>
</view>

  <!-- 加载提示 -->
  <view wx:if="{{isLoadingStories}}" class="loading-container">
    <image class="loading-icon" src="/images/loading.png"></image>
    <text class="loading-text">AI正在努力生成今日故事…</text>
  </view>

  <swiper wx:if="{{!isLoadingStories && todayRecommends.length > 0}}" class="recommend-swiper" indicator-dots="{{true}}" autoplay="{{true}}" interval="{{3000}}" duration="{{500}}" circular="{{true}}">
    <swiper-item wx:for="{{todayRecommends}}" wx:key="storyId" class="recommend-swiper-item">
      <view class="recommend-card" bindtap="listenStory" data-story-id="{{item.storyId}}">
      
        <view class="story-content">
          <image class="story-image" src="{{item.image}}" mode="aspectFill"></image>
          <view class="content-right">
          <text class="story-title">{{item.title}}</text>
            <text class="story-preview">{{item.preview}}</text>
            <button class="listen-btn {{item.isPlaying ? 'playing' : ''}}" catchtap="listenStory" data-story-id="{{item.storyId}}">              <image src="{{item.isPlaying ? '/images/pause.png' : '/images/play.png'}}" mode="aspectFit"></image>
              <text>{{item.isPlaying ? '正在播放' : '立即收听'}}</text>
            </button>
          </view>
        </view>
      </view>
    </swiper-item>
  </swiper>

  <!-- 故事分类 -->
  <view class="section" wx:if="{{!isLoadingStories}}">
    <scroll-view class="category-grid" scroll-x="true" scroll-with-animation="true" scroll-left="{{scrollLeft}}" enhanced="true" show-scrollbar="false">
      <view class="category-class {{currentCategory === item.name ? 'active' : ''}}" wx:for="{{categories}}" wx:key="name" bindtap="onCategoryTap" data-name="{{item.name}}">
        <text>{{item.name}}</text>
      </view>
    </scroll-view>
  </view>

  <!-- 分类故事列表 -->
  <view class="story-grid">
    <view class="story-card" wx:for="{{categoryStories}}" wx:key="_id" bindtap="onStoryTap" data-story="{{item}}">
      <image class="story-list-image" src="{{item.image || '/images/default_story.png'}}" mode="aspectFill"></image>
      <view class="story-info">
        <text class="story-list-title">{{item.title}}</text>
        <text class="story-subtitle">{{item.subtitle}}</text>
      </view>
    </view>
  </view>

  <!-- AI -->
  <view class="AI-btn" bindtap="customStory">
    <image src="/images/AI_background.png" ></image>
  </view>

   <!-- home -->
  <view class="tab-home" catchtap="onTabHome">
    <image src="/images/home.png" ></image>
  </view>

  <!-- history -->
  <view class="tab-history-inactive" bindtap="ToHistory">
    <image src="/images/history.png" ></image>
  </view>
</view>