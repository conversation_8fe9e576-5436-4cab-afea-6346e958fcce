// 云函数入口文件
const cloud = require('wx-server-sdk')

cloud.init({ env: cloud.DYNAMIC_CURRENT_ENV });

// 云函数入口函数
exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext()
  const db = cloud.database()
  
  try {
    // 检查myStories集合是否存在
    const collections = await db.collections()
    const collectionNames = collections.map(collection => collection.name)
    
    if (!collectionNames.includes('myStories')) {
      // 创建myStories集合
      await db.createCollection('myStories')
      console.log('成功创建myStories集合')
    }
    
    return {
      success: true,
      message: 'myStories集合已准备就绪'
    }
  } catch (error) {
    console.error('初始化myStories集合失败:', error)
    return {
      success: false,
      error: error
    }
  }
}