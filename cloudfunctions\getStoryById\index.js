// cloudfunctions/getStoryById/index.js
const cloud = require('wx-server-sdk');

cloud.init({ env: cloud.DYNAMIC_CURRENT_ENV });
const db = cloud.database();

/**
 * 根据故事ID获取故事详细信息
 */
exports.main = async (event, context) => {
  const { storyId } = event;

  console.log('getStoryById called with:', { storyId });

  if (!storyId) {
    return {
      errCode: 1,
      errMsg: 'Missing required parameter: storyId',
    };
  }

  try {
    // 从stories数据集获取故事信息
    const storyRes = await db.collection('stories').doc(storyId).get();

    if (!storyRes.data) {
      return {
        errCode: 2,
        errMsg: 'Story not found',
      };
    }

    const storyData = storyRes.data;
    
    console.log('Story retrieved successfully:', {
      id: storyData._id,
      title: storyData.title,
      hasContent: !!storyData.content,
      hasAudio: !!(storyData.audioSegments || storyData.uncleAudioSegments)
    });

    return {
      errCode: 0,
      errMsg: 'Success',
      story: storyData
    };

  } catch (error) {
    console.error('Error getting story:', error);
    return {
      errCode: 500,
      errMsg: error.message,
    };
  }
};
