// 云函数入口文件
// 云函数入口文件
const cloud = require('wx-server-sdk')
const axios = require('axios')
const FormData = require('form-data')

cloud.init({ env: cloud.DYNAMIC_CURRENT_ENV });

const db = cloud.database()

async function getConfig() {
  const res = await db.collection('config').doc('story_config').get()
  return res.data
}

// SiliconFlow API 配置
const SILICONFLOW_BASE_URL = 'https://api.siliconflow.cn/v1' // SiliconFlow API基础URL
const SILICONFLOW_MODEL = 'FunAudioLLM/SenseVoiceSmall' // 使用的语音转文本模型

// 使用SiliconFlow API进行语音转文本
const transcribeAudioWithSiliconFlow = async (audioUrl) => {
  const config = await getConfig()
  const SILICONFLOW_API_KEY = config.SILICONFLOW_API_KEY
  const url = `${SILICONFLOW_BASE_URL}/audio/transcriptions`
  const headers = {
    'Authorization': `Bearer ${SILICONFLOW_API_KEY}`
    // Content-Type 会由 FormData 自动设置
  }

  try {
    console.log('开始从临时链接下载音频文件:', audioUrl)
    const audioResponse = await axios.get(audioUrl, { responseType: 'arraybuffer' })
    console.log('音频文件下载成功，文件大小:', audioResponse.data.length, 'bytes')

    const formData = new FormData()
    formData.append('file', Buffer.from(audioResponse.data), {
      filename: 'audio.mp3', // 文件名可以自定义
      contentType: 'audio/mpeg' // 根据实际音频格式调整
    })
    formData.append('model', SILICONFLOW_MODEL)

    console.log('开始调用SiliconFlow语音转文本API')
    const response = await axios.post(url, formData, {
      headers: {
        ...headers,
        ...formData.getHeaders() // 获取 FormData 的 Content-Type
      },
      timeout: 60000 // 设置60秒超时，语音识别可能耗时较长
    })

    if (response.status === 200 && response.data && response.data.text) {
      console.log('SiliconFlow API调用成功，转录文本:', response.data.text)
      return response.data.text
    } else {
      console.error('SiliconFlow API调用失败，响应:', response.data)
      throw new Error(`SiliconFlow API调用失败，状态码: ${response.status}, 响应: ${JSON.stringify(response.data)}`)
    }
  } catch (error) {
    const errorMessage = error.response ? JSON.stringify(error.response.data) : error.message;
    console.error('调用SiliconFlow API失败：', errorMessage)
    // 根据错误类型返回用户友好的提示
    if (error.message && error.message.includes('timeout')) {
      return '语音识别超时，请稍后再试或检查网络连接。';
    }
    return '语音识别服务暂时不可用，请稍后再试。';
  }
}

// 直接返回音频URL，不再需要保存到云存储 (此函数在此场景下可能不再直接使用，但保留以防其他地方调用)
const getAudioUrl = (audioUrl) => {
  return audioUrl
}

// 云函数入口函数
exports.main = async (event, context) => {
  const { storyId, audioUrl } = event
  const wxContext = cloud.getWXContext()
  const user = wxContext.OPENID // 虽然SiliconFlow API可能不需要user，但保留以备将来使用或记录

  try {
    console.log('开始处理用户录音，storyId:', storyId, 'user:', user)
    
    // 调用SiliconFlow API进行语音转文本
    const text = await transcribeAudioWithSiliconFlow(audioUrl)
    console.log('语音识别完成，获取到的文本:', text)
        
    // 检查是否为API返回的错误提示文本或识别失败
    const errorMessagesFromApi = [
      '语音识别超时，请稍后再试或检查网络连接。',
      '语音识别服务暂时不可用，请稍后再试。'
    ];

    if (!text || text.trim() === '' || errorMessagesFromApi.includes(text)) {
      let userMessage = '未能识别出语音内容，请尝试说话时间长一些或说话更清晰一些。';
      if (errorMessagesFromApi.includes(text)) {
        userMessage = text; // 如果是API明确返回的错误，直接使用
      }
      console.warn('语音识别未返回有效文本或API返回错误信息:', text);
      return {
        code: -1,
        data: null,
        message: userMessage
      }
    }

    console.log('语音识别成功，文本内容:', text)

    // 更新故事记录
    await db.collection('stories').doc(storyId).update({
      data: {
        text: text,
        updateTime: db.serverDate()
      }
    })
    console.log('故事记录更新成功')

    return {
      code: 0,
      data: {
        storyId: storyId,
        text: text
      },
      message: '语音识别成功'
    }

  } catch (error) {
    console.error('处理用户录音失败：', error)
    return {
      code: -1,
      data: null,
      message: '处理用户录音失败: ' + error.message
    }
  }
}