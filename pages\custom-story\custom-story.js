const share = require('../../utils/share.js');
Page({
  data: {
    menuTop: 0,
    menuHeight: 0,
    voiceOptions: ['妈妈', '爸爸', '叔叔', '阿姨'],
    lengthOptions: ['3分钟', '5分钟', '10分钟'],
    selectedVoice: '妈妈',
    selectedLength: '3分钟',
    storyDescription: '',
    recorderManager: null,
    voiceUri: '' // 添加voiceUri字段用于存储从voicesetting页面返回的uri
  },
  onShareAppMessage: share.onShareAppMessage,
  onShareTimeline: share.onShareTimeline,

  onLoad() {
    const app = getApp();
    this.setData({
      menuTop: app.globalData.menuTop,
      menuHeight: app.globalData.menuHeight,
    });
    const recorderManager = wx.getRecorderManager();
    this.setData({ recorderManager });

    // 检查本地存储中的录音权限状态
    const recordAuth = wx.getStorageSync('recordAuth');
    if (!recordAuth) {
      this.checkRecordAuth();
    }

    // 获取宝宝信息，设置默认声音
    const babyInfo = wx.getStorageSync('babyInfo');
    if (babyInfo && babyInfo.voiceType) {
      let selectedVoice = '妈妈';
      switch (babyInfo.voiceType) {
        case 'mom':
          selectedVoice = '妈妈';
          break;
        case 'dad':
          selectedVoice = '爸爸';
          break;
        case 'male':
          selectedVoice = '叔叔';
          break;
        case 'female':
          selectedVoice = '阿姨';
          break;
      }
      this.setData({
        selectedVoice: selectedVoice
      });
    }

    // 录音结束后的处理
    recorderManager.onStop(async (res) => {
      console.log('录音停止，临时路径：', res.tempFilePath);
      wx.showLoading({ title: '正在识别语音...' });
      try {
        // 上传录音文件到云存储
        const cloudPath = `audio/temp/${Date.now()}.mp3`;
        const uploadRes = await wx.cloud.uploadFile({
          cloudPath,
          filePath: res.tempFilePath
        });

        // 获取文件的临时访问链接
        const { fileList } = await wx.cloud.getTempFileURL({
          fileList: [uploadRes.fileID]
        });
        if (!fileList || !fileList[0] || !fileList[0].tempFileURL) {
          throw new Error('获取录音文件链接失败');
        }

        // 调用 voiceToText 云函数进行语音识别
        const { result } = await wx.cloud.callFunction({
          name: 'voiceToText',
          data: {
            storyId: Date.now().toString(),
            audioUrl: fileList[0].tempFileURL
          }
        });

        if (result.code === 0 && result.data && result.data.text) {
          this.setData({ storyDescription: result.data.text });
        } else {
          // 获取错误信息，优先使用result.message
          const errorMsg = result.message || '语音识别失败';
          throw new Error(errorMsg);
        }

        // 删除临时录音文件
        await wx.cloud.deleteFile({
          fileList: [uploadRes.fileID]
        });
      } catch (err) {
        console.error('语音识别失败:', err);
        wx.showToast({
          title: '语音识别失败，请重试',
          icon: 'none'
        });
      } finally {
        wx.hideLoading();
      }
    });

    // 录音错误的处理
    recorderManager.onError((err) => {
      console.error('录音错误:', err);
      wx.showToast({
        title: '录音出错，请重试',
        icon: 'none'
      });
    });
  },

  onShow() {
    // 页面显示时重新加载宝宝信息
    const babyInfo = wx.getStorageSync('babyInfo');
    if (babyInfo) {
      let voiceUri = '';
      // 根据当前选中的声音类型，从babyInfo中获取对应的uri
      switch (this.data.selectedVoice) {
        case '妈妈':
          voiceUri = babyInfo.momUri || '';
          break;
        case '爸爸':
          voiceUri = babyInfo.dadUri || '';
          break;
        // 对于叔叔和阿姨，目前没有对应的uri，保持为空
        case '叔叔':
        case '阿姨':
          voiceUri = '';
          break;
      }
      this.setData({
        voiceUri: voiceUri
      });
    }
  },

  // 返回上一页
  onBack() {
    wx.navigateBack({
      delta: 1
    });
  },
  
  // 声音选项点击处理
  selectVoice(e) {
    this.setData({
      selectedVoice: e.currentTarget.dataset.voice
    });
  },

  // 长度选项点击处理
  selectLength(e) {
    this.setData({
      selectedLength: e.currentTarget.dataset.length
    });
  },

  // 清空描述内容
  clearDescription() {
    if (this.data.storyDescription) {
      this.setData({
        storyDescription: ''
      })
    };
  },

  // 处理文本框输入事件
  onDescriptionInput(e) {
    this.setData({
      storyDescription: e.detail.value
    });
  },

  // 弹出输入对话框输入描述
  inputDescription() {
    wx.showModal({
      title: '请输入故事描述',
      content: '',
      editable: true,
      placeholderText: '请输入您想听的故事...',
      success: (res) => {
        if (res.confirm && res.content.trim() !== '') {
          this.setData({
            storyDescription: res.content.trim()
          });
        }
      }
    });
  },

  // 检查录音权限
  async checkRecordAuth() {
    try {
      const { authSetting } = await wx.getSetting();
      if (authSetting['scope.record']) {
        wx.setStorageSync('recordAuth', true);
        return true;
      }
      const { confirm } = await wx.authorize({ scope: 'scope.record' });
      if (confirm) {
        wx.setStorageSync('recordAuth', true);
        return true;
      }
      return false;
    } catch (err) {
      console.error('获取录音权限失败:', err);
      return false;
    }
  },

  // 点击录音按钮
  onRecordTap() {
    // 点击时不做任何响应
  },

  // 开始录音（长按）
  async startRecording() {
    // 检查本地存储中的权限状态
    const recordAuth = wx.getStorageSync('recordAuth');
    if (!recordAuth) {
      const hasAuth = await this.checkRecordAuth();
      if (!hasAuth) {
        wx.showToast({
          title: '请授权录音权限',
          icon: 'none'
        });
        return;
      }
    }

    // 再次检查系统权限状态，以防用户在系统设置中关闭了权限
    const { authSetting } = await wx.getSetting();
    if (!authSetting['scope.record']) {
      wx.setStorageSync('recordAuth', false);
      wx.showModal({
        title: '需要录音权限',
        content: '请在设置中开启录音权限',
        confirmText: '去设置',
        success: (res) => {
          if (res.confirm) {
            wx.openSetting();
          }
        }
      });
      return;
    }

    this.setData({
      isRecording: true
    });

    // 显示持续的录音提示
    wx.showToast({
      title: '正在录音，松开结束',
      icon: 'none',
      duration: 60000 // 设置较长的持续时间
    });
    this.data.recorderManager.start({
      duration: 60000,
      format: 'mp3',
      sampleRate: 16000,
      numberOfChannels: 1,
      encodeBitRate: 48000,
      frameSize: 50
    });
  },

  // 停止录音
  stopRecording() {
    this.setData({
      isRecording: false
    });
    wx.hideToast(); // 隐藏录音提示
    this.data.recorderManager.stop();
  },

  // 生成故事
  async generateStory() {
    if (!this.data.storyDescription) {
      wx.showToast({
        title: '请先输入或说出故事描述',
        icon: 'none'
      });
      return;
    }

    const babyInfo = wx.getStorageSync('babyInfo');
    if ((this.data.selectedVoice === '妈妈' && (!babyInfo || !babyInfo.momUri)) ||
        (this.data.selectedVoice === '爸爸' && (!babyInfo || !babyInfo.dadUri))) {
      wx.showModal({
        title: '提示',
        content: '请先录制' + this.data.selectedVoice + '的声音！',
        confirmText: '立即前往',
        success: (res) => {
          if (res.confirm) {
            wx.navigateTo({
              url: `/pages/voicesetting/voicesetting?from=custom-story&selectedVoice=${this.data.selectedVoice}`
            });
          }
        }
      });
      return;
    }

    const permission = require('../../utils/permission');
    const checkResult = await permission.checkPermission(permission.PERMISSION_TYPES.GENERATE_STORY);
    if (!checkResult.hasPermission) {
      permission.showVipModal(checkResult.message);
      return;
    }

    // After permission check, navigate to the story page to start generation
    let voiceType = 'female';
    switch (this.data.selectedVoice) {
      case '妈妈': voiceType = 'mom'; break;
      case '爸爸': voiceType = 'dad'; break;
      case '叔叔': voiceType = 'male'; break;
      case '阿姨': voiceType = 'female'; break;
    }
    const duration = parseInt(this.data.selectedLength);

    wx.navigateTo({
      url: `/pages/story/story?fromCustom=true&description=${encodeURIComponent(this.data.storyDescription)}&voiceType=${voiceType}&duration=${duration}`
    });
  },
})