// pages/privacy/privacy.js
Page({
  data: {
    
  },

  onLoad(options) {
    
  },

  // 用户同意隐私协议
  onAgree(e) {
    console.log(e)
    // 保存隐私协议同意状态
    wx.setStorageSync('privacy_agreed', true);
    
    // 跳转到宝宝信息填写页面
    wx.redirectTo({
      url: '/pages/babyinfo/babyinfo'
    });
  },

  // 用户不同意隐私协议
  onDisagree() {
    wx.showModal({
      title: '提示',
      content: '您需要同意隐私协议才能使用本小程序',
      showCancel: false,
      success: () => {
        // 用户点击确定后，退出小程序
        wx.exitMiniProgram();
      }
    });
  }
})