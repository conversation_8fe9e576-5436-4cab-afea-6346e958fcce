<view class="container">
  <view class="custom-nav-bar" style="padding-top: {{menuTop}}px; height: {{menuHeight}}px;">
    <image class="back-icon" src="/images/back.png" mode="aspectFit" bindtap="onBack" />
    <text class="nav-title">请选择讲故事的声音</text>
  </view>
  
  <view class="story-image">
    <image src="/images/baby_reading.png" mode="aspectFit"></image>
  </view>
  
  <!-- 横向选项卡 -->
  <view class="voice-tabs">
    <view class="voice-tab {{selectedVoice === 'mom' ? 'active' : ''}}" bindtap="selectVoice" data-voice="mom">
      <text>妈妈</text>
    </view>
    <view class="voice-tab {{selectedVoice === 'dad' ? 'active' : ''}}" bindtap="selectVoice" data-voice="dad">
      <text>爸爸</text>
    </view>
    <view class="voice-tab {{selectedVoice === 'male' ? 'active' : ''}}" bindtap="selectVoice" data-voice="male">
      <text>叔叔</text>
    </view>
    <view class="voice-tab {{selectedVoice === 'female' ? 'active' : ''}}" bindtap="selectVoice" data-voice="female">
      <text>阿姨</text>
    </view>
  </view>
  
  <!-- 内容区域 -->
  <view class="voice-content">
    <!-- 妈妈/爸爸选项的内容 -->
    <view class="parent-voice-content" wx:if="{{selectedVoice === 'mom' || selectedVoice === 'dad'}}">
      <view class="voice-prompt">请务必在安静的环境里，像讲故事一样，一字不差的慢慢念以下文案，即可用您的声音讲故事：</view>
      <view class="voice-text">亲爱的宝贝！今天我来给你讲一个有趣的故事。从前，有一只小狐狸发现了一个神奇的蘑菇，蘑菇告诉它："我是魔法蘑菇，来一起冒险吧！"小狐狸勇敢地说："好呀！"</view>
      
      <!-- 录音按钮 -->
      <view class="record-actions">
        <!-- 未录音状态 -->
        <view class="record-btn {{isRecording ? 'recording' : ''}} round-btn" wx:if="{{!(selectedVoice === 'mom' ? momRecorded : dadRecorded)}}" bindtouchstart="startRecording" bindtouchend="stopRecording" data-voice="{{selectedVoice}}">
          <text>{{isRecording ? '松开结束' : '按住录音'}}</text>
        </view>
        
        <!-- 已录音状态 -->
        <block wx:if="{{selectedVoice === 'mom' ? momRecorded : dadRecorded}}">
          <view class="record-btn {{isRecording ? 'recording' : ''}} round-btn" bindtouchstart="startRecording" bindtouchend="stopRecording" data-voice="{{selectedVoice}}">
            <text>{{isRecording ? '松开结束' : '重新录音'}}</text>
          </view>
          <view class="play-btn round-btn" bindtap="playRecording" data-voice="{{selectedVoice}}">
            <text>检查录音</text>
          </view>
        </block>
      </view>
    </view>
    
    <!-- 男声/女声选项的内容 -->
    <view class="parent-voice-content" wx:if="{{selectedVoice === 'male' || selectedVoice === 'female'}}">
      <view class="voice-text">你好，小朋友！今天我来给你讲一个有趣的故事。从前，有一只小狐狸发现了一个神奇的蘑菇，蘑菇告诉它："我是魔法蘑菇，来一起冒险吧！"小狐狸勇敢地说："好呀！"</view>
      <view class="btn-center">
        <view class="play-btn round-btn" bindtap="playSystemVoice" data-voice="{{selectedVoice}}">
          <text>试听</text>
        </view>
      </view>
    </view>
  </view>
  
  <!-- 确定按钮 -->
  <view class="btn-container">
    <button 
      class="confirm-btn {{selectedVoice && (selectedVoice === 'male' || selectedVoice === 'female' || (selectedVoice === 'mom' ? momRecorded : dadRecorded)) ? '' : 'disabled'}}"
      bindtap="onConfirm">
      确定
    </button>
  </view>
</view>    