<view class="container">
  <view class="custom-nav-bar" style="padding-top: {{menuTop}}px; height: {{menuHeight}}px;">
    <image class="back-icon" src="/images/back.png" mode="aspectFit" bindtap="onBack" />
    <view class="nav-title">个性化定制故事</view>
  </view>
  <!-- 声音选择区域 -->
  <view class="section-voice">
    <text class="label">请选择讲故事的声音:</text>
    <view class="options">
      <button wx:for="{{voiceOptions}}" wx:key="index" class="{{selectedVoice === item ? 'active' : ''}}" bindtap="selectVoice" data-voice="{{item}}">
        {{item}}
      </button>
    </view>
  </view>

  <!-- 长度选择区域 -->
  <view class="section">
    <text class="label">请选择故事的时间长度:</text>
    <view class="options">
      <button wx:for="{{lengthOptions}}" wx:key="index" class="{{selectedLength === item ? 'active' : ''}}" bindtap="selectLength" data-length="{{item}}">
        {{item}}
      </button>
    </view>
  </view>

  <!-- 故事描述区域 -->
  <view class="section">
    <view class="description-box">
      <textarea
        value="{{storyDescription}}"
        placeholder="赶紧按住录音或输入，描述您想听的故事吧！"
        bindinput="onDescriptionInput"
        auto-height
      />
    </view>
  </view>

  <!-- 操作按钮 -->
  <view class="bottom-bar">
    <image class="action-icon {{!storyDescription ? 'disabled' : ''}}" src="{{storyDescription ? '/images/refresh_white.png' : '/images/refresh.png'}}" mode="aspectFit" bindtap="clearDescription" />
    <button class="record-btn {{isRecording ? 'recording' : ''}}" bindtap="onRecordTap" bindlongpress="startRecording" bindtouchend="stopRecording">{{isRecording ? '松开结束录音' : '按住说出您想听的故事'}}</button>
    <image class="action-icon" src="/images/key_board.png" mode="aspectFit" bindtap="inputDescription" />
  </view>

  <!-- 开始生成按钮，仅当有故事描述时显示 -->
  <button class="generate-btn" bindtap="generateStory" wx:if="{{storyDescription}}">
    开始生成故事
  </button>
</view>
