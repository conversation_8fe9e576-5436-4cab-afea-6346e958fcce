.container {
  display: flex;
  padding: 30rpx;
  flex-direction: column;
  min-height: 100vh;
  box-sizing: border-box;
  background-image: url('https://636c-cloudbase-1gahfeezccaf7d37-1354986900.tcb.qcloud.la/backgroud.png');
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center;
  background-attachment: fixed;
}

.custom-nav-bar {
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  width: 100%;
  box-sizing: border-box;
  margin-bottom:50px;
}

.nav-title {
  font-size: 38rpx;
  flex: 1;
  text-align: center;
  margin-right: 120rpx;
  color:rgb(219, 219, 216);
}


.title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333333;
}

.back-icon {
    width: 60rpx;
    height:60rpx;
    margin-left: 10rpx;
}

.story-image {
  display: flex;
  justify-content: center;
  margin: 20rpx 0 30rpx;
  
}

.story-image image {
  width: 300rpx;
  height: 300rpx;
  border-radius: 36rpx;
}

.voice-tabs {
  display: flex;
  margin: 20rpx 6rpx;
  background:rgb(87, 87, 85);
  border-radius: 50rpx;
  padding: 8rpx;
}

.voice-tab {
  flex: 1;
  text-align: center;
  padding: 25rpx 0;
  font-size: 32rpx;
  color: #fff;
  position: relative;
  transition: all 0.3s;
  margin: 0 2rpx;
}

.voice-tab:first-child {
  border-radius: 50rpx 0 0 50rpx;
}

.voice-tab:last-child {
  border-radius: 0 50rpx 50rpx 0;
}

.voice-tab.active {
  background: #4CAF50;
  color: #fff;
  font-weight: 500;
  box-shadow: 0 2rpx 8rpx rgba(76, 175, 80, 0.3);
}

.record-btn {
  width: 160rpx;
  height: 160rpx;
  padding: 0;
  background-color: rgba(27, 133, 239, 0.8);
  color: white;
  border-radius: 160rpx;
  font-size: 30rpx;
  margin-left: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  box-sizing: border-box;
  margin-right:50rpx;
}

.play-btn {
  width: 160rpx;
  height: 160rpx;
  padding: 0 40rpx;
  background-color:rgb(212, 132, 46);
  color: white;
  border-radius: 40rpx;
  font-size: 30rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
}

.voice-content {
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 20rpx;
  padding: 30rpx;
  backdrop-filter: blur(10px);
  margin-bottom: 20rpx;
}

.parent-voice-content {
  border-radius: 20rpx;
  padding: 10rpx;
}

.voice-prompt {
  font-size: 29rpx;
  color: rgb(233, 191, 191);
  margin-bottom: 15rpx;
  line-height: 1.6;
  font-weight: 500;
}

.voice-text {
  font-size: 32rpx;
  color:rgb(250, 249, 249);
  line-height: 1.8;
  margin-bottom: 32rpx;
  white-space: pre-line;
}

.system-tag {
  font-size: 24rpx;
  color: #999999;
  margin-top: 4rpx;
}

.round-btn {
  border-radius: 50rpx;
  padding: 20rpx;
  min-height: 80rpx;
  min-width: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  
}

.record-btn.recording {
  background-color: rgba(255, 59, 48, 0.9);
  transform: scale(0.98);
}

.btn-container {
  width: 100%;
  margin-top: 20rpx;
  padding: 20rpx 0;
  display: flex;
  justify-content: center;
  gap: 0;
  flex-wrap: wrap;
}

.confirm-btn {
  flex: 0 0 auto;
  height: 90rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #4CAF50;
  color: #ffffff;
  border-radius: 45rpx;
  font-size: 32rpx;
  font-weight: bold;
  margin: 0 20rpx;
  min-width: 40%;
  box-sizing: border-box;
}

.confirm-btn.disabled {
  background-color: #cccccc;
  color: #ffffff;
  opacity: 0.7;
}

.record-actions {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  gap: 20rpx;
  margin-top: 20rpx;
}

.btn-center {
  display: flex;
  justify-content: center;
  align-items: center;
}    