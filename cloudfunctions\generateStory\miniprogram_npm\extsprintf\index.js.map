{"version": 3, "sources": ["extsprintf.js"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.js", "sourcesContent": ["/*\n * extsprintf.js: extended POSIX-style sprintf\n */\n\nvar mod_assert = require('assert');\nvar mod_util = require('util');\n\n/*\n * Public interface\n */\nexports.sprintf = jsSprintf;\nexports.printf = jsPrintf;\nexports.fprintf = jsFprintf;\n\n/*\n * Stripped down version of s[n]printf(3c).  We make a best effort to throw an\n * exception when given a format string we don't understand, rather than\n * ignoring it, so that we won't break existing programs if/when we go implement\n * the rest of this.\n *\n * This implementation currently supports specifying\n *\t- field alignment ('-' flag),\n * \t- zero-pad ('0' flag)\n *\t- always show numeric sign ('+' flag),\n *\t- field width\n *\t- conversions for strings, decimal integers, and floats (numbers).\n *\t- argument size specifiers.  These are all accepted but ignored, since\n *\t  Javascript has no notion of the physical size of an argument.\n *\n * Everything else is currently unsupported, most notably precision, unsigned\n * numbers, non-decimal numbers, and characters.\n */\nfunction jsSprintf(fmt)\n{\n\tvar regex = [\n\t    '([^%]*)',\t\t\t\t/* normal text */\n\t    '%',\t\t\t\t/* start of format */\n\t    '([\\'\\\\-+ #0]*?)',\t\t\t/* flags (optional) */\n\t    '([1-9]\\\\d*)?',\t\t\t/* width (optional) */\n\t    '(\\\\.([1-9]\\\\d*))?',\t\t/* precision (optional) */\n\t    '[lhjztL]*?',\t\t\t/* length mods (ignored) */\n\t    '([diouxXfFeEgGaAcCsSp%jr])'\t/* conversion */\n\t].join('');\n\n\tvar re = new RegExp(regex);\n\tvar args = Array.prototype.slice.call(arguments, 1);\n\tvar flags, width, precision, conversion;\n\tvar left, pad, sign, arg, match;\n\tvar ret = '';\n\tvar argn = 1;\n\n\tmod_assert.equal('string', typeof (fmt));\n\n\twhile ((match = re.exec(fmt)) !== null) {\n\t\tret += match[1];\n\t\tfmt = fmt.substring(match[0].length);\n\n\t\tflags = match[2] || '';\n\t\twidth = match[3] || 0;\n\t\tprecision = match[4] || '';\n\t\tconversion = match[6];\n\t\tleft = false;\n\t\tsign = false;\n\t\tpad = ' ';\n\n\t\tif (conversion == '%') {\n\t\t\tret += '%';\n\t\t\tcontinue;\n\t\t}\n\n\t\tif (args.length === 0)\n\t\t\tthrow (new Error('too few args to sprintf'));\n\n\t\targ = args.shift();\n\t\targn++;\n\n\t\tif (flags.match(/[\\' #]/))\n\t\t\tthrow (new Error(\n\t\t\t    'unsupported flags: ' + flags));\n\n\t\tif (precision.length > 0)\n\t\t\tthrow (new Error(\n\t\t\t    'non-zero precision not supported'));\n\n\t\tif (flags.match(/-/))\n\t\t\tleft = true;\n\n\t\tif (flags.match(/0/))\n\t\t\tpad = '0';\n\n\t\tif (flags.match(/\\+/))\n\t\t\tsign = true;\n\n\t\tswitch (conversion) {\n\t\tcase 's':\n\t\t\tif (arg === undefined || arg === null)\n\t\t\t\tthrow (new Error('argument ' + argn +\n\t\t\t\t    ': attempted to print undefined or null ' +\n\t\t\t\t    'as a string'));\n\t\t\tret += doPad(pad, width, left, arg.toString());\n\t\t\tbreak;\n\n\t\tcase 'd':\n\t\t\targ = Math.floor(arg);\n\t\t\t/*jsl:fallthru*/\n\t\tcase 'f':\n\t\t\tsign = sign && arg > 0 ? '+' : '';\n\t\t\tret += sign + doPad(pad, width, left,\n\t\t\t    arg.toString());\n\t\t\tbreak;\n\n\t\tcase 'x':\n\t\t\tret += doPad(pad, width, left, arg.toString(16));\n\t\t\tbreak;\n\n\t\tcase 'j': /* non-standard */\n\t\t\tif (width === 0)\n\t\t\t\twidth = 10;\n\t\t\tret += mod_util.inspect(arg, false, width);\n\t\t\tbreak;\n\n\t\tcase 'r': /* non-standard */\n\t\t\tret += dumpException(arg);\n\t\t\tbreak;\n\n\t\tdefault:\n\t\t\tthrow (new Error('unsupported conversion: ' +\n\t\t\t    conversion));\n\t\t}\n\t}\n\n\tret += fmt;\n\treturn (ret);\n}\n\nfunction jsPrintf() {\n\tvar args = Array.prototype.slice.call(arguments);\n\targs.unshift(process.stdout);\n\tjsFprintf.apply(null, args);\n}\n\nfunction jsFprintf(stream) {\n\tvar args = Array.prototype.slice.call(arguments, 1);\n\treturn (stream.write(jsSprintf.apply(this, args)));\n}\n\nfunction doPad(chr, width, left, str)\n{\n\tvar ret = str;\n\n\twhile (ret.length < width) {\n\t\tif (left)\n\t\t\tret += chr;\n\t\telse\n\t\t\tret = chr + ret;\n\t}\n\n\treturn (ret);\n}\n\n/*\n * This function dumps long stack traces for exceptions having a cause() method.\n * See node-verror for an example.\n */\nfunction dumpException(ex)\n{\n\tvar ret;\n\n\tif (!(ex instanceof Error))\n\t\tthrow (new Error(jsSprintf('invalid type for %%r: %j', ex)));\n\n\t/* Note that V8 prepends \"ex.stack\" with ex.toString(). */\n\tret = 'EXCEPTION: ' + ex.constructor.name + ': ' + ex.stack;\n\n\tif (ex.cause && typeof (ex.cause) === 'function') {\n\t\tvar cex = ex.cause();\n\t\tif (cex) {\n\t\t\tret += '\\nCaused by: ' + dumpException(cex);\n\t\t}\n\t}\n\n\treturn (ret);\n}\n"]}