{"version": 3, "sources": ["assert.js"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.js", "sourcesContent": ["// Copyright (c) 2012, <PERSON>. All rights reserved.\n// Copyright 2015 Joyent, Inc.\n\nvar assert = require('assert');\nvar Stream = require('stream').Stream;\nvar util = require('util');\n\n\n///--- Globals\n\n/* JSSTYLED */\nvar UUID_REGEXP = /^[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{12}$/;\n\n\n///--- Internal\n\nfunction _capitalize(str) {\n    return (str.charAt(0).toUpperCase() + str.slice(1));\n}\n\nfunction _toss(name, expected, oper, arg, actual) {\n    throw new assert.AssertionError({\n        message: util.format('%s (%s) is required', name, expected),\n        actual: (actual === undefined) ? typeof (arg) : actual(arg),\n        expected: expected,\n        operator: oper || '===',\n        stackStartFunction: _toss.caller\n    });\n}\n\nfunction _getClass(arg) {\n    return (Object.prototype.toString.call(arg).slice(8, -1));\n}\n\nfunction noop() {\n    // Why even bother with asserts?\n}\n\n\n///--- Exports\n\nvar types = {\n    bool: {\n        check: function (arg) { return typeof (arg) === 'boolean'; }\n    },\n    func: {\n        check: function (arg) { return typeof (arg) === 'function'; }\n    },\n    string: {\n        check: function (arg) { return typeof (arg) === 'string'; }\n    },\n    object: {\n        check: function (arg) {\n            return typeof (arg) === 'object' && arg !== null;\n        }\n    },\n    number: {\n        check: function (arg) {\n            return typeof (arg) === 'number' && !isNaN(arg);\n        }\n    },\n    finite: {\n        check: function (arg) {\n            return typeof (arg) === 'number' && !isNaN(arg) && isFinite(arg);\n        }\n    },\n    buffer: {\n        check: function (arg) { return Buffer.isBuffer(arg); },\n        operator: 'Buffer.isBuffer'\n    },\n    array: {\n        check: function (arg) { return Array.isArray(arg); },\n        operator: 'Array.isArray'\n    },\n    stream: {\n        check: function (arg) { return arg instanceof Stream; },\n        operator: 'instanceof',\n        actual: _getClass\n    },\n    date: {\n        check: function (arg) { return arg instanceof Date; },\n        operator: 'instanceof',\n        actual: _getClass\n    },\n    regexp: {\n        check: function (arg) { return arg instanceof RegExp; },\n        operator: 'instanceof',\n        actual: _getClass\n    },\n    uuid: {\n        check: function (arg) {\n            return typeof (arg) === 'string' && UUID_REGEXP.test(arg);\n        },\n        operator: 'isUUID'\n    }\n};\n\nfunction _setExports(ndebug) {\n    var keys = Object.keys(types);\n    var out;\n\n    /* re-export standard assert */\n    if (process.env.NODE_NDEBUG) {\n        out = noop;\n    } else {\n        out = function (arg, msg) {\n            if (!arg) {\n                _toss(msg, 'true', arg);\n            }\n        };\n    }\n\n    /* standard checks */\n    keys.forEach(function (k) {\n        if (ndebug) {\n            out[k] = noop;\n            return;\n        }\n        var type = types[k];\n        out[k] = function (arg, msg) {\n            if (!type.check(arg)) {\n                _toss(msg, k, type.operator, arg, type.actual);\n            }\n        };\n    });\n\n    /* optional checks */\n    keys.forEach(function (k) {\n        var name = 'optional' + _capitalize(k);\n        if (ndebug) {\n            out[name] = noop;\n            return;\n        }\n        var type = types[k];\n        out[name] = function (arg, msg) {\n            if (arg === undefined || arg === null) {\n                return;\n            }\n            if (!type.check(arg)) {\n                _toss(msg, k, type.operator, arg, type.actual);\n            }\n        };\n    });\n\n    /* arrayOf checks */\n    keys.forEach(function (k) {\n        var name = 'arrayOf' + _capitalize(k);\n        if (ndebug) {\n            out[name] = noop;\n            return;\n        }\n        var type = types[k];\n        var expected = '[' + k + ']';\n        out[name] = function (arg, msg) {\n            if (!Array.isArray(arg)) {\n                _toss(msg, expected, type.operator, arg, type.actual);\n            }\n            var i;\n            for (i = 0; i < arg.length; i++) {\n                if (!type.check(arg[i])) {\n                    _toss(msg, expected, type.operator, arg, type.actual);\n                }\n            }\n        };\n    });\n\n    /* optionalArrayOf checks */\n    keys.forEach(function (k) {\n        var name = 'optionalArrayOf' + _capitalize(k);\n        if (ndebug) {\n            out[name] = noop;\n            return;\n        }\n        var type = types[k];\n        var expected = '[' + k + ']';\n        out[name] = function (arg, msg) {\n            if (arg === undefined || arg === null) {\n                return;\n            }\n            if (!Array.isArray(arg)) {\n                _toss(msg, expected, type.operator, arg, type.actual);\n            }\n            var i;\n            for (i = 0; i < arg.length; i++) {\n                if (!type.check(arg[i])) {\n                    _toss(msg, expected, type.operator, arg, type.actual);\n                }\n            }\n        };\n    });\n\n    /* re-export built-in assertions */\n    Object.keys(assert).forEach(function (k) {\n        if (k === 'AssertionError') {\n            out[k] = assert[k];\n            return;\n        }\n        if (ndebug) {\n            out[k] = noop;\n            return;\n        }\n        out[k] = assert[k];\n    });\n\n    /* export ourselves (for unit tests _only_) */\n    out._setExports = _setExports;\n\n    return out;\n}\n\nmodule.exports = _setExports(process.env.NODE_NDEBUG);\n"]}