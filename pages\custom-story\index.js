Page({
  data: {
    isRecording: false,
    recordText: ''
  },

  startRecording() {
    const recorderManager = wx.getRecorderManager();
    
    recorderManager.onStart(() => {
      this.setData({
        isRecording: true
      });
    });

    recorderManager.onStop((res) => {
      this.setData({
        isRecording: false
      });
      const { tempFilePath } = res;
      this.uploadVoiceFile(tempFilePath);
    });

    recorderManager.start({
      duration: 60000, // 最长录音时间，单位ms
      format: 'mp3'
    });
  },

  stopRecording() {
    const recorderManager = wx.getRecorderManager();
    recorderManager.stop();
  },

  async uploadVoiceFile(tempFilePath) {
    wx.showLoading({
      title: '正在识别语音...',
    });

    try {
      // 先上传到云存储
      const cloudPath = `audio/temp/${Date.now()}.mp3`;
      const uploadResult = await wx.cloud.uploadFile({
        cloudPath,
        filePath: tempFilePath
      });

      console.log('上传结果：', uploadResult);

      if (!uploadResult.fileID) {
        throw new Error('文件上传失败');
      }

      // 获取文件访问链接
      const { fileList } = await wx.cloud.getTempFileURL({
        fileList: [uploadResult.fileID]
      });

      if (!fileList[0].tempFileURL) {
        throw new Error('获取文件访问链接失败');
      }

      // 调用云函数进行语音转文字
      const result = await wx.cloud.callFunction({
        name: 'voiceToText',
        data: {
          audioUrl: fileList[0].tempFileURL,
          storyId: Date.now().toString()
        }
      });

      console.log('云函数返回结果：', result);

      if (!result.result || result.result.code !== 0) {
        throw new Error(result.result?.message || '语音识别失败');
      }

      if (!result.result.data || !result.result.data.text) {
        throw new Error('语音识别结果为空');
      }

      this.setData({
        description: result.result.data.text
      });

      // 删除临时文件
      await wx.cloud.deleteFile({
        fileList: [uploadResult.fileID]
      });

    } catch (error) {
      console.error('语音识别失败:', error);
      wx.showToast({
        title: '语音识别失败: ' + error.message,
        icon: 'none',
        duration: 2000
      });
    } finally {
      wx.hideLoading();
    }
  }
});