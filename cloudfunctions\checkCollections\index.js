// 云函数入口文件
const cloud = require('wx-server-sdk');
cloud.init({ env: cloud.DYNAMIC_CURRENT_ENV });
const db = cloud.database();

// 云函数入口函数
exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext();
  
  // 需要检查的集合列表
  const collections = ['orders', 'users', 'baby_info', 'userUsage'];
  const result = {};
  
  try {
    // 检查每个集合是否存在
    for (const collection of collections) {
      try {
        // 添加limit(1)避免全表扫描，只需要验证集合是否存在和可访问
        const countResult = await db.collection(collection).limit(1).count();
        result[collection] = {
          exists: true,
          count: countResult.total
        };
      } catch (error) {
        result[collection] = {
          exists: false,
          error: error.message
        };
      }
    }
    
    return {
      code: 0,
      message: '检查完成',
      data: result
    };
  } catch (error) {
    console.error('检查集合失败', error);
    return {
      code: -1,
      message: '检查集合失败: ' + error.message
    };
  }
};