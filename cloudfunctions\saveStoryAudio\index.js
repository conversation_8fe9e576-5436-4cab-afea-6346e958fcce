// cloudfunctions/saveStoryAudio/index.js
// 根据用户需求重新设计的音频保存云函数
const cloud = require('wx-server-sdk');

cloud.init({ env: cloud.DYNAMIC_CURRENT_ENV });
const db = cloud.database();

/**
 * 保存流式生成的音频到对应的数据库字段
 * 根据声音类型保存到不同的数据集和字段：
 * - female: stories.audioSegments
 * - male: stories.uncleAudioSegments
 * - mom: user_story_audio.momAudioSegments
 * - dad: user_story_audio.dadAudioSegments
 */
exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext();
  const openid = wxContext.OPENID;
  const { storyId, voiceType, audioBuffer, storyTitle } = event;

  console.log('saveStoryAudio called with:', {
    storyId,
    voiceType,
    storyTitle,
    audioBufferSize: audioBuffer ? audioBuffer.length : 0,
    openid
  });

  if (!storyId || !voiceType || !audioBuffer) {
    return {
      errCode: 1,
      errMsg: 'Missing required parameters: storyId, voiceType, audioBuffer',
    };
  }

  try {
    // 第一步：上传音频到云存储
    const audioBufferData = Buffer.from(audioBuffer, 'base64');
    const fileName = `story_audio/${storyId}_${voiceType}_${Date.now()}.mp3`;

    console.log(`上传音频文件: ${fileName}, 大小: ${audioBufferData.length} bytes`);

    const uploadResult = await cloud.uploadFile({
      cloudPath: fileName,
      fileContent: audioBufferData,
    });

    if (!uploadResult.fileID) {
      throw new Error('音频上传失败');
    }

    console.log('音频上传成功:', uploadResult.fileID);

    // 第二步：根据声音类型保存到对应的数据库字段
    let saveResult;

    if (voiceType === 'female' || voiceType === 'male') {
      // 保存到stories数据集
      saveResult = await saveToStoriesCollection(storyId, voiceType, uploadResult.fileID);
    } else if (voiceType === 'mom' || voiceType === 'dad') {
      // 保存到user_story_audio数据集
      if (!openid) {
        throw new Error('User not logged in for mom/dad voice');
      }
      saveResult = await saveToUserStoryAudioCollection(storyId, voiceType, uploadResult.fileID, openid);
    } else {
      throw new Error(`Unsupported voice type: ${voiceType}`);
    }

    console.log('音频保存成功:', saveResult);

    return {
      errCode: 0,
      message: 'Audio saved successfully',
      fileID: uploadResult.fileID,
      voiceType: voiceType,
      collection: voiceType === 'female' || voiceType === 'male' ? 'stories' : 'user_story_audio'
    };

  } catch (error) {
    console.error('保存音频失败:', error);
    return {
      errCode: 500,
      errMsg: error.message,
    };
  }
};

/**
 * 保存到stories数据集
 */
async function saveToStoriesCollection(storyId, voiceType, fileID) {
  const audioField = voiceType === 'female' ? 'audioSegments' : 'uncleAudioSegments';

  const updateData = {
    [audioField]: [fileID],
    updateTime: db.serverDate()
  };

  return await db.collection('stories').doc(storyId).update({
    data: updateData
  });
}

/**
 * 保存到user_story_audio数据集
 */
async function saveToUserStoryAudioCollection(storyId, voiceType, fileID, openid) {
  const audioField = voiceType === 'mom' ? 'momAudioSegments' : 'dadAudioSegments';

  // 先查询是否已存在记录
  const existingQuery = await db.collection('user_story_audio')
    .where({
      _openid: openid,
      storyId: storyId
    })
    .get();

  if (existingQuery.data.length > 0) {
    // 更新现有记录
    const docId = existingQuery.data[0]._id;
    return await db.collection('user_story_audio').doc(docId).update({
      data: {
        [audioField]: [fileID],
        updateTime: db.serverDate()
      }
    });
  } else {
    // 创建新记录
    return await db.collection('user_story_audio').add({
      data: {
        _openid: openid,
        storyId: storyId,
        [audioField]: [fileID],
        createTime: db.serverDate(),
        updateTime: db.serverDate()
      }
    });
  }
}
