// pages/login/login.js
Page({
  data: {
    loading: false,
    menuTop: 0,
    menuHeight: 0,
  },

  onLoad(options) {
    const app = getApp();
    this.setData({
      menuTop: app.globalData.menuTop,
      menuHeight: app.globalData.menuHeight,
    });
  },

  // 处理登录
  handleLogin() {
    if (this.data.loading) return;

    this.setData({ loading: true });

    // 获取用户信息
    wx.getUserProfile({
      desc: '用于完善用户资料',
      success: (userInfo) => {
        // 调用云函数登录
        wx.cloud.callFunction({
          name: 'getOpenId',
          success: res => {
            const openid = res.result.openid;
            if (!openid) {
              wx.showToast({
                title: '登录失败，请重试',
                icon: 'none'
              });
              this.setData({ loading: false });
              return;
            }

            // 保存openid到本地
            wx.setStorageSync('openid', openid);
            // 保存用户信息到本地
            wx.setStorageSync('userInfo', userInfo.userInfo);

            // 检查云端是否有宝宝信息
            wx.cloud.callFunction({
              name: 'getBabyInfo',
              data: { openid },
              success: res => {
                if (res.result.code === 0 && res.result.data && res.result.data.length > 0) {
                  // 云端有数据，同步到本地并直接进入首页
                  const babyInfo = res.result.data[0];
                  wx.setStorageSync('babyInfo', babyInfo);
                  wx.setStorageSync('babyInfoSaved', true);
                  wx.setStorageSync('hasSelectedAge', true);
                  
                  // 直接进入首页
                  wx.reLaunch({
                    url: '/pages/home/<USER>'
                  });
                } else {
                  // 云端没有数据，跳转到宝宝信息填写页面
                  wx.redirectTo({
                    url: '/pages/babyinfo/babyinfo'
                  });
                }
              },
              fail: err => {
                console.error('获取宝宝信息失败', err);
                wx.redirectTo({
                  url: '/pages/babyinfo/babyinfo'
                });
              }
            });
          },
          fail: err => {
            console.error('云函数调用失败', err);
            wx.showToast({
              title: '登录失败，请重试',
              icon: 'none'
            });
            this.setData({ loading: false });
          }
        });
      },
      fail: (err) => {
        console.error('获取用户信息失败', err);
        wx.showToast({
          title: '需要您授权才能使用',
          icon: 'none'
        });
        this.setData({ loading: false });
      }
    });
  },
  onBack() {
    wx.switchTab({
      url: '/pages/home/<USER>'
    });
  },
});