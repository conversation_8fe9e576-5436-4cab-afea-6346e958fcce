const cloud = require('wx-server-sdk');

cloud.init({ env: cloud.DYNAMIC_CURRENT_ENV });

const db = cloud.database();

exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext();
  const openid = wxContext.OPENID;
  const { storyId, voice } = event;

  if (!storyId || !voice) {
    return {
      errCode: 1,
      errMsg: 'Missing storyId or voice parameter',
    };
  }

  const voiceFieldMap = {
    auntie: 'audioSegments', 
    uncle: 'uncleAudioSegments',
    mom: 'MomAudioSegments',
    dad: 'DadAudioSegments',
  };

  const collectionName = (voice === 'mom' || voice === 'dad') ? 'user_story_audio' : 'stories';
  const audioField = voiceFieldMap[voice];

  try {
    let audioData;
    
    if (collectionName === 'user_story_audio') {
      if (!openid) {
        return {
          errCode: 4,
          errMsg: 'User not logged in, cannot fetch user-specific audio.',
        };
      }
      
      const userStoryAudio = await db.collection('user_story_audio').where({
        _openid: openid,
        storyId: storyId,
      }).get();

      if (userStoryAudio.data.length > 0) {
        audioData = userStoryAudio.data[0];
      }
    } else {
      const storyResult = await db.collection('stories').doc(storyId).get();
      audioData = storyResult.data;
    }

    if (!audioData) {
      return {
        errCode: 2,
        errMsg: 'Story not found',
      };
    }

    const generatedCount = audioData[`${audioField}_generated_count`] || 0;
    const totalCount = audioData[`${audioField}_total_count`] || 0;
    
    // 过滤出已生成的音频片段（非null）
    let audioSegments = audioData[audioField] || [];
    if (typeof audioSegments === 'object' && !Array.isArray(audioSegments)) {
      audioSegments = Object.values(audioSegments).map(segment => segment.fileID);
    }
    const availableSegments = audioSegments.filter(segment => segment !== null && segment !== undefined);

    const status = audioData[`${audioField}_status`] || 'waiting';

    return {
      errCode: 0,
      status: status, // 'waiting', 'generating', 'completed', 'failed'
      audioSegments: availableSegments,
      generatedCount: generatedCount,
      totalCount: totalCount,
      progress: totalCount > 0 ? (generatedCount / totalCount) * 100 : 0,
      hasNewSegments: availableSegments.length > 0,
    };

  } catch (error) {
    console.error('Error in getAudioProgress:', error);
    return {
      errCode: 500,
      errMsg: error.message,
    };
  }
}; 