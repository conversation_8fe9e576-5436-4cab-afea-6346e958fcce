const share = require('../../utils/share.js');
Page({
  data: {
    menuTop: 0,
    menuHeight: 0,
    currentCategory: '推荐',
    categoryStories: [],
    todayRecommends: [],
    babyInfo: null,
    babyAge: 5, // 默认年龄
    refreshing: false,
    isLoadingStories: true, // 新增加载状态
    showAgeSelector: false, // 控制年龄选择弹窗
    tempBabyAge: 5, // 弹窗中临时选中的年龄
  },
  onShareAppMessage: share.onShareAppMessage,
  onShareTimeline: share.onShareTimeline,

  onLoad() {
    const app = getApp();
    this.setData({
      menuTop: app.globalData.menuTop,
      menuHeight: app.globalData.menuHeight,
    });

    const hasSelectedAge = wx.getStorageSync('hasSelectedAge');
    if (!hasSelectedAge) {
      this.setData({ showAgeSelector: true, isLoadingStories: false });
    } else {
      this.getBabyInfo().then(() => {
        this.loadDayStories({ showCentralLoader: true });
        this.getUserVipInfo();
      });
    }
  },

  onAgeSelect(e) {
    const newAge = Number(e.currentTarget.dataset.age);
    if (this.data.tempBabyAge !== newAge) {
      this.setData({
        tempBabyAge: newAge,
      });
    }
  },

  confirmAgeSelection() {
    const selectedAge = this.data.tempBabyAge;
    this.setData({
      babyAge: selectedAge,
      showAgeSelector: false,
      isLoadingStories: true, // 开始加载故事
    });
    wx.setStorageSync('hasSelectedAge', true);
    this.loadDayStories({ force: true, showCentralLoader: true });
  },

  noop() {},

  goToLogin() {
    wx.navigateTo({ url: '/pages/login/login' });
  },
  
    //获取会员信息
    getUserVipInfo() {
      // 获取用户openid
      const openid = wx.getStorageSync('openid');
      if (!openid) {
        this.setData({
          vipStatus: 'never',
          buttonText: '立即开通'
        });
        return;
      }

      // 调用云函数获取用户信息，如果用户不存在会自动创建
      wx.cloud.callFunction({
        name: 'getUserInfo'
      }).then(res => {
        if (res.result.code !== 0 || !res.result.data) {
          console.log('获取用户信息失败:', res.result.message);
          this.setData({
            vipStatus: 'never',
            buttonText: '立即开通'
          });
          return;
        }

        const user = res.result.data;
        // 检查user对象是否存在
        if (!user) {
          console.error('用户数据为空');
          this.setData({
            vipStatus: 'never',
            buttonText: '立即开通'
          });
          return;
        }

        // 检查vipExpireDate属性
        if (!user.vipExpireDate) {
          // vipExpireDate为空，说明从未开通过会员
          this.setData({
            vipStatus: 'never',
            buttonText: '立即开通'
          });
        } else {
          const currentDate = new Date();
          const expireDate = new Date(user.vipExpireDate);
          
          // 将当前日期和到期日期都设置为当天的0点，只比较日期而不比较具体时间
          currentDate.setHours(0, 0, 0, 0);
          expireDate.setHours(0, 0, 0, 0);
          
          if (expireDate >= currentDate) {
            // 会员未过期（包括当天到期的情况）
            this.setData({
              vipStatus: 'active',
              vipExpireDate: this.formatDate(expireDate),
              buttonText: '立即续费'
            });
          } else {
            // 会员已过期
            this.setData({
              vipStatus: 'expired',
              vipExpireDate: this.formatDate(expireDate),
              buttonText: '立即续费'
            });
          }
        }
      }).catch(err => {
        console.error('获取会员信息失败', err);
        // 发生错误时设置默认状态
        this.setData({
          vipStatus: 'never',
          buttonText: '立即开通'
        });
      });
    },
  
    // 格式化日期
    formatDate(date) {
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      return `${year}-${month}-${day}`;
    },

  onPullDownRefresh() {
    this.setData({ refreshing: true, isGenerating: true }); // 设置 isGenerating 为 true
    this.loadDayStories({ force: true, showCentralLoader: false })
      .then(() => {
        wx.stopPullDownRefresh();
        this.setData({ refreshing: false, isGenerating: false }); // 设置 isGenerating 为 false
      })
      .catch(() => {
        wx.stopPullDownRefresh();
        this.setData({ refreshing: false, isGenerating: false }); // 设置 isGenerating 为 false
        wx.showToast({
          title: '刷新失败',
          icon: 'none'
        });
      });
  },

  onTabHome() {
    // 阻止事件冒泡
  },
  
  toProfile(e) {
    const openid = wx.getStorageSync('openid');
    if (!openid) {
      wx.navigateTo({ url: '/pages/login/login' });
      return;
    }
    if (e.target.dataset.type === 'vip') {
      return;
    }
    wx.navigateTo({
      url: '/pages/profile/profile'
    });
  },

  onShow() {
    const hasSelectedAge = wx.getStorageSync('hasSelectedAge');
    if (!hasSelectedAge) {
      // 如果还没选择过年龄，不执行任何操作，等待弹窗确认
      return;
    }
    const previousAge = this.data.babyAge;
    this.getBabyInfo().then(() => {
      const currentAge = this.data.babyAge;
      // 如果年龄发生变化，则强制刷新故事
      if (previousAge !== currentAge) {
        this.loadDayStories({ force: true, showCentralLoader: true });
      }
    });
    this.getUserVipInfo();
  },

  calculateBabyAge(birthday) {
    if (!birthday) return this.data.babyAge; // 如果没有生日信息，则返回当前年龄
    const today = new Date();
    const birthDate = new Date(birthday);
    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
      age--;
    }
    // 根据计算出的年龄，返回对应的年龄段值
    if (age <= 3) return 3;
    if (age <= 6) return 5;
    if (age <= 9) return 7;
    return 10;
  },

  getBabyInfo() {
    return new Promise((resolve, reject) => {
      const openid = wx.getStorageSync('openid');
      if (!openid) {
        // 用户未登录，直接使用当前的babyAge
        resolve();
        return;
      }
      wx.cloud.callFunction({
        name: 'getBabyInfo',
        success: res => {
          if (res.result.code === 0 && res.result.data && res.result.data.length > 0) {
            const babyInfo = res.result.data[0];
            this.setData({
              babyInfo: babyInfo,
              babyAge: this.calculateBabyAge(babyInfo.birthday)
            });
          }
          resolve();
        },
        fail: err => {
          console.error('获取宝宝信息失败', err);
          reject(err);
        }
      });
    });
  },

  loadDayStories(options = {}) {
    const { force = false, showCentralLoader = true } = options;
    if (showCentralLoader) {
      this.setData({ isLoadingStories: true }); // 开始加载，设置状态为true
    }
    return new Promise((resolve, reject) => {
      const age = this.data.babyAge;
      const now = Date.now();
      const cachedData = wx.getStorageSync('storiesData');
      const cacheTime = wx.getStorageSync('storiesDataTime');
      const cachedAge = wx.getStorageSync('storiesDataAge');

      if (!force && cachedData && cacheTime && (now - cacheTime < 24 * 60 * 60 * 1000) && cachedAge === age) {
        this.updateStoriesData(cachedData);
        this.setData({ isLoadingStories: false });
        resolve();
        return;
      }

      wx.cloud.callFunction({
        name: 'dayStories',
        data: {
          age: age
        },
        success: res => {
          if (res.result.code === 0) {
            const storiesData = res.result.data;
            
            // 更新缓存
            wx.setStorageSync('storiesData', storiesData);
            wx.setStorageSync('storiesDataTime', now);
            wx.setStorageSync('storiesDataAge', age);
            
            this.updateStoriesData(storiesData);
            this.setData({ isLoadingStories: false }); // 加载完成
            resolve();
          } else {
            wx.showToast({
              title: '获取故事失败',
              icon: 'none'
            });
            reject(new Error('获取故事失败'));
          }
        },
        fail: err => {
          console.error('调用dayStories云函数失败', err);
          wx.showToast({
            title: '获取故事失败',
            icon: 'none'
          });
          this.setData({ isLoadingStories: false }); // 加载失败
          reject(err);
        }
      });
    });
  },

  updateStoriesData(data) {
    const { todayRecommends, recommendedStories, categories, storiesByType } = data;
    
    // 更新今日推荐
    this.setData({
      todayRecommends: todayRecommends.map(story => ({
        title: story.title,
        preview: story.content.substring(0, 100) + '...',
        storyId: story._id,
        voiceUrl: story.voiceUrl || '',
        image: story.image || '/images/default_story.png'
      }))
    });

    // 更新分类列表
    const updatedCategories = [
      { name: '推荐', image: '/images/recommend.png' },
      ...categories.map(type => ({
        name: type,
        image: `/images/${type}.png`
      }))
    ];

    // 缓存所有分类的故事数据
    this.allStoriesData = {
      recommended: recommendedStories,
      ...storiesByType
    };

    // 根据当前选中的分类更新故事列表
    const currentCategoryStories = this.data.currentCategory === '推荐' ? 
      recommendedStories : 
      (storiesByType[this.data.currentCategory] || []);

    this.setData({
      categories: updatedCategories,
      categoryStories: currentCategoryStories
    });
  },

  loadCategoryStories() {
    // 重新调用dayStories云函数获取最新数据
    this.loadDayStories({ showCentralLoader: true });
  },

  onCategoryTap(e) {
    const category = e.currentTarget.dataset.name;
    
    // 从缓存的故事数据中获取对应分类的故事
    const stories = category === '推荐' ? 
      this.allStoriesData.recommended : 
      (this.allStoriesData[category] || []);
    
    this.setData({
      currentCategory: category,
      categoryStories: stories
    }, () => {
      // 自动滚动到选中的分类按钮位置
      this.scrollToCategoryButton(e.currentTarget);
    });
  },

  // 刷新推荐故事
  onRefresh() {
    if (this.data.isGenerating) return;
    this.setData({
      isGenerating: true
    });

    this.loadDayStories({ force: true, showCentralLoader: false })
      .then(() => {
        this.setData({
          isGenerating: false
        });
      })
      .catch(() => {
        this.setData({
          isGenerating: false
        });
      });
  },

  // 滚动到选中的分类按钮位置
  scrollToCategoryButton(target) {
    const query = wx.createSelectorQuery();
    query.select('.category-grid').boundingClientRect();
    query.selectAll('.category-class').boundingClientRect();
    query.exec(res => {
      if (!res || !res[0] || !res[1] || !res[1].length) return;
      
      const containerWidth = res[0].width;
      const containerLeft = res[0].left;
      const buttons = res[1];
      
      // 找到当前选中的按钮的索引
      const activeIndex = this.data.categories.findIndex(category => category.name === this.data.currentCategory);
      if (activeIndex === -1) return;
      
      const activeButton = buttons[activeIndex];
      if (!activeButton) return;
      
      // 计算所有按钮的总宽度和间距
      const totalWidth = buttons.reduce((sum, btn) => sum + btn.width, 0);
      const buttonGap = 20; // 按钮之间的间距
      const totalWidthWithGaps = totalWidth + (buttons.length - 1) * buttonGap;
      
      // 计算最大可滚动距离
      const maxScroll = Math.max(0, totalWidthWithGaps - containerWidth);
      
      let scrollLeft;
      
      // 计算目标按钮的中心位置
      const buttonLeft = buttons.slice(0, activeIndex).reduce((sum, btn) => sum + btn.width + buttonGap, 0);
      const buttonCenter = buttonLeft + activeButton.width / 2;
      
      // 计算理想的滚动位置（使按钮居中）
      const idealScroll = buttonCenter - containerWidth / 2;
      
      // 处理特殊情况
      if (activeIndex === 0) {
        // 第一个按钮，滚动到最左侧
        scrollLeft = 0;
      } else if (activeIndex === buttons.length - 1) {
        // 最后一个按钮，确保完全显示
        scrollLeft = maxScroll;
      } else if (activeIndex === buttons.length - 2) {
        // 倒数第二个按钮，确保最后两个按钮都完全显示
        const lastTwoButtonsWidth = buttons[activeIndex].width + buttonGap + buttons[activeIndex + 1].width;
        scrollLeft = Math.min(maxScroll, totalWidthWithGaps - containerWidth);
      } else {
        // 其他情况，尝试居中显示，但不超出边界
        scrollLeft = Math.max(0, Math.min(maxScroll, idealScroll));
      }
      
      // 使用动画效果设置滚动位置
      this.setData({
        scrollLeft: scrollLeft
      });
    });
  },

  onReachBottom() {
    // 由于所有数据都已经加载，不需要分页加载更多
    return;
  },

  async onStoryTap(e) {
    const openid = wx.getStorageSync('openid');
    if (!openid) {
      let guestListenCount = wx.getStorageSync('guestListenCount') || 0;
      guestListenCount++;
      wx.setStorageSync('guestListenCount', guestListenCount);
      if (guestListenCount > 2) {
        wx.showToast({
          title: '未登录，只能试听2个故事哦！请立即登录！',
          icon: 'none'
        });
        setTimeout(() => {
          wx.navigateTo({ url: '/pages/login/login' });
        }, 800);
        return;
      }
      const story = e.currentTarget.dataset.story;
      wx.navigateTo({
        url: `/pages/story/story?storyId=${story.storyId || story._id}&fromHome=true`
      });
      return;
    }
    const story = e.currentTarget.dataset.story;
    
    // 引入权限管理模块
    const permission = require('../../utils/permission');
    
    // 检查听故事权限
    const checkResult = await permission.checkPermission(permission.PERMISSION_TYPES.LISTEN_STORY);
    if (!checkResult.hasPermission) {
      // 没有权限，显示会员提示弹窗
      permission.showVipModal(checkResult.message);
      return;
    }

    // 更新使用记录
    await permission.updateUsageRecord(permission.PERMISSION_TYPES.LISTEN_STORY);
    
    wx.navigateTo({
      url: `/pages/story/story?storyId=${story.storyId || story._id}&fromHome=true`
    });
  },

  async listenStory(e) {
    const openid = wx.getStorageSync('openid');
    if (!openid) {
      let guestListenCount = wx.getStorageSync('guestListenCount') || 0;
      guestListenCount++;
      wx.setStorageSync('guestListenCount', guestListenCount);
      if (guestListenCount > 2) {
        wx.showToast({
          title: '未登录，只能试听2个故事哦！请立即登录！',
          icon: 'none'
        });
        setTimeout(() => {
          wx.navigateTo({ url: '/pages/login/login' });
        }, 800);
        return;
      }
      const storyId = e.currentTarget.dataset.storyId;
      wx.navigateTo({
        url: `/pages/story/story?storyId=${storyId}&fromHome=true`
      });
      return;
    }
    const storyId = e.currentTarget.dataset.storyId;
    
    // 引入权限管理模块
    const permission = require('../../utils/permission');
    
    // 检查听故事权限
    const checkResult = await permission.checkPermission(permission.PERMISSION_TYPES.LISTEN_STORY);
    if (!checkResult.hasPermission) {
      // 没有权限，显示会员提示弹窗
      permission.showVipModal(checkResult.message);
      return;
    }

    // 更新使用记录
    await permission.updateUsageRecord(permission.PERMISSION_TYPES.LISTEN_STORY);
    
    wx.navigateTo({
      url: `/pages/story/story?storyId=${storyId}&fromHome=true`
    });
  },

  customStory() {
    const openid = wx.getStorageSync('openid');
    if (!openid) {
      wx.showToast({
        title: '自定义故事，请先登录！',
        icon: 'none'
      });
      setTimeout(() => {
        wx.navigateTo({ url: '/pages/login/login' });
      }, 800);
      return;
    }
    wx.navigateTo({
      url: '/pages/custom-story/custom-story'
    });
  },

  ToHistory() {
    const openid = wx.getStorageSync('openid');
    if (!openid) {
      wx.showToast({
        title: '查看历史和收藏，请先登录！',
        icon: 'none'
      });
      setTimeout(() => {
        wx.navigateTo({ url: '/pages/login/login' });
      }, 800);
      return;
    }
    wx.switchTab({
      url: '/pages/history/history'
    });
  },

  vipBuy() {
    const openid = wx.getStorageSync('openid');
    if (!openid) {
      wx.showToast({
        title: '查看会员状态，请先登录！',
        icon: 'none'
      });
      setTimeout(() => {
        wx.navigateTo({ url: '/pages/login/login' });
      }, 800);
      return;
    }
    console.log("立即续费按钮被点击了");
    // 这里可以添加跳转到会员购买页面的逻辑
    wx.navigateTo({
      url: '/pages/vip/vip'
    });
  },

})
