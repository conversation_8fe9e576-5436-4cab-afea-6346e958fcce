/* pages/profile/profile.wxss */
.profile-container {

  padding: 30rpx;
  background-image: url('https://636c-cloudbase-1gahfeezccaf7d37-1354986900.tcb.qcloud.la/backgroud.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
  background-position: center;
  background-attachment: fixed;
  height: 100vh;
  box-sizing: border-box;
}

.custom-nav-bar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    height: 180rpx;
    background-color: rgba(48, 47, 51, 0.4);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 999;
    backdrop-filter: blur(10px);
    padding-bottom: 10rpx;
}

.custom-nav-bar-2 {
    margin-top:30rpx;
    top: 0;
    left: 0;
    right: 0;
    height: 80rpx;
    background-color: transparent;
    display: flex;
    align-items: center;
    z-index: 999;
    padding: 0 20rpx;
}

.back-icon {
    width: 60rpx;
    height:60rpx;
    margin-left: 20rpx;
}

.nav-title {
    color:rgb(200, 200, 241);
    font-size: 36rpx;
    flex-grow: 1;
    text-align: center;
    margin-right:70rpx;
}


.vip-text {
  font-size: 30rpx;
  color:rgb(253, 241, 188);
  line-height:1.5;
  display: block;
}
.vip-text-expired {
  font-size: 30rpx;
  color:rgb(244, 241, 241);
  line-height:1.5;
  display: block;
}
/* 用户信息区域 */
.profile-content {
  padding: 20rpx;
  margin-top: 40rpx;
  margin-bottom: 20rpx;
  background-color:rgb(158, 157, 156);
  border-radius:30rpx;
  height: 220rpx;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  margin-top:160rpx;
}

.profile-vip-content {
  padding: 20rpx;
  margin-top: 40rpx;
  margin-bottom: 20rpx;
  background-color:rgb(225, 170, 60);
  border-radius:30rpx;
  height: 220rpx;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  margin-top:160rpx;
}

.profile-header {
  display: flex;
  flex-direction: column;
}

.avatar-wrapper {
  width: 110rpx !important;
  height: 110rpx !important;
  border-radius: 50% !important;
  overflow: hidden;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.2);
  background-color: rgba(255, 255, 255, 0.1);
  margin: 20rpx;
  padding: 0 !important;
  line-height: normal !important;
}

.avatar-wrapper image {
  width: 100% !important;
  height: 100% !important;
  object-fit: cover;
  transition: transform 0.3s ease;
  display: block !important;
  border-radius: 50% !important;
}

.avatar-wrapper:active image {
  transform: scale(1.1);
}

.nickname-wrapper {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  flex: 1;
}

.nickname-input {
  font-size: 38rpx;
  color: #fff;
  margin-right: 10rpx;
}

.arrow {
  margin-top:10rpx;
  width: 32rpx;
  height: 32rpx;
}

.membership-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 40rpx;
  margin-left:10rpx;
  width: 100%;
}

.membership-info {
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

.membership-text {
  font-size: 32rpx;
  color: #FFD700;
  margin-right: 20rpx;
}

.expiry-date {
  font-size: 30rpx;
  color: rgba(255,255,255,0.8);
  margin-right: 20rpx;
}

.vip-btn {
  border-radius: 30rpx;
  background-color: rgb(222, 110, 19);
  padding: 16rpx 26rpx;
  display: flex;
  align-items: center;
  color: rgb(252, 252, 252);
  font-size: 30rpx;
  margin-right:20rpx;
}

/* 宝宝信息区域 */

.section-title {
  font-size: 32rpx;
  color:rgb(221, 221, 240);
  margin-bottom: 20rpx;
  margin-left:10rpx;
}

.version-info {
  position: fixed;
  bottom: 40rpx;
  left: 0;
  right: 0;
  display: flex;
  flex-direction: column;
  line-height:46rpx;
  align-items: center;
  font-size: 24rpx;
  color:rgb(203, 201, 201);
}

.info-list {
  background-color:rgb(36, 48, 58);
  border-radius: 12rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx rgba(217, 217, 217, 0.05) solid;
}

.info-item:last-child {
  border-bottom: none;
}

.label {
  font-size: 32rpx;
  color:rgb(222, 218, 218);
}

.value-wrapper {
  display: flex;
  align-items: center;
}

.value {
  font-size: 28rpx;
  color:rgb(181, 179, 179);
  margin-right: 10rpx;
}


/* 其他功能区域 */
.other-functions {
  margin-top: 30rpx;
}

.baby-info {
  margin-bottom: 30rpx;
}

/* 退出登录按钮样式 */
.logout-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 30rpx;
  margin-top: 40rpx;
}

.logout-btn {
  width: 80%;
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color:rgb(84, 82, 82);
  color:rgb(146, 143, 143);
  border-radius: 44rpx;
  font-size: 32rpx;
  font-weight: 500;
  margin: 10rpx auto;
}

.logout-btn:active {
  opacity: 0.8;
}