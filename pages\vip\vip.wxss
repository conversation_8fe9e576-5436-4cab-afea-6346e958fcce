.container {
  padding: 20rpx;
   background-image: url('https://636c-cloudbase-1gahfeezccaf7d37-1354986900.tcb.qcloud.la/backgroud.png');
  min-height: 100vh;
  background-size: cover;
  background-attachment: fixed;
  background-position: center;
  background-repeat: no-repeat;
}

.custom-nav-bar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    height: 180rpx;
    background-color: rgba(48, 47, 51, 0.4);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 999;
    backdrop-filter: blur(10px);
    padding-bottom: 10rpx;
}
.custom-nav-bar::after {
    content: "";
    position: absolute;
    left: 0;
    right: 0;
    bottom: 0;
    top: 0;
    background: rgba(48, 47, 51, 0.4);
    z-index: -1;
    border-radius: 0 0 32rpx 32rpx;
    pointer-events: none;
}

.back-icon {
  width: 56rpx;
  height: 56rpx;
  margin-left: 20rpx;
}

.nav-title {
  font-size: 36rpx;
  flex: 1;
  text-align: center;
  margin-right: 68rpx;
  color:rgb(227, 193, 99);
}

.vip-expired {
  display: flex;
  justify-content: space-between;
  margin: 30rpx 0;
  margin-top:180rpx;
}

.vip-text {
  font-size: 32rpx;
  color:rgb(245, 231, 169);
  line-height:1.5;
  display: block;
}

.vip-text-expired {
  font-size: 32rpx;
  color:rgb(182, 182, 182);
  line-height:1.5;
  display: block;
}

/* 购买卡片样式 */
.vip-cards {
  display: flex;
  justify-content: space-between;
  margin: 30rpx 0;
}

.vip-card {
  width: 40%;
  border-radius: 20rpx;
  background-color:rgb(192, 191, 188);
  padding: 50rpx 30rpx;
  text-align: center;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
  border: 2rpx solid transparent;

}

.vip-card.selected {
  background-color:rgb(247, 225, 166);
}

.price {
  font-size: 38rpx;
  font-weight: bold;
  color:rgb(212, 38, 38);
  margin-bottom: 10rpx;
}

.duration {
  font-size: 36rpx;
  color: rgb(14, 12, 12);
  margin-top:30rpx;
  margin-bottom: 10rpx;
}

.daily-price {
  font-size: 24rpx;
  color: #666;
}

/* 开通按钮样式 */
.vip-btn {
  width: 90%;
  height: 88rpx;
  background: rgb(238, 85, 58);
  color: white;
  font-size: 36rpx;
  border-radius: 60rpx;
  margin: 40rpx auto;
  box-shadow: 0 4rpx 12rpx rgba(0, 122, 255, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 会员权益样式 */
.benefits-section {
  margin-top: 40rpx;
  padding: 30rpx;
  background:rgb(252, 249, 249);
  border-radius: 20rpx;
}

.benefits-title {
  font-size: 36rpx;
  font-weight: bold;
  color:rgb(224, 146, 37);
  margin-bottom: 30rpx;
  text-align: center;
}

.vip-btn-ios {
  width: 96%;
  margin: 40rpx auto 0; /* 修改了 margin 实现左右居中 */
  padding: 20rpx;
  background-color: #fff;
  border-radius: 20rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.1);
}

.ios-notice-line1 {
  font-size: 32rpx;
  color:rgb(18, 17, 17); /* 粉红色 */
  margin-bottom: 10rpx;
}

.ios-notice-line2 {
  font-size: 28rpx;
  color:rgb(224, 23, 70); /* 粉红色 */
}

.benefits-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.benefit-item {
  display: flex;
  align-items: flex-start;
  padding: 20rpx;
  background:rgb(244, 233, 207);
  border-radius: 32rpx;
}

.benefit-icon {
  width: 200rpx;
  height: 200rpx;
  margin-right: 20rpx;
  border-radius: 32rpx;
}

.benefit-content {
  flex: 1;
  padding-top: 10rpx;
}

.benefit-name {
  font-size: 32rpx;
  font-weight: bold;
  color:rgb(186, 155, 14);
  display: block;
  margin-bottom: 6rpx;
}

.benefit-desc {
  font-size: 28rpx;
  color:rgb(113, 99, 66);
  line-height:1.4;
  display: block;
  padding-top:8rpx;
}