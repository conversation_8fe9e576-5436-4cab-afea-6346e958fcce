{"version": 3, "sources": ["index.js"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.js", "sourcesContent": ["module.exports = ForeverAgent\nForeverAgent.SSL = ForeverAgentSSL\n\nvar util = require('util')\n  , Agent = require('http').Agent\n  , net = require('net')\n  , tls = require('tls')\n  , AgentSSL = require('https').Agent\n  \nfunction getConnectionName(host, port) {  \n  var name = ''\n  if (typeof host === 'string') {\n    name = host + ':' + port\n  } else {\n    // For node.js v012.0 and iojs-v1.5.1, host is an object. And any existing localAddress is part of the connection name.\n    name = host.host + ':' + host.port + ':' + (host.localAddress ? (host.localAddress + ':') : ':')\n  }\n  return name\n}    \n\nfunction ForeverAgent(options) {\n  var self = this\n  self.options = options || {}\n  self.requests = {}\n  self.sockets = {}\n  self.freeSockets = {}\n  self.maxSockets = self.options.maxSockets || Agent.defaultMaxSockets\n  self.minSockets = self.options.minSockets || ForeverAgent.defaultMinSockets\n  self.on('free', function(socket, host, port) {\n    var name = getConnectionName(host, port)\n\n    if (self.requests[name] && self.requests[name].length) {\n      self.requests[name].shift().onSocket(socket)\n    } else if (self.sockets[name].length < self.minSockets) {\n      if (!self.freeSockets[name]) self.freeSockets[name] = []\n      self.freeSockets[name].push(socket)\n      \n      // if an error happens while we don't use the socket anyway, meh, throw the socket away\n      var onIdleError = function() {\n        socket.destroy()\n      }\n      socket._onIdleError = onIdleError\n      socket.on('error', onIdleError)\n    } else {\n      // If there are no pending requests just destroy the\n      // socket and it will get removed from the pool. This\n      // gets us out of timeout issues and allows us to\n      // default to Connection:keep-alive.\n      socket.destroy()\n    }\n  })\n\n}\nutil.inherits(ForeverAgent, Agent)\n\nForeverAgent.defaultMinSockets = 5\n\n\nForeverAgent.prototype.createConnection = net.createConnection\nForeverAgent.prototype.addRequestNoreuse = Agent.prototype.addRequest\nForeverAgent.prototype.addRequest = function(req, host, port) {\n  var name = getConnectionName(host, port)\n  \n  if (typeof host !== 'string') {\n    var options = host\n    port = options.port\n    host = options.host\n  }\n\n  if (this.freeSockets[name] && this.freeSockets[name].length > 0 && !req.useChunkedEncodingByDefault) {\n    var idleSocket = this.freeSockets[name].pop()\n    idleSocket.removeListener('error', idleSocket._onIdleError)\n    delete idleSocket._onIdleError\n    req._reusedSocket = true\n    req.onSocket(idleSocket)\n  } else {\n    this.addRequestNoreuse(req, host, port)\n  }\n}\n\nForeverAgent.prototype.removeSocket = function(s, name, host, port) {\n  if (this.sockets[name]) {\n    var index = this.sockets[name].indexOf(s)\n    if (index !== -1) {\n      this.sockets[name].splice(index, 1)\n    }\n  } else if (this.sockets[name] && this.sockets[name].length === 0) {\n    // don't leak\n    delete this.sockets[name]\n    delete this.requests[name]\n  }\n  \n  if (this.freeSockets[name]) {\n    var index = this.freeSockets[name].indexOf(s)\n    if (index !== -1) {\n      this.freeSockets[name].splice(index, 1)\n      if (this.freeSockets[name].length === 0) {\n        delete this.freeSockets[name]\n      }\n    }\n  }\n\n  if (this.requests[name] && this.requests[name].length) {\n    // If we have pending requests and a socket gets closed a new one\n    // needs to be created to take over in the pool for the one that closed.\n    this.createSocket(name, host, port).emit('free')\n  }\n}\n\nfunction ForeverAgentSSL (options) {\n  ForeverAgent.call(this, options)\n}\nutil.inherits(ForeverAgentSSL, ForeverAgent)\n\nForeverAgentSSL.prototype.createConnection = createConnectionSSL\nForeverAgentSSL.prototype.addRequestNoreuse = AgentSSL.prototype.addRequest\n\nfunction createConnectionSSL (port, host, options) {\n  if (typeof port === 'object') {\n    options = port;\n  } else if (typeof host === 'object') {\n    options = host;\n  } else if (typeof options === 'object') {\n    options = options;\n  } else {\n    options = {};\n  }\n\n  if (typeof port === 'number') {\n    options.port = port;\n  }\n\n  if (typeof host === 'string') {\n    options.host = host;\n  }\n\n  return tls.connect(options);\n}\n"]}