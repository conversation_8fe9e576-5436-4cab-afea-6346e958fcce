{"version": 3, "sources": ["promise.js", "error.js"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;ACFA,ADGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.js", "sourcesContent": ["var Ajv = require('ajv')\nvar HARError = require('./error')\nvar schemas = require('har-schema')\n\nvar ajv\n\nfunction createAjvInstance () {\n  var ajv = new Ajv({\n    allErrors: true\n  })\n  ajv.addMetaSchema(require('ajv/lib/refs/json-schema-draft-06.json'))\n  ajv.addSchema(schemas)\n\n  return ajv\n}\n\nfunction validate (name, data) {\n  data = data || {}\n\n  // validator config\n  ajv = ajv || createAjvInstance()\n\n  var validate = ajv.getSchema(name + '.json')\n\n  return new Promise(function (resolve, reject) {\n    var valid = validate(data)\n\n    !valid ? reject(new HARError(validate.errors)) : resolve(data)\n  })\n}\n\nexports.afterRequest = function (data) {\n  return validate('afterRequest', data)\n}\n\nexports.beforeRequest = function (data) {\n  return validate('beforeRequest', data)\n}\n\nexports.browser = function (data) {\n  return validate('browser', data)\n}\n\nexports.cache = function (data) {\n  return validate('cache', data)\n}\n\nexports.content = function (data) {\n  return validate('content', data)\n}\n\nexports.cookie = function (data) {\n  return validate('cookie', data)\n}\n\nexports.creator = function (data) {\n  return validate('creator', data)\n}\n\nexports.entry = function (data) {\n  return validate('entry', data)\n}\n\nexports.har = function (data) {\n  return validate('har', data)\n}\n\nexports.header = function (data) {\n  return validate('header', data)\n}\n\nexports.log = function (data) {\n  return validate('log', data)\n}\n\nexports.page = function (data) {\n  return validate('page', data)\n}\n\nexports.pageTimings = function (data) {\n  return validate('pageTimings', data)\n}\n\nexports.postData = function (data) {\n  return validate('postData', data)\n}\n\nexports.query = function (data) {\n  return validate('query', data)\n}\n\nexports.request = function (data) {\n  return validate('request', data)\n}\n\nexports.response = function (data) {\n  return validate('response', data)\n}\n\nexports.timings = function (data) {\n  return validate('timings', data)\n}\n", "function HARError (errors) {\n  var message = 'validation failed'\n\n  this.name = 'HARError'\n  this.message = message\n  this.errors = errors\n\n  if (typeof Error.captureStackTrace === 'function') {\n    Error.captureStackTrace(this, this.constructor)\n  } else {\n    this.stack = (new Error(message)).stack\n  }\n}\n\nHARError.prototype = Error.prototype\n\nmodule.exports = HARError\n"]}