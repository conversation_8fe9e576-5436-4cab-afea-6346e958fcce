// utils/storyAudioManager.js
// 故事音频管理器 - 根据用户需求重新设计

/**
 * 主要功能：
 * 1. 检索已有音频（stories或user_story_audio）
 * 2. 下载并播放已有音频（渐进式下载，立即播放第一个）
 * 3. 流式生成新音频（边生成边播放）
 * 4. 保存音频到对应的数据集和字段
 */

export class StoryAudioManager {
  constructor(page) {
    this.page = page;
    this.audioCtx = null;
    this.currentAudio = null;
    this.downloadedSegments = []; // 已下载的音频段
    this.currentSegmentIndex = 0;
    this.isStreamingMode = false; // 是否为流式模式
    this.streamingComplete = false; // 流式是否完成
    this.segmentDurations = []; // 存储每个音频段的时长
    this.totalDuration = 0; // 总时长（秒）
    this.progressUpdateTimer = null; // 进度更新定时器
    this.isAborted = false; // 中断标志
    this.isDestroyed = false; // 销毁标志
  }



  /**
   * 检查管理器是否已被销毁或中断
   */
  isValid() {
    return !this.isDestroyed && !this.isAborted && !this.page.isPageHidden && this.page.audioManager === this;
  }

  /**
   * 主入口：加载并播放故事音频
   */
  async loadAndPlayStoryAudio(storyId, voiceType) {
    try {
      console.log(`开始加载故事音频: ${storyId}, 声音类型: ${voiceType}`);
      
      // 显示加载状态
      this.page.setData({
        isLoading: true,
        audioStatus: 'loading',
        showProgress: false
      });

      // 调用云函数检索音频
      const res = await wx.cloud.callFunction({
        name: 'getOrCreateStoryAudio',
        data: { storyId, voice: voiceType }
      });

      if (res.result.errCode === 0) {
        // 情况1: 找到已有音频
        await this.handleExistingAudio(res.result);
      } else if (res.result.errCode === 10) {
        // 情况2: 需要流式生成音频
        await this.handleStreamingGeneration(res.result);
      } else {
        throw new Error(res.result.errMsg || '获取音频失败');
      }

    } catch (error) {
      console.error('加载故事音频失败:', error);
      wx.showToast({ title: '加载音频失败', icon: 'none' });
      this.page.setData({
        isLoading: false,
        audioStatus: 'failed'
      });
    }
  }

  /**
   * 处理已有音频：下载并播放
   */
  async handleExistingAudio(result) {
    console.log('找到已有音频，开始下载播放', result);

    // 处理不同格式的audioUrl
    let audioUrls = [];
    if (Array.isArray(result.audioUrl)) {
      audioUrls = result.audioUrl;
    } else if (typeof result.audioUrl === 'string') {
      audioUrls = [result.audioUrl];
    } else if (result.audioUrl && typeof result.audioUrl === 'object') {
      // 如果是索引对象 {0: {...}, 1: {...}, 2: {...}}，按索引顺序提取
      const keys = Object.keys(result.audioUrl).sort((a, b) => parseInt(a) - parseInt(b));
      console.log('音频对象的键:', keys);

      for (const key of keys) {
        const audioItem = result.audioUrl[key];
        console.log(`处理音频段 ${key}:`, audioItem);

        if (typeof audioItem === 'string') {
          // 直接是URL字符串
          audioUrls.push(audioItem);
        } else if (audioItem && typeof audioItem === 'object') {
          // 是对象，尝试提取URL字段
          const url = audioItem.url || audioItem.audioUrl || audioItem.fileID || audioItem.tempFileURL;
          if (url && typeof url === 'string') {
            audioUrls.push(url);
          } else {
            console.warn(`音频段 ${key} 没有找到有效的URL字段:`, audioItem);
          }
        }
      }
    }

    if (audioUrls.length === 0) {
      console.error('没有找到有效的音频URL:', result.audioUrl);
      this.page.setData({
        isLoading: false,
        audioStatus: 'failed'
      });
      wx.showToast({ title: '音频格式错误', icon: 'none' });
      return;
    }

    console.log('解析出的音频URLs:', audioUrls);

    // 开始下载第一个音频段
    this.downloadAudioSegments(audioUrls);
  }

  /**
   * 渐进式下载音频段
   */
  async downloadAudioSegments(audioUrls) {
    console.log(`开始下载 ${audioUrls.length} 个音频段`);
    
    for (let i = 0; i < audioUrls.length; i++) {
      try {
        // 检查管理器是否还有效
        if (!this.isValid()) {
          console.log('音频管理器已无效，停止下载');
          return;
        }

        console.log(`下载音频段 ${i + 1}/${audioUrls.length}`);

        let downloadUrl = audioUrls[i];

        // 如果是云存储fileID，需要先获取临时URL
        if (downloadUrl.startsWith('cloud://')) {
          console.log(`转换云存储fileID为临时URL: ${downloadUrl}`);
          try {
            const tempUrlRes = await wx.cloud.getTempFileURL({
              fileList: [downloadUrl]
            });

            if (tempUrlRes.fileList && tempUrlRes.fileList.length > 0 && tempUrlRes.fileList[0].tempFileURL) {
              downloadUrl = tempUrlRes.fileList[0].tempFileURL;
              console.log(`获取临时URL成功: ${downloadUrl}`);
            } else {
              throw new Error('获取临时URL失败');
            }
          } catch (tempUrlError) {
            console.error(`获取临时URL失败:`, tempUrlError);
            if (i === 0) {
              this.page.setData({
                isLoading: false,
                audioStatus: 'failed'
              });
              wx.showToast({ title: '音频URL转换失败', icon: 'none' });
              return;
            }
            continue;
          }
        }

        // 下载音频文件到本地，添加重试机制
        let downloadRes;
        let retryCount = 0;
        const maxRetries = 3;

        while (retryCount < maxRetries) {
          try {
            // 在每次下载尝试前检查管理器是否还有效
            if (!this.isValid()) {
              console.log('下载前检查发现管理器已无效，停止下载');
              return;
            }

            console.log(`尝试下载音频段 ${i + 1}，第 ${retryCount + 1} 次尝试`);
            downloadRes = await new Promise((resolve, reject) => {
              wx.downloadFile({
                url: downloadUrl,
                success: resolve,
                fail: reject
              });
            });

            console.log(`下载结果 (尝试 ${retryCount + 1}):`, downloadRes);

            if (downloadRes.statusCode === 200 && downloadRes.tempFilePath) {
              console.log(`下载音频段 ${i + 1} 成功: ${downloadRes.tempFilePath}`);
              break; // 下载成功，跳出重试循环
            } else {
              console.log(`下载失败，状态码: ${downloadRes.statusCode}`);
              retryCount++;
              if (retryCount < maxRetries) {
                console.log(`等待 1 秒后重试...`);
                await new Promise(resolve => setTimeout(resolve, 1000));
              }
            }
          } catch (error) {
            retryCount++;
            if (retryCount < maxRetries) {
              // 只在第一次失败时提示，避免重复日志
              if (retryCount === 1) {
                console.log(`音频段 ${i + 1} 下载失败，正在重试...`);
              }
              await new Promise(resolve => setTimeout(resolve, 1000));
            } else {
              // 最后一次失败时提示将使用临时URL
              console.log(`音频段 ${i + 1} 下载失败，将使用临时URL播放`);
              downloadRes = { statusCode: 0, errMsg: error.errMsg || error.message };
            }
          }
        }

        if (downloadRes.statusCode === 200 && downloadRes.tempFilePath) {
          this.downloadedSegments.push(downloadRes.tempFilePath);

          // 如果是第一个音频段，立即开始播放
          if (i === 0) {
            // 再次检查管理器是否还有效
            if (!this.isValid()) {
              console.log('播放前检查发现管理器已无效，停止播放');
              return;
            }
            console.log('第一个音频段下载完成，开始播放');
            await this.startPlayback(downloadRes.tempFilePath);
          }

          // 检查是否所有音频段都下载完成
          console.log(`当前已下载: ${this.downloadedSegments.length}/${audioUrls.length} 个音频段`);
          if (this.downloadedSegments.length === audioUrls.length) {
            console.log('所有音频段下载完成，计算总时长并显示进度条');
            await this.calculateTotalDuration();
            this.page.setData({
              showProgress: true,
              allSegmentsDownloaded: true
            });
          } else {
            console.log(`还需要下载 ${audioUrls.length - this.downloadedSegments.length} 个音频段，暂不显示进度条`);
          }
        } else {
          console.error(`下载音频段 ${i + 1} 失败:`, downloadRes);
          console.log(`下载URL: ${downloadUrl}`);
          console.log(`错误信息: ${downloadRes.errMsg}`);

          // 如果下载失败，尝试重新获取临时URL
          const errMsg = downloadRes.errMsg || '';
          const errMsgStr = typeof errMsg === 'string' ? errMsg : JSON.stringify(errMsg);
          console.log(`错误信息类型: ${typeof errMsg}`);
          console.log(`错误信息字符串: ${errMsgStr}`);

          if (errMsgStr.includes('ENOENT')) {
            console.log(`临时URL可能已过期，尝试重新获取音频段 ${i + 1}`);
            try {
              // 重新获取临时URL
              const refreshResult = await wx.cloud.getTempFileURL({
                fileList: [audioUrls[i]]
              });

              if (refreshResult.fileList && refreshResult.fileList[0] && refreshResult.fileList[0].tempFileURL) {
                const newTempUrl = refreshResult.fileList[0].tempFileURL;
                console.log(`重新获取临时URL成功: ${newTempUrl}`);

                // 使用新的临时URL重试下载
                console.log(`使用新临时URL重试下载音频段 ${i + 1}`);
                const retryDownloadRes = await new Promise((resolve, reject) => {
                  wx.downloadFile({
                    url: newTempUrl,
                    success: resolve,
                    fail: reject
                  });
                });

                console.log(`重试下载结果:`, retryDownloadRes);

                if (retryDownloadRes.statusCode === 200 && retryDownloadRes.tempFilePath) {
                  console.log(`重试下载音频段 ${i + 1} 成功`);
                  this.downloadedSegments.push(retryDownloadRes.tempFilePath);

                  // 如果是第一个音频段，立即开始播放
                  if (i === 0) {
                    console.log('第一个音频段重试下载完成，开始播放');
                    await this.startPlayback(retryDownloadRes.tempFilePath);
                  }

                  // 检查是否所有音频段都下载完成
                  console.log(`当前已下载: ${this.downloadedSegments.length}/${audioUrls.length} 个音频段`);
                  if (this.downloadedSegments.length === audioUrls.length) {
                    console.log('所有音频段下载完成，计算总时长并显示进度条');
                    await this.calculateTotalDuration();
                    this.page.setData({
                      showProgress: true,
                      allSegmentsDownloaded: true
                    });
                  } else {
                    console.log(`还需要下载 ${audioUrls.length - this.downloadedSegments.length} 个音频段，暂不显示进度条`);
                  }
                  continue; // 继续下一个音频段
                }
              }
            } catch (retryError) {
              console.error(`重试下载音频段 ${i + 1} 失败:`, retryError);
            }
          }

          // 如果下载失败，尝试直接使用临时URL播放
          console.log(`下载失败，尝试直接使用临时URL播放音频段 ${i + 1}`);

          // 再次检查管理器是否还有效
          if (!this.isValid()) {
            console.log('使用临时URL播放前检查发现管理器已无效，停止播放');
            return;
          }

          this.downloadedSegments.push(downloadUrl); // 直接使用临时URL

          // 如果是第一个音频段，立即开始播放
          if (i === 0) {
            console.log('第一个音频段使用临时URL开始播放');
            await this.startPlayback(downloadUrl);
          }

          // 检查是否所有音频段都处理完成
          console.log(`当前已处理: ${this.downloadedSegments.length}/${audioUrls.length} 个音频段`);
          if (this.downloadedSegments.length === audioUrls.length) {
            console.log('所有音频段处理完成，计算总时长并显示进度条');
            await this.calculateTotalDuration();
            this.page.setData({
              showProgress: true,
              allSegmentsDownloaded: true
            });
          } else {
            console.log(`还需要处理 ${audioUrls.length - this.downloadedSegments.length} 个音频段，暂不显示进度条`);
          }
        }

      } catch (error) {
        console.error(`下载音频段 ${i + 1} 失败:`, error);
        // 如果是第一个音频段下载失败，显示错误
        if (i === 0) {
          this.page.setData({
            isLoading: false,
            audioStatus: 'failed'
          });
          wx.showToast({ title: '音频下载失败', icon: 'none' });
          return;
        }
      }
    }

    // 设置加载完成状态
    this.page.setData({
      isLoading: false
    });
  }

  /**
   * 计算所有音频段的总时长
   */
  async calculateTotalDuration() {
    this.totalDuration = 0;
    this.segmentDurations = [];

    for (let i = 0; i < this.downloadedSegments.length; i++) {
      try {
        const audioContext = wx.createInnerAudioContext();
        const duration = await new Promise((resolve) => {
          audioContext.src = this.downloadedSegments[i];
          audioContext.onCanplay(() => {
            const dur = audioContext.duration || 0;
            audioContext.destroy();
            resolve(dur);
          });
          audioContext.onError((error) => {
            console.error(`获取音频段 ${i + 1} 时长失败:`, error);
            audioContext.destroy();
            resolve(0); // 如果获取失败，使用0作为时长
          });
        });

        this.segmentDurations.push(duration);
        this.totalDuration += duration;
        console.log(`音频段 ${i + 1} 时长: ${duration}秒`);
      } catch (error) {
        console.error(`计算音频段 ${i + 1} 时长失败:`, error);
        this.segmentDurations.push(0);
      }
    }

    console.log(`总时长: ${this.totalDuration}秒`);
    console.log(`各段时长:`, this.segmentDurations);

    this.page.setData({
      duration: this.formatTime(this.totalDuration),
      totalDuration: this.totalDuration
    });
  }

  /**
   * 开始播放音频
   */
  async startPlayback(audioUrl) {
    try {
      // 在播放前检查管理器是否还有效
      if (!this.isValid()) {
        console.log('播放前检查发现管理器已无效，停止播放');
        return;
      }

      // 创建音频上下文
      if (this.audioCtx) {
        this.audioCtx.destroy();
      }

      this.audioCtx = wx.createInnerAudioContext({
        obeyMuteSwitch: false
      });

      // 同时设置 currentAudio 引用，用于播放控制
      this.currentAudio = this.audioCtx;

      this.audioCtx.src = audioUrl;
      this.audioCtx.preservesPitch = true;
      this.audioCtx.playbackRate = this.page.data.playbackRate || 1.0;

      // 设置音频事件监听器
      this.setupAudioListeners();

      // 开始播放
      this.audioCtx.play();
      
      // 更新页面状态
      this.page.setData({
        isPlaying: true,
        audioStatus: 'playing',
        isLoading: false
      });

      console.log('音频开始播放');

    } catch (error) {
      console.error('开始播放失败:', error);
      throw error;
    }
  }

  /**
   * 设置音频事件监听器
   */
  setupAudioListeners() {
    if (!this.audioCtx) return;

    this.audioCtx.onPlay(() => {
      // 在播放回调中检查管理器是否还有效
      if (!this.isValid()) {
        console.log('播放回调中检查发现管理器已无效，立即停止');
        try {
          this.audioCtx.stop();
        } catch (e) {
          console.log('停止音频时出错:', e);
        }
        return;
      }

      console.log('音频开始播放');
      this.page.setData({
        isPlaying: true,
        audioStatus: 'playing'
      });
    });

    this.audioCtx.onPause(() => {
      console.log('音频暂停');
      this.page.setData({
        isPlaying: false,
        audioStatus: 'paused'
      });
    });

    this.audioCtx.onEnded(() => {
      // 在 onEnded 回调中检查管理器是否还有效
      if (!this.isValid()) {
        console.log('onEnded 回调中检查发现管理器已无效，停止处理');
        return;
      }

      console.log('当前音频段播放完成');
      this.handleSegmentEnded();
    });

    this.audioCtx.onError((error) => {
      console.error('音频播放错误:', error);
      this.page.setData({
        isPlaying: false,
        audioStatus: 'error'
      });
    });

    this.audioCtx.onCanplay(() => {
      // 在 onCanplay 回调中检查管理器是否还有效
      if (!this.isValid()) {
        console.log('onCanplay 回调中检查发现管理器已无效，停止处理');
        return;
      }

      const duration = this.audioCtx.duration;
      if (duration && duration > 0) {
        console.log('音频可以播放，时长:', duration);

        // 不在这里显示进度条，只有在所有音频段下载完成后才显示
        // 进度条的显示由 downloadAudioSegments 方法中的逻辑控制
      }
    });

    this.audioCtx.onTimeUpdate(() => {
      if (this.page.data.showProgress && this.totalDuration > 0) {
        // 计算当前总播放时间
        const currentSegmentTime = this.audioCtx.currentTime || 0;
        const previousSegmentsDuration = this.segmentDurations
          .slice(0, this.currentSegmentIndex)
          .reduce((sum, duration) => sum + duration, 0);
        const totalCurrentTime = previousSegmentsDuration + currentSegmentTime;

        // 计算进度百分比
        const progress = (totalCurrentTime / this.totalDuration) * 100;

        this.page.setData({
          progress: Math.min(progress, 100),
          currentTime: this.formatTime(totalCurrentTime)
        });
      }
    });
  }

  /**
   * 处理音频段播放完成
   */
  handleSegmentEnded() {
    // 在处理音频段结束时检查管理器是否还有效
    if (!this.isValid()) {
      console.log('handleSegmentEnded 中检查发现管理器已无效，停止处理');
      return;
    }

    if (this.isStreamingMode && !this.streamingComplete) {
      // 流式模式下，播放完成但流式未完成，等待更多数据
      console.log('流式播放段完成，等待更多数据');
      return;
    }

    // 检查是否有下一个音频段
    if (this.currentSegmentIndex + 1 < this.downloadedSegments.length) {
      // 播放下一个音频段
      this.currentSegmentIndex++;
      const nextSegmentUrl = this.downloadedSegments[this.currentSegmentIndex];
      console.log(`播放下一个音频段: ${this.currentSegmentIndex + 1}/${this.downloadedSegments.length}`);
      
      this.audioCtx.src = nextSegmentUrl;
      this.audioCtx.play();
    } else {
      // 所有音频段播放完成
      console.log('所有音频播放完成');
      this.page.setData({
        isPlaying: false,
        audioStatus: 'completed',
        progress: 0,
        currentTime: '00:00'
      });
    }
  }

  /**
   * 处理流式生成音频
   */
  async handleStreamingGeneration(result) {
    console.log('开始流式生成音频');
    
    this.isStreamingMode = true;
    this.streamingComplete = false;

    // 调用页面的流式播放方法
    if (typeof this.page.startSiliconFlowStreaming === 'function') {
      await this.page.startSiliconFlowStreaming(
        result.storyData,
        result.voiceType,
        result.ticket
      );
    } else {
      throw new Error('页面不支持流式播放');
    }
  }

  /**
   * 切换播放/暂停状态
   */
  togglePlayback() {
    console.log('togglePlayback 被调用');
    console.log('currentAudio:', this.currentAudio);
    console.log('audioCtx:', this.audioCtx);
    console.log('当前播放状态:', this.page.data.isPlaying);

    if (!this.currentAudio) {
      console.log('没有音频可以播放');
      return;
    }

    if (this.page.data.isPlaying) {
      // 暂停播放
      console.log('暂停音频播放');
      this.currentAudio.pause();
      this.page.setData({
        isPlaying: false,
        audioStatus: 'paused'
      });
    } else {
      // 开始/恢复播放
      console.log('开始/恢复音频播放');
      this.currentAudio.play();
      this.page.setData({
        isPlaying: true,
        audioStatus: 'playing'
      });
    }
  }

  /**
   * 跳转到指定时间（进度条拖动）
   */
  seekToTime(targetTime) {
    if (!this.segmentDurations || this.segmentDurations.length === 0) {
      console.log('音频段时长信息不可用，无法跳转');
      return;
    }

    console.log(`跳转到时间: ${targetTime}秒`);

    // 找到目标时间对应的音频段
    let accumulatedTime = 0;
    let targetSegmentIndex = 0;
    let targetSegmentTime = targetTime;

    for (let i = 0; i < this.segmentDurations.length; i++) {
      if (accumulatedTime + this.segmentDurations[i] >= targetTime) {
        targetSegmentIndex = i;
        targetSegmentTime = targetTime - accumulatedTime;
        break;
      }
      accumulatedTime += this.segmentDurations[i];
    }

    console.log(`目标音频段: ${targetSegmentIndex + 1}, 段内时间: ${targetSegmentTime}秒`);

    // 如果需要切换到不同的音频段
    if (targetSegmentIndex !== this.currentSegmentIndex) {
      this.currentSegmentIndex = targetSegmentIndex;
      this.audioCtx.src = this.downloadedSegments[targetSegmentIndex];

      // 等待新音频段加载完成后跳转
      const onCanplay = () => {
        this.audioCtx.seek(targetSegmentTime);
        this.audioCtx.offCanplay(onCanplay);

        // 如果之前在播放，继续播放
        if (this.page.data.isPlaying) {
          this.audioCtx.play();
        }
      };

      this.audioCtx.onCanplay(onCanplay);
    } else {
      // 在当前音频段内跳转
      this.audioCtx.seek(targetSegmentTime);
    }
  }

  /**
   * 格式化时间显示
   */
  formatTime(seconds) {
    if (!seconds || isNaN(seconds)) return '00:00';
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  }

  /**
   * 播放控制方法
   */
  play() {
    if (this.audioCtx) {
      this.audioCtx.play();
    }
  }

  pause() {
    if (this.audioCtx) {
      this.audioCtx.pause();
    }
  }

  stop() {
    if (this.audioCtx) {
      this.audioCtx.stop();
    }
  }

  /**
   * 停止播放（不销毁，可以重新开始）
   */
  stopPlayback() {
    console.log('停止音频播放');

    if (this.audioCtx) {
      try {
        this.audioCtx.stop();
        console.log('音频上下文已停止');
      } catch (e) {
        console.error('停止音频上下文时出错:', e);
      }
    }

    // 更新页面状态
    this.page.setData({
      isPlaying: false,
      audioStatus: 'stopped'
    });

    console.log('音频播放已完全停止');
  }

  /**
   * 完全重置播放器（停止下载、播放，重置所有状态）
   */
  reset() {
    console.log('完全重置音频播放器');

    // 设置中断标志
    this.isAborted = true;

    // 停止播放
    this.stopPlayback();

    // 销毁音频上下文
    if (this.audioCtx) {
      try {
        this.audioCtx.destroy();
        this.audioCtx = null;
        console.log('音频上下文已销毁');
      } catch (e) {
        console.error('销毁音频上下文时出错:', e);
      }
    }

    // 重置所有状态
    this.downloadedSegments = [];
    this.currentSegmentIndex = 0;
    this.isStreamingMode = false;
    this.streamingComplete = false;
    this.segmentDurations = [];
    this.totalDuration = 0;
    this.currentAudio = null;
    this.isAborted = false; // 重置中断标志

    console.log('音频播放器已完全重置');
  }

  /**
   * 清理资源
   */
  destroy() {
    console.log('销毁音频管理器');
  
    // 立即设置销毁标志，阻止所有后续操作
    this.isDestroyed = true;
    this.isAborted = true;
  
    // 清理进度更新定时器
    if (this.progressUpdateTimer) {
      clearInterval(this.progressUpdateTimer);
      this.progressUpdateTimer = null;
    }
  
    // 先停止播放
    this.stopPlayback();
  
    // 然后销毁资源
    if (this.audioCtx) {
      try {
        // 移除所有事件监听器
        this.audioCtx.offPlay();
        this.audioCtx.offPause();
        this.audioCtx.offEnded();
        this.audioCtx.offError();
        this.audioCtx.offCanplay();
        this.audioCtx.offTimeUpdate();
        
        this.audioCtx.stop();
        this.audioCtx.destroy();
        this.audioCtx = null;
        console.log('音频上下文已销毁');
      } catch (e) {
        console.error('销毁音频上下文时出错:', e);
      }
    }
  
    this.downloadedSegments = [];
    this.currentSegmentIndex = 0;
    this.isStreamingMode = false;
    this.streamingComplete = false;
    console.log('音频管理器资源已清理');
  }
}
