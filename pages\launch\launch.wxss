.container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  padding: 40rpx;
  height: 100vh;
  background-image: url('https://636c-cloudbase-1gahfeezccaf7d37-1354986900.tcb.qcloud.la/backgroud.png');
  background-size: 100% 100%;
  background-attachment: fixed;
  background-position: center;
  background-repeat: no-repeat;
}

.logo {
  width: 360rpx;
  height: 360rpx;
  margin-top: calc(100vh / 6);
  border-radius: 44rpx;
}

.title {
  font-size: 42rpx;
  font-weight: bold;
  color:rgb(225, 218, 218);
  margin-bottom: 20rpx;
  margin-top:30rpx;
}

.subtitle {
  font-size: 30rpx;
  color:rgb(186, 186, 186);
  margin-bottom: 60rpx;
}

/* 隐私协议弹窗样式 */
.privacy-popup {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 999;
  display: flex;
  justify-content: center;
  align-items: center;
}

.privacy-container {
  width: 90%;
  max-height: 80vh;
  background-color: #ffffff;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.15);
  z-index: 1000;
  padding: 30rpx;
  box-sizing: border-box;
}

.privacy-title {
  font-size: 36rpx;
  font-weight: bold;
  text-align: center;
  margin-bottom: 30rpx;
  color: #333;
}

.privacy-content {
  height: 60vh;
  margin-bottom: 30rpx;
}

.privacy-content text {
  font-size: 28rpx;
  line-height: 1.6;
  color: #666;
}

.privacy-actions {
  display: flex;
  justify-content: space-between;
  margin-bottom: 30rpx;
}

.privacy-btn {
  width: 45%;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  border-radius: 40rpx;
  font-size: 32rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0;
}

.disagree {
  background-color: #f5f5f5;
  color: #999;
}

.agree {
  background-color: #4CAF50;
  color: #fff;
}