// utils/preloadedStoryPlayer.js
// 根据用户需求重新设计的故事音频播放器
import { StoryAudioManager } from './storyAudioManager.js';

export async function setupPreloadedStory(page, options) {
  const storyId = options.storyId;
  const savedRate = wx.getStorageSync('playbackRate');
  if (savedRate) {
      page.setData({ playbackRate: savedRate });
  }

  // 根据用户需求，正确映射声音类型
  const babyInfo = wx.getStorageSync('babyInfo');
  let voiceType = 'female'; // 默认female
  if (babyInfo && babyInfo.voiceType) {
      // 直接使用用户选择的声音类型：female, male, mom, dad
      voiceType = babyInfo.voiceType;
  }

  console.log(`设置故事播放: ${storyId}, 声音类型: ${voiceType}`);

  const { fromHistory, fromHome } = options;
  page.setData({
      storyId,
      fromHistory: !!fromHistory,
      fromHome: !!fromHome,
      showProgress: false,
      retryCount: 0,
      maxRetries: 3,
      isLiked: false,
      voiceType: voiceType,
      isLoading: true,
      audioStatus: 'loading'
  });

  // 获取故事内容
  await loadStoryContent(page, storyId);

  // 使用新的音频管理器加载和播放音频
  if (!page.audioManager) {
      page.audioManager = new StoryAudioManager(page);
  }

  await page.audioManager.loadAndPlayStoryAudio(storyId, voiceType);

  // 记录历史和检查收藏状态
  page.recordStoryHistory(storyId);
  page.checkIfLiked(storyId);
}

/**
 * 加载故事内容
 */
async function loadStoryContent(page, storyId) {
  try {
      const storyRes = await wx.cloud.callFunction({
          name: 'getStoryById',
          data: { storyId }
      });

      if (storyRes.result && storyRes.result.errCode === 0) {
          const storyData = storyRes.result.story;
          page.setData({
              title: storyData.title,
              subtitle: storyData.subtitle,
              content: storyData.content,
              question: storyData.question,
              image: storyData.image || '',
              storyData: storyData,
              status: 'completed' // 设置状态为完成，显示收藏和答案按钮
          });
          console.log('故事内容加载成功:', storyData.title);
      } else {
          throw new Error('获取故事内容失败');
      }
  } catch (error) {
      console.error('加载故事内容失败:', error);
      wx.showToast({ title: '加载故事失败', icon: 'none' });
  }
}


