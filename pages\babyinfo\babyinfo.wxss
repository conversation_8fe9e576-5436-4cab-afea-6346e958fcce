.babyinfo-container {
  padding: 30rpx;
  height: 100vh;
  box-sizing: border-box;
  background-image: url('https://636c-cloudbase-1gahfeezccaf7d37-1354986900.tcb.qcloud.la/backgroud.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
  background-position: center;
  background-attachment: fixed;
}

.custom-nav-bar {
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  width: 100%;
  box-sizing: border-box;
  margin-bottom:50px;
}

.nav-title {
  font-size: 38rpx;
  flex: 1;
  text-align: center;
  margin-right: 40rpx;
  color:rgb(219, 219, 216);
}

.avatar-wrapper {
  background-color: transparent;
  padding: 0;
  width: 156rpx;
  height: 160rpx;
  margin: 0 auto 60rpx;
  display: block;
  overflow: hidden;
}

.avatar-wrapper::after {
  border: none;
}

.avatar {
  width: 156rpx;
  height: 156rpx;
  border-radius: 50%;
  border: 3rpx solid #eee;
  object-fit: cover;
}

.gender-selection {
  display: flex;
  justify-content: space-between;
  width: 90%;
  margin: 0 auto 80rpx;
}

.gender-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex-grow: 1;
  padding: 16rpx 0;
  background-color:rgb(180, 179, 179);
  border-radius: 36rpx;
  margin: 0 20rpx;
}

.gender-btn.active {
  background-color: #e6f7ff;
  background-color: rgba(76, 175, 80);
}

.gender-icon {
  font-size: 70rpx;
  margin-bottom: 10rpx;
}

.nickname-input {
  margin: 0 auto;
  text-align: left;
  width: 76%;
  font-size: 32rpx;
  color: #ffffff;
  margin-bottom: 40rpx;
  padding: 20rpx;
  background-color: transparent;
  border-radius: 0;
  border-bottom: 2rpx solid #999;
}

.boy-icon {
  color:rgb(226, 229, 233);
}

.girl-icon {
  color: #ff69b4;
}

.gender-text {
  font-size: 32rpx;
  color:rgb(247, 243, 243);
}

.form-container {
  width: 82%;
  margin: 0 auto 40rpx;
  padding: 0 5%;
}

.form-item {
  width: 100%;
  display: flex;
  align-items: center;
  padding: 30rpx 0;
  border-bottom: 2rpx solid #999;
}

.label {
  font-size: 32rpx;
  color: #666;
  width: 140rpx;
  text-align: left;
}

.picker-wrapper {
  flex: 1;
  text-align: center;
  margin-left: 20rpx;
}

.picker {
  text-align: center;
  font-size: 32rpx;
  color: #fff;
  display: flex;
  justify-content: center;
}

.picker.placeholder {
  color:rgb(131, 131, 134);
}

.btn-container {
  position: fixed;
  bottom: 30rpx;
  left: 0;
  right: 0;
  padding: 0 5%;
}

.next-btn {
  background-color:rgb(77, 194, 81);
  color: #fff;
  border-radius: 32rpx;
  font-size: 32rpx;
  width: 90%;
  padding:28rpx;
  margin-bottom:30rpx;
}

.next-btn.disabled {
  background-color:rgb(123, 123, 123);
  color:rgb(161, 159, 159);
}
