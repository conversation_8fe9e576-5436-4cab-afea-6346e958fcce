// pages/history/history.js
Page({
  data: {
    stories: [],
    isRecording: false,
    recorderManager: null,
    aiGenerated: [],
    classicStories: [],
    refreshing: false,
    // 添加一个变量来跟踪当前滑动的项
    activeSlideItemId: null,
    // 添加tab相关变量
    activeTab: 'favorite', // 默认显示收藏tab
    favoriteStories: [], // 收藏的故事
    historyStories: [], // 听过的故事
    isLiked: false // 是否已收藏
  },

  onLoad() {
    // this.initRecorder();
    this.loadStories();
  },

  onShow() {
    this.loadStories();
    // 每次页面显示时重置所有滑动状态
    this.resetAllSlides();
  },
  
  // 切换tab
  switchTab(e) {
    const tab = e.currentTarget.dataset.tab;
    if (tab !== this.data.activeTab) {
      this.setData({ 
        activeTab: tab
      });
    }
  },
  
  // 已移除swiper组件，不再需要handleSwiperChange函数
  

  onPullDownRefresh() {
    this.setData({ refreshing: true });
    this.loadStories().then(() => {
      wx.stopPullDownRefresh();
      this.setData({ refreshing: false });
    }).catch(() => {
      wx.stopPullDownRefresh();
      this.setData({ refreshing: false });
    });
  },

  onRefresh() {
    if (this.data.refreshing) return;
    this.setData({ refreshing: true });
    wx.showToast({
      title: '正在刷新...',
      icon: 'loading',
      duration: 2000
    });
    this.loadStories().then(() => {
      this.setData({ refreshing: false });
      wx.showToast({
        title: '刷新成功',
        icon: 'success',
        duration: 1500
      });
    }).catch(() => {
      this.setData({ refreshing: false });
      wx.showToast({
        title: '刷新失败',
        icon: 'error',
        duration: 1500
      });
    });
  },


  loadStories() {
    const db = wx.cloud.database();
    const _ = db.command;
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    return new Promise(async (resolve, reject) => {
      try {
        const openid = wx.getStorageSync('openid');
        if (!openid) {
          wx.reLaunch({ url: '/pages/login/login' });
          wx.showToast({ title: '请先登录', icon: 'none' });
          this.setData({ favoriteStories: [], historyStories: [], stories: [], activeSlideItemId: null });
          resolve();
          return;
        }

        const myStoriesRes = await db.collection('myStories').where({ _openid: openid }).get();
        let likedIds = [];
        let historyIds = [];
        let likedStoriesMap = {};
        let historyStoriesMap = {};
        
        if (myStoriesRes.data && myStoriesRes.data.length > 0) {
          const userRecord = myStoriesRes.data[0];
          
          if (userRecord.likedStories && userRecord.likedStories.length > 0) {
            userRecord.likedStories.forEach(item => {
              likedIds.push(item.storyId);
              likedStoriesMap[item.storyId] = item.timestamp;
            });
          }
          
          if (userRecord.historyStories && userRecord.historyStories.length > 0) {
            userRecord.historyStories.forEach(item => {
              historyIds.push(item.storyId);
              historyStoriesMap[item.storyId] = item.timestamp;
            });
          }
        }

        likedIds = [...new Set(likedIds)];
        historyIds = [...new Set(historyIds)];

        // 分批查询函数
        const batchQuery = async (collection, ids, batchSize = 20) => {
          let result = [];
          for (let i = 0; i < ids.length; i += batchSize) {
            const batch = ids.slice(i, i + batchSize);
            if (batch.length > 0) {
              const res = await db.collection(collection).where({ _id: _.in(batch) }).get();
              result = result.concat(res.data || []);
            }
          }
          return result;
        };

        // 根据ID获取收藏的故事（分批）
        let favoriteStories = [];
        if (likedIds.length > 0) {
          favoriteStories = await batchQuery('stories', likedIds);
          // 根据时间戳排序
          if (Object.keys(likedStoriesMap).length > 0) {
            favoriteStories.sort((a, b) => (new Date(likedStoriesMap[b._id] || b.createTime)) - (new Date(likedStoriesMap[a._id] || a.createTime)));
          } else {
            favoriteStories.sort((a, b) => new Date(b.createTime) - new Date(a.createTime));
          }
        }

        // 根据ID获取历史故事（分批）
        let historyStories = [];
        if (historyIds.length > 0) {
          historyStories = await batchQuery('stories', historyIds);
          // 根据时间戳排序
          if (Object.keys(historyStoriesMap).length > 0) {
            historyStories.sort((a, b) => (new Date(historyStoriesMap[b._id] || b.createTime)) - (new Date(historyStoriesMap[a._id] || a.createTime)));
          } else {
            historyStories.sort((a, b) => new Date(b.createTime) - new Date(a.createTime));
          }
        }

        const formatTime = (time) => {
          const date = new Date(time);
          return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`;
        };

        const processedFavoriteStories = favoriteStories.map(story => ({
          ...story,
          createTime: formatTime(likedStoriesMap[story._id] || story.createTime),
          slideOpen: false,
          title: story.title || '正在生成的故事...',
          content: story.content || '请稍候...',
          isCustom: !!story._openid
        }));

        const processedHistoryStories = historyStories.map(story => ({
          ...story,
          createTime: formatTime(historyStoriesMap[story._id] || story.createTime),
          slideOpen: false,
          title: story.title || '正在生成的故事...',
          content: story.content || '请稍候...',
          isCustom: !!story._openid
        }));

        this.setData({
          favoriteStories: processedFavoriteStories,
          historyStories: processedHistoryStories,
          stories: this.data.activeTab === 'favorite' ? processedFavoriteStories : processedHistoryStories,
          activeSlideItemId: null
        });

        resolve();
      } catch (err) {
        console.error('[History Load] An error occurred:', err);
        wx.showToast({ title: '获取故事��表失败', icon: 'none' });
        reject(err);
      }
    });
  },

  customStory() {
    wx.navigateTo({
      url: '/pages/custom-story/custom-story'
    });
  },

  onTabHistory() {
    // 阻止事件冒泡
  },

  ToHome() {
    wx.switchTab({
      url: '/pages/home/<USER>'
    });
  },

  async onStoryTap(e) {
    const { storyid } = e.currentTarget.dataset;
    
    // 引入权限管理模块
    const permission = require('../../utils/permission');
    
    // 检查听故事权限
    const checkResult = await permission.checkPermission(permission.PERMISSION_TYPES.LISTEN_HISTORY);
    if (!checkResult.hasPermission) {
      // 没有权限，显示会员提示弹窗
      permission.showVipModal(checkResult.message);
      return;
    }

    // 更新使用记录
    await permission.updateUsageRecord(permission.PERMISSION_TYPES.LISTEN_HISTORY);
    
    // 记录用户听过的故事
    this.recordStoryHistory(storyid);
    wx.navigateTo({
      url: `/pages/story/story?storyId=${storyid}`
    });
  },

  // 记录用户听过的故事
  async recordStoryHistory(storyId) {
    try {
      const openid = wx.getStorageSync('openid');
      if (!openid) return;

      const db = wx.cloud.database();
      const _ = db.command;
      
      // 查询用户记录
      const res = await db.collection('myStories').where({
        _openid: openid // 使用_openid字段查询，这是云数据库自动创建的字段
      }).get();

      const timestamp = new Date();
      const historyItem = {
        storyId: storyId,
        timestamp: timestamp
      };

      if (res.data.length === 0) {
        // 如果用户不存在，创建用户记录
        await db.collection('myStories').add({
          data: {
            // 不需要手动添加openid字段，云函数会自动添加_openid字段
            historyStories: [historyItem],
            likedStories: [],
            createTime: new Date(),
            updateTime: new Date()
          }
        });
      } else {
        const userRecord = res.data[0];
        
        // 检查是否有旧数据结构
        if (userRecord.historyId) {
          // 如果是旧数据结构，创建新的数组字段
          await db.collection('myStories').doc(userRecord._id).update({
            data: {
              historyStories: [historyItem],
              historyId: null,
              updateTime: new Date()
            }
          });
        } else {
          // 使用新数据结构
          const historyStories = userRecord.historyStories || [];
          
          // 检查是否已存在该故事的历史记录
          const existingIndex = historyStories.findIndex(item => item.storyId === storyId);
          
          if (existingIndex === -1) {
            // 如果不存在，添加到数组
            await db.collection('myStories').doc(userRecord._id).update({
              data: {
                historyStories: _.push(historyItem),
                updateTime: new Date()
              }
            });
          } else {
            // 如果存在，更新时间
            // 由于无法直接更新数组中的特定元素，需要先移除再添加
            historyStories.splice(existingIndex, 1);
            historyStories.push(historyItem);
            
            await db.collection('myStories').doc(userRecord._id).update({
              data: {
                historyStories: historyStories,
                updateTime: new Date()
              }
            });
          }
        }
      }
    } catch (err) {
      console.error('记录历史失败', err);
    }
  },

  // 收藏/取消收藏故事
  async toggleLike(e) {
    try {
      const { storyid } = e.currentTarget.dataset;
      const openid = wx.getStorageSync('openid');
      if (!openid) {
        wx.showToast({
          title: '请先登录',
          icon: 'none'
        });
        return;
      }

      const db = wx.cloud.database();
      const _ = db.command;
      
      // 查询用户记录
      const res = await db.collection('myStories').where({
        _openid: openid // 使用_openid字段查询，这是云数据库自动创建的字段
      }).get();

      // 如果已经收藏，则直接执行取消收藏操作
      const userRecord = res.data.length > 0 ? res.data[0] : null;
      const likedStories = userRecord?.likedStories || [];
      const existingIndex = likedStories.findIndex(item => item.storyId === storyid);
      
      if (existingIndex !== -1) {
        // 已收藏，取消收藏
        likedStories.splice(existingIndex, 1);
        await db.collection('myStories').doc(userRecord._id).update({
          data: {
            likedStories: likedStories,
            updateTime: new Date()
          }
        });
        wx.showToast({
          title: '已取消收藏',
          icon: 'success'
        });
        // 重新加载故事列表
        this.loadStories();
        return;
      }
      
      // 检查收藏权限
      const permission = require('../../utils/permission');
      
      // 首先检查用户是否为会员
      const isVip = await permission.checkIsVip();
      
      // 如果不是会员，才检查收藏数量限制
      if (!isVip) {
        // 获取用户当前的实际收藏数量
        const currentFavoriteCount = likedStories.length;
        
        // 先检查实际收藏数量是否已达到限制
        if (currentFavoriteCount >= permission.FREE_LIMITS[permission.PERMISSION_TYPES.FAVORITE]) {
          // 如果实际收藏数量已达到限制，显示会员提示弹窗
          permission.showVipModal(`非会员最多只能收藏${permission.FREE_LIMITS[permission.PERMISSION_TYPES.FAVORITE]}个故事，开通会员享受无限收藏特权！`);
          return;
        }
        
        // 再检查权限系统中记录的收藏数量
        const checkResult = await permission.checkPermission(permission.PERMISSION_TYPES.FAVORITE);
        if (!checkResult.hasPermission) {
          // 没有权限，显示会员提示弹窗
          permission.showVipModal(checkResult.message);
          return;
        }
      }

      const timestamp = new Date();
      const likedItem = {
        storyId: storyid,
        timestamp: timestamp
      };

      if (res.data.length === 0) {
        // 如果用户不存在，创建用户记录并添加收藏
        await db.collection('myStories').add({
          data: {
            // 不需要手动添加openid字段，云函数会自动添加_openid字段
            likedStories: [likedItem],
            historyStories: [],
            createTime: new Date(),
            updateTime: new Date()
          }
        });
        // 更新使用记录
        permission.updateUsageRecord(permission.PERMISSION_TYPES.FAVORITE);
        wx.showToast({
          title: '收藏成功',
          icon: 'success'
        });
      } else {
        // 检查是否有旧数据结构
        if (userRecord.likedId) {
          // 如果是旧数据结构，创建新的数组字段
          await db.collection('myStories').doc(userRecord._id).update({
            data: {
              likedStories: [likedItem],
              likedId: null,
              updateTime: new Date()
            }
          });
          // 更新使用记录
          permission.updateUsageRecord(permission.PERMISSION_TYPES.FAVORITE);
          wx.showToast({
            title: '收藏成功',
            icon: 'success'
          });
        } else {
          // 使用新数据结构
          // 未收藏，添加收藏
          await db.collection('myStories').doc(userRecord._id).update({
            data: {
              likedStories: _.push(likedItem),
              updateTime: new Date()
            }
          });
          // 更新使用记录
          permission.updateUsageRecord(permission.PERMISSION_TYPES.FAVORITE);
          wx.showToast({
            title: '收藏成功',
            icon: 'success'
          });
        }
      }

      // 重新加载故事列表
      this.loadStories();
    } catch (err) {
      console.error('收藏操作失败', err);
      wx.showToast({
        title: '操作失败',
        icon: 'none'
      });
    }
  },

  // 单条故事删除（从收藏或历史记录中删除）
  deleteStory(e) {
    const { storyid } = e.currentTarget.dataset;
    wx.showModal({
      title: '确认删除',
      content: '从列表中删除该故事？',
      success: async (res) => {
        if (res.confirm) {
          try {
            const db = wx.cloud.database();
            const openid = wx.getStorageSync('openid');
            if (!openid) {
              wx.showToast({
                title: '请先登录',
                icon: 'none'
              });
              return;
            }

            // 根据当前tab确定要删除的是收藏还是历史记录
            const isFromFavorite = this.data.activeTab === 'favorite';
            
            // 查询用户记录
            const res = await db.collection('myStories').where({
              _openid: openid
            }).get();

            if (res.data.length > 0) {
              const userRecord = res.data[0];
              
              // 检查是否有旧数据结构
              if ((isFromFavorite && userRecord.likedId) || (!isFromFavorite && userRecord.historyId)) {
                // 旧数据结构，将对应字段设为null
                const queryField = isFromFavorite ? 'likedId' : 'historyId';
                await db.collection('myStories').doc(userRecord._id).update({
                  data: {
                    [queryField]: null,
                    updateTime: new Date()
                  }
                });
              } else {
                // 新数据结构，从数组中删除
                const arrayField = isFromFavorite ? 'likedStories' : 'historyStories';
                const storiesArray = userRecord[arrayField] || [];
                
                // 找到要删除的项的索引
                const indexToRemove = storiesArray.findIndex(item => item.storyId === storyid);
                
                if (indexToRemove !== -1) {
                  // 从数组中删除
                  storiesArray.splice(indexToRemove, 1);
                  
                  // 更新数据库
                  await db.collection('myStories').doc(userRecord._id).update({
                    data: {
                      [arrayField]: storiesArray,
                      updateTime: new Date()
                    }
                  });
                }
              }

              // 刷新页面数据
              this.loadStories();
              wx.showToast({
                title: '删除成功',
                icon: 'success'
              });
            } else {
              wx.showToast({
                title: '记录不存在',
                icon: 'none'
              });
            }
          } catch (err) {
            console.error('删除故事失败', err);
            wx.showToast({
              title: '删除失败',
              icon: 'error'
            });
          }
        }
      }
    });
  },

  // 清空当前tab的记录（收藏或历史）
  clearStories() {
    const tabName = this.data.activeTab === 'favorite' ? '收藏' : '历史';
    wx.showModal({
      title: `确认清空${tabName}`,
      content: `确定要清空所有${tabName}记录吗？`,
      success: async (res) => {
        if (res.confirm) {
          try {
            const db = wx.cloud.database();
            const openid = wx.getStorageSync('openid');
            if (!openid) {
              wx.showToast({
                title: '请先登录',
                icon: 'none'
              });
              return;
            }

            // 根据当前tab确定要清空的是收藏还是历史记录
            const isFromFavorite = this.data.activeTab === 'favorite';
            const arrayField = isFromFavorite ? 'likedStories' : 'historyStories';
            const oldField = isFromFavorite ? 'likedId' : 'historyId';
            
            // 查询用户记录
            const res = await db.collection('myStories').where({
              _openid: openid
            }).get();

            if (res.data.length > 0) {
              const updatePromises = [];
              
              // 处理每条记录
              res.data.forEach(record => {
                // 检查是否有旧数据结构
                if (record[oldField]) {
                  // 旧数据结构，将对应字段设为null
                  updatePromises.push(
                    db.collection('myStories').doc(record._id).update({
                      data: {
                        [oldField]: null,
                        updateTime: new Date()
                      }
                    })
                  );
                } else if (record[arrayField] && record[arrayField].length > 0) {
                  // 新数据结构，清空数组
                  updatePromises.push(
                    db.collection('myStories').doc(record._id).update({
                      data: {
                        [arrayField]: [],
                        updateTime: new Date()
                      }
                    })
                  );
                }
              });

              if (updatePromises.length > 0) {
                await Promise.all(updatePromises);

                // 更新前端状态
                if (this.data.activeTab === 'favorite') {
                  this.setData({
                    favoriteStories: [],
                    stories: [],
                    activeSlideItemId: null
                  });
                } else {
                  this.setData({
                    historyStories: [],
                    stories: [],
                    activeSlideItemId: null
                  });
                }

                wx.showToast({
                  title: '清空成功',
                  icon: 'success'
                });
              } else {
                wx.showToast({
                  title: '没有记录需要清空',
                  icon: 'none'
                });
              }
            } else {
              wx.showToast({
                title: '没有记录需要清空',
                icon: 'none'
              });
            }
          } catch (err) {
            console.error(`清空${tabName}失败`, err);
            wx.showToast({
              title: '清空失败',
              icon: 'none'
            });
          }
        }
      }
    });
  },
  
  
  handleSlideStart(e) {
    const { storyid, type } = e.currentTarget.dataset;
    
    // 如果有其他已打开的项，先关闭
    this.resetAllSlides();
    
    // 设置当前活动项
    this.setData({
      activeSlideItemId: storyid
    });
    
    // 打开当前项的删除按钮
    let stories = type === 'ai' ? [...this.data.aiGenerated] : [...this.data.classicStories];
    const index = stories.findIndex(item => item._id === storyid);
    if (index !== -1) {
      stories[index].slideOpen = true;
      this.setData({
        [type === 'ai' ? 'aiGenerated' : 'classicStories']: stories
      });
    }
  },
  
  // 处理点击页面其他区域时关闭所有滑动项
  onPageTap(e) {
    // 检查点击是否发生在删除按钮区域或滑动项区域
    const dataset = e.target.dataset || {};
    if (dataset.preventClose) {
      return;
    }
    
    this.resetAllSlides();
  },
  
  // 处理页面滚动时关闭所有滑动项
  onPageScroll() {
    this.resetAllSlides();
  },
  
  // 重置所有滑动状态
  resetAllSlides() {
    if (!this.data.activeSlideItemId) return;
    
    let favoriteStories = [...this.data.favoriteStories];
    let historyStories = [...this.data.historyStories];
    
    let updated = false;
    
    favoriteStories.forEach(item => {
      if (item.x !== 0) {
        item.x = 0;
        updated = true;
      }
    });
    
    historyStories.forEach(item => {
      if (item.x !== 0) {
        item.x = 0;
        updated = true;
      }
    });
    
    if (updated) {
      // 更新当前活动tab的stories数组
      const stories = this.data.activeTab === 'favorite' ? favoriteStories : historyStories;
      
      this.setData({
        favoriteStories,
        historyStories,
        stories,
        activeSlideItemId: null
      });
    }
  },

  ToHome() {
    wx.switchTab({
      url: '/pages/home/<USER>'
    });
  },
  
  // 处理movable-view的变化事件
  handleMovableChange(e) {
    const { index, type } = e.currentTarget.dataset;
    const x = e.detail.x;
    
    // 更新对应故事项的x值
    const stories = this.data.activeTab === 'favorite' ? [...this.data.favoriteStories] : [...this.data.historyStories];
    if (stories[index]) {
      stories[index].x = x;
      
      // 如果滑动距离超过阈值，记录当前滑动项的ID
      if (x < -50 && stories[index]._id) {
        this.setData({
          activeSlideItemId: stories[index]._id
        });
      }
      
      // 更新数据
      if (this.data.activeTab === 'favorite') {
        this.setData({
          favoriteStories: stories
        });
      } else {
        this.setData({
          historyStories: stories
        });
      }
    }
  },
  
  // 处理触摸结束事件
  handleTouchEnd(e) {
    const { index } = e.currentTarget.dataset;
    const stories = this.data.activeTab === 'favorite' ? [...this.data.favoriteStories] : [...this.data.historyStories];
    
    if (!stories[index]) return;
    
    const x = stories[index].x || 0;
    
    // 如果滑动距离超过阈值，则显示删除按钮
    if (x < -50) {
      // 保持在打开状态
      stories[index].x = -120;
    } else {
      // 恢复到初始位置
      stories[index].x = 0;
    }
    
    // 更新数据
    if (this.data.activeTab === 'favorite') {
      this.setData({
        favoriteStories: stories
      });
    } else {
      this.setData({
        historyStories: stories
      });
    }
  }
});