{"appid": "wx114ca61634549666", "compileType": "miniprogram", "libVersion": "3.7.8", "packOptions": {"ignore": [], "include": []}, "setting": {"coverView": true, "es6": true, "postcss": true, "minified": true, "enhance": true, "showShadowRootInWxmlPanel": true, "packNpmRelationList": [], "babelSetting": {"ignore": [], "disablePlugins": [], "outputPath": ""}, "minifyWXSS": true, "minifyWXML": true, "useCompilerPlugins": false, "useStaticServer": true, "useMultiFrameRuntime": true, "useApiHook": true, "useApiHostProcess": true, "compileWorklet": false, "uglifyFileName": false, "uploadWithSourceMap": true, "packNpmManually": false, "localPlugins": false, "disableUseStrict": false, "condition": false, "swc": false, "disableSWC": true}, "condition": {}, "editorSetting": {"tabIndent": "insertSpaces", "tabSize": 2}, "cloudfunctionRoot": "cloudfunctions/", "cloudfunctionTemplateRoot": "cloudfunctionTemplate/", "projectname": "AI-爸妈讲故事", "description": "一个让爸妈为宝宝讲故事的小程序", "miniprogramRoot": "", "srcMiniprogramRoot": "", "checkInvalidKey": true, "permissions": {"openapi": ["getPhoneNumber"]}, "permission": {"scope.record": {"desc": "需要使用您的录音功能"}}, "simulatorPluginLibVersion": {}}