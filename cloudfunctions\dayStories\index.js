// 云函数入口文件
const cloud = require('wx-server-sdk')

cloud.init({ env: cloud.DYNAMIC_CURRENT_ENV });

const db = cloud.database()
const _ = db.command

// 根据年龄获取对应的故事类型
function getStoryTypesByAge(age) {
  if (age >= 1 && age <= 3) {
    return ['认知故事', '童谣故事', '动物故事', '睡前故事']
  } else if (age >= 4 && age <= 6) {
    return ['寓言故事',  '经典童话', '幽默故事', '职业认知故事', '情商培养故事']
  } else if (age >= 7 && age <= 9) {
    return ['探险故事', '成长故事', '神话故事', '民间故事', '科普故事']
  } else {
    return ['推理故事', '奇幻故事', '历史故事', '励志故事', '科技科幻故事']
  }
}

// 云函数入口函数
exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext()
  const { age } = event

  try {
    console.log('接收到的年龄参数：', age)
    // 获取适合年龄的故事类型
    const storyTypes = getStoryTypesByAge(age)
    console.log('根据年龄获取的故事类型：', storyTypes)

    // 从stories集合中获取非用户自定义的故事（系统预置故事）
    const storiesQuery = {
      type: _.in(storyTypes),
      _openid: _.exists(false) // 只查没有openid字段的
    }
    console.log('查询条件：', JSON.stringify(storiesQuery))

    // 获取所有符合条件的故事
    console.log('开始查询stories集合...')
    const queryResult = await db.collection('stories')
      .where(storiesQuery)
      .limit(100)
      .get()
    
    console.log('数据库查询结果：', JSON.stringify(queryResult))
    let { data: allStories } = queryResult
    
    // 如果没有找到任何故事，直接返回空数据
    if (allStories.length === 0) {
      return {
        code: 0,
        data: {
          todayRecommends: [],
          recommendedStories: [],
          categories: [],
          storiesByType: {}
        }
      }
    }
    console.log('查询到的故事数量：', allStories.length)

    // Fisher-Yates洗牌算法实现公平的随机选择
    function shuffleArray(array) {
      for (let i = array.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [array[i], array[j]] = [array[j], array[i]];
      }
      return array;
    }

    // 随机选择故事作为今日推荐
    const shuffledStories = shuffleArray([...allStories]);
    const recommendCount = Math.min(3, shuffledStories.length);
    const todayRecommends = shuffledStories.slice(0, recommendCount);

    // 随机选择故事作为推荐分类，避免重复
    const remainingStories = shuffledStories.slice(recommendCount);
    const recommendedCount = Math.min(20, remainingStories.length);
    const recommendedStories = remainingStories.slice(0, recommendedCount);

    // 按类型分组故事
    const storiesByType = {}
    allStories.forEach(story => {
      if (!storiesByType[story.type]) {
        storiesByType[story.type] = []
      }
      storiesByType[story.type].push(story)
    })

    // 只返回有故事的分类
    const categories = storyTypes.filter(type => storiesByType[type] && storiesByType[type].length > 0)

    return {
      code: 0,
      data: {
        todayRecommends,
        recommendedStories,
        categories,
        storiesByType
      }
    }

  } catch (error) {
    console.error('获取故事失败：', error)
    return {
      code: -1,
      message: '获取故事失败',
      error
    }
  }
}