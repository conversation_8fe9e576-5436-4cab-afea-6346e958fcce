{"version": 3, "sources": ["index.js", "lib/ec.js", "lib/sec.js"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;AACA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA,ACHA;AFOA,ACHA,ACHA;AFOA,ACHA,ACHA;AFOA,ACHA,ACHA;AFOA,ACHA,ACHA;AFOA,ACHA,ACHA;AFOA,ACHA,ACHA;AFOA,ACHA,ACHA;AFOA,ACHA,ACHA;AFOA,ACHA,ACHA;AFOA,ACHA,ACHA;AFOA,ACHA,ACHA;AFOA,ACHA,ACHA;AFOA,ACHA,ACHA;AFOA,ACHA,ACHA;AFOA,ACHA,ACHA;AFOA,ACHA,ACHA;AFOA,ACHA,ACHA;AFOA,ACHA,ACHA;AFOA,ACHA,ACHA;AFOA,ACHA,ACHA;AFOA,ACHA,ACHA;AFOA,ACHA,ACHA;AFOA,ACHA,ACHA;AFOA,ACHA,ACHA;AFOA,ACHA,ACHA;AFOA,ACHA,ACHA;AFOA,ACHA,ACHA;AFOA,ACHA,ACHA;AFOA,ACHA,ACHA;AFOA,ACHA,ACHA;AFOA,ACHA,ACHA;AFOA,ACHA,ACHA;AFOA,ACHA,ACHA;AFOA,ACHA,ACHA;AFOA,ACHA,ACHA;AFOA,ACHA,ACHA;AFOA,ACHA,ACHA;AFOA,ACHA,ACHA;AFOA,ACHA,ACHA;AFOA,ACHA,ACHA;AFOA,ACHA,ACHA;AFOA,ACHA,ACHA;AFOA,ACHA,ACHA;AFOA,ACHA,ACHA;AFOA,ACHA,ACHA;AFOA,ACHA,ACHA;AFOA,ACHA,ACHA;AFOA,ACHA,ACHA;AFOA,ACHA,ACHA;AFOA,ACHA,ACHA;AFOA,ACHA,ACHA;AFOA,ACHA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.js", "sourcesContent": ["var crypto = require(\"crypto\");\nvar BigInteger = require(\"jsbn\").BigInteger;\nvar ECPointFp = require(\"./lib/ec.js\").ECPointFp;\nvar Buffer = require(\"safer-buffer\").Buffer;\nexports.ECCurves = require(\"./lib/sec.js\");\n\n// zero prepad\nfunction unstupid(hex,len)\n{\n\treturn (hex.length >= len) ? hex : unstupid(\"0\"+hex,len);\n}\n\nexports.ECKey = function(curve, key, isPublic)\n{\n  var priv;\n\tvar c = curve();\n\tvar n = c.getN();\n  var bytes = Math.floor(n.bitLength()/8);\n\n  if(key)\n  {\n    if(isPublic)\n    {\n      var curve = c.getCurve();\n//      var x = key.slice(1,bytes+1); // skip the 04 for uncompressed format\n//      var y = key.slice(bytes+1);\n//      this.P = new ECPointFp(curve,\n//        curve.fromBigInteger(new BigInteger(x.toString(\"hex\"), 16)),\n//        curve.fromBigInteger(new BigInteger(y.toString(\"hex\"), 16)));      \n      this.P = curve.decodePointHex(key.toString(\"hex\"));\n    }else{\n      if(key.length != bytes) return false;\n      priv = new BigInteger(key.toString(\"hex\"), 16);      \n    }\n  }else{\n    var n1 = n.subtract(BigInteger.ONE);\n    var r = new BigInteger(crypto.randomBytes(n.bitLength()));\n    priv = r.mod(n1).add(BigInteger.ONE);\n    this.P = c.getG().multiply(priv);\n  }\n  if(this.P)\n  {\n//  var pubhex = unstupid(this.P.getX().toBigInteger().toString(16),bytes*2)+unstupid(this.P.getY().toBigInteger().toString(16),bytes*2);\n//  this.PublicKey = Buffer.from(\"04\"+pubhex,\"hex\");\n    this.PublicKey = Buffer.from(c.getCurve().encodeCompressedPointHex(this.P),\"hex\");\n  }\n  if(priv)\n  {\n    this.PrivateKey = Buffer.from(unstupid(priv.toString(16),bytes*2),\"hex\");\n    this.deriveSharedSecret = function(key)\n    {\n      if(!key || !key.P) return false;\n      var S = key.P.multiply(priv);\n      return Buffer.from(unstupid(S.getX().toBigInteger().toString(16),bytes*2),\"hex\");\n   }     \n  }\n}\n\n", "// Basic Javascript Elliptic Curve implementation\n// Ported loosely from BouncyCastle's Java EC code\n// Only Fp curves implemented for now\n\n// Requires jsbn.js and jsbn2.js\nvar BigInteger = require('jsbn').BigInteger\nvar Barrett = BigInteger.prototype.Barrett\n\n// ----------------\n// ECFieldElementFp\n\n// constructor\nfunction ECFieldElementFp(q,x) {\n    this.x = x;\n    // TODO if(x.compareTo(q) >= 0) error\n    this.q = q;\n}\n\nfunction feFpEquals(other) {\n    if(other == this) return true;\n    return (this.q.equals(other.q) && this.x.equals(other.x));\n}\n\nfunction feFpToBigInteger() {\n    return this.x;\n}\n\nfunction feFpNegate() {\n    return new ECFieldElementFp(this.q, this.x.negate().mod(this.q));\n}\n\nfunction feFpAdd(b) {\n    return new ECFieldElementFp(this.q, this.x.add(b.toBigInteger()).mod(this.q));\n}\n\nfunction feFpSubtract(b) {\n    return new ECFieldElementFp(this.q, this.x.subtract(b.toBigInteger()).mod(this.q));\n}\n\nfunction feFpMultiply(b) {\n    return new ECFieldElementFp(this.q, this.x.multiply(b.toBigInteger()).mod(this.q));\n}\n\nfunction feFpSquare() {\n    return new ECFieldElementFp(this.q, this.x.square().mod(this.q));\n}\n\nfunction feFpDivide(b) {\n    return new ECFieldElementFp(this.q, this.x.multiply(b.toBigInteger().modInverse(this.q)).mod(this.q));\n}\n\nECFieldElementFp.prototype.equals = feFpEquals;\nECFieldElementFp.prototype.toBigInteger = feFpToBigInteger;\nECFieldElementFp.prototype.negate = feFpNegate;\nECFieldElementFp.prototype.add = feFpAdd;\nECFieldElementFp.prototype.subtract = feFpSubtract;\nECFieldElementFp.prototype.multiply = feFpMultiply;\nECFieldElementFp.prototype.square = feFpSquare;\nECFieldElementFp.prototype.divide = feFpDivide;\n\n// ----------------\n// ECPointFp\n\n// constructor\nfunction ECPointFp(curve,x,y,z) {\n    this.curve = curve;\n    this.x = x;\n    this.y = y;\n    // Projective coordinates: either zinv == null or z * zinv == 1\n    // z and zinv are just BigIntegers, not fieldElements\n    if(z == null) {\n      this.z = BigInteger.ONE;\n    }\n    else {\n      this.z = z;\n    }\n    this.zinv = null;\n    //TODO: compression flag\n}\n\nfunction pointFpGetX() {\n    if(this.zinv == null) {\n      this.zinv = this.z.modInverse(this.curve.q);\n    }\n    var r = this.x.toBigInteger().multiply(this.zinv);\n    this.curve.reduce(r);\n    return this.curve.fromBigInteger(r);\n}\n\nfunction pointFpGetY() {\n    if(this.zinv == null) {\n      this.zinv = this.z.modInverse(this.curve.q);\n    }\n    var r = this.y.toBigInteger().multiply(this.zinv);\n    this.curve.reduce(r);\n    return this.curve.fromBigInteger(r);\n}\n\nfunction pointFpEquals(other) {\n    if(other == this) return true;\n    if(this.isInfinity()) return other.isInfinity();\n    if(other.isInfinity()) return this.isInfinity();\n    var u, v;\n    // u = Y2 * Z1 - Y1 * Z2\n    u = other.y.toBigInteger().multiply(this.z).subtract(this.y.toBigInteger().multiply(other.z)).mod(this.curve.q);\n    if(!u.equals(BigInteger.ZERO)) return false;\n    // v = X2 * Z1 - X1 * Z2\n    v = other.x.toBigInteger().multiply(this.z).subtract(this.x.toBigInteger().multiply(other.z)).mod(this.curve.q);\n    return v.equals(BigInteger.ZERO);\n}\n\nfunction pointFpIsInfinity() {\n    if((this.x == null) && (this.y == null)) return true;\n    return this.z.equals(BigInteger.ZERO) && !this.y.toBigInteger().equals(BigInteger.ZERO);\n}\n\nfunction pointFpNegate() {\n    return new ECPointFp(this.curve, this.x, this.y.negate(), this.z);\n}\n\nfunction pointFpAdd(b) {\n    if(this.isInfinity()) return b;\n    if(b.isInfinity()) return this;\n\n    // u = Y2 * Z1 - Y1 * Z2\n    var u = b.y.toBigInteger().multiply(this.z).subtract(this.y.toBigInteger().multiply(b.z)).mod(this.curve.q);\n    // v = X2 * Z1 - X1 * Z2\n    var v = b.x.toBigInteger().multiply(this.z).subtract(this.x.toBigInteger().multiply(b.z)).mod(this.curve.q);\n\n    if(BigInteger.ZERO.equals(v)) {\n        if(BigInteger.ZERO.equals(u)) {\n            return this.twice(); // this == b, so double\n        }\n\treturn this.curve.getInfinity(); // this = -b, so infinity\n    }\n\n    var THREE = new BigInteger(\"3\");\n    var x1 = this.x.toBigInteger();\n    var y1 = this.y.toBigInteger();\n    var x2 = b.x.toBigInteger();\n    var y2 = b.y.toBigInteger();\n\n    var v2 = v.square();\n    var v3 = v2.multiply(v);\n    var x1v2 = x1.multiply(v2);\n    var zu2 = u.square().multiply(this.z);\n\n    // x3 = v * (z2 * (z1 * u^2 - 2 * x1 * v^2) - v^3)\n    var x3 = zu2.subtract(x1v2.shiftLeft(1)).multiply(b.z).subtract(v3).multiply(v).mod(this.curve.q);\n    // y3 = z2 * (3 * x1 * u * v^2 - y1 * v^3 - z1 * u^3) + u * v^3\n    var y3 = x1v2.multiply(THREE).multiply(u).subtract(y1.multiply(v3)).subtract(zu2.multiply(u)).multiply(b.z).add(u.multiply(v3)).mod(this.curve.q);\n    // z3 = v^3 * z1 * z2\n    var z3 = v3.multiply(this.z).multiply(b.z).mod(this.curve.q);\n\n    return new ECPointFp(this.curve, this.curve.fromBigInteger(x3), this.curve.fromBigInteger(y3), z3);\n}\n\nfunction pointFpTwice() {\n    if(this.isInfinity()) return this;\n    if(this.y.toBigInteger().signum() == 0) return this.curve.getInfinity();\n\n    // TODO: optimized handling of constants\n    var THREE = new BigInteger(\"3\");\n    var x1 = this.x.toBigInteger();\n    var y1 = this.y.toBigInteger();\n\n    var y1z1 = y1.multiply(this.z);\n    var y1sqz1 = y1z1.multiply(y1).mod(this.curve.q);\n    var a = this.curve.a.toBigInteger();\n\n    // w = 3 * x1^2 + a * z1^2\n    var w = x1.square().multiply(THREE);\n    if(!BigInteger.ZERO.equals(a)) {\n      w = w.add(this.z.square().multiply(a));\n    }\n    w = w.mod(this.curve.q);\n    //this.curve.reduce(w);\n    // x3 = 2 * y1 * z1 * (w^2 - 8 * x1 * y1^2 * z1)\n    var x3 = w.square().subtract(x1.shiftLeft(3).multiply(y1sqz1)).shiftLeft(1).multiply(y1z1).mod(this.curve.q);\n    // y3 = 4 * y1^2 * z1 * (3 * w * x1 - 2 * y1^2 * z1) - w^3\n    var y3 = w.multiply(THREE).multiply(x1).subtract(y1sqz1.shiftLeft(1)).shiftLeft(2).multiply(y1sqz1).subtract(w.square().multiply(w)).mod(this.curve.q);\n    // z3 = 8 * (y1 * z1)^3\n    var z3 = y1z1.square().multiply(y1z1).shiftLeft(3).mod(this.curve.q);\n\n    return new ECPointFp(this.curve, this.curve.fromBigInteger(x3), this.curve.fromBigInteger(y3), z3);\n}\n\n// Simple NAF (Non-Adjacent Form) multiplication algorithm\n// TODO: modularize the multiplication algorithm\nfunction pointFpMultiply(k) {\n    if(this.isInfinity()) return this;\n    if(k.signum() == 0) return this.curve.getInfinity();\n\n    var e = k;\n    var h = e.multiply(new BigInteger(\"3\"));\n\n    var neg = this.negate();\n    var R = this;\n\n    var i;\n    for(i = h.bitLength() - 2; i > 0; --i) {\n\tR = R.twice();\n\n\tvar hBit = h.testBit(i);\n\tvar eBit = e.testBit(i);\n\n\tif (hBit != eBit) {\n\t    R = R.add(hBit ? this : neg);\n\t}\n    }\n\n    return R;\n}\n\n// Compute this*j + x*k (simultaneous multiplication)\nfunction pointFpMultiplyTwo(j,x,k) {\n  var i;\n  if(j.bitLength() > k.bitLength())\n    i = j.bitLength() - 1;\n  else\n    i = k.bitLength() - 1;\n\n  var R = this.curve.getInfinity();\n  var both = this.add(x);\n  while(i >= 0) {\n    R = R.twice();\n    if(j.testBit(i)) {\n      if(k.testBit(i)) {\n        R = R.add(both);\n      }\n      else {\n        R = R.add(this);\n      }\n    }\n    else {\n      if(k.testBit(i)) {\n        R = R.add(x);\n      }\n    }\n    --i;\n  }\n\n  return R;\n}\n\nECPointFp.prototype.getX = pointFpGetX;\nECPointFp.prototype.getY = pointFpGetY;\nECPointFp.prototype.equals = pointFpEquals;\nECPointFp.prototype.isInfinity = pointFpIsInfinity;\nECPointFp.prototype.negate = pointFpNegate;\nECPointFp.prototype.add = pointFpAdd;\nECPointFp.prototype.twice = pointFpTwice;\nECPointFp.prototype.multiply = pointFpMultiply;\nECPointFp.prototype.multiplyTwo = pointFpMultiplyTwo;\n\n// ----------------\n// ECCurveFp\n\n// constructor\nfunction ECCurveFp(q,a,b) {\n    this.q = q;\n    this.a = this.fromBigInteger(a);\n    this.b = this.fromBigInteger(b);\n    this.infinity = new ECPointFp(this, null, null);\n    this.reducer = new Barrett(this.q);\n}\n\nfunction curveFpGetQ() {\n    return this.q;\n}\n\nfunction curveFpGetA() {\n    return this.a;\n}\n\nfunction curveFpGetB() {\n    return this.b;\n}\n\nfunction curveFpEquals(other) {\n    if(other == this) return true;\n    return(this.q.equals(other.q) && this.a.equals(other.a) && this.b.equals(other.b));\n}\n\nfunction curveFpGetInfinity() {\n    return this.infinity;\n}\n\nfunction curveFpFromBigInteger(x) {\n    return new ECFieldElementFp(this.q, x);\n}\n\nfunction curveReduce(x) {\n    this.reducer.reduce(x);\n}\n\n// for now, work with hex strings because they're easier in JS\nfunction curveFpDecodePointHex(s) {\n    switch(parseInt(s.substr(0,2), 16)) { // first byte\n    case 0:\n\treturn this.infinity;\n    case 2:\n    case 3:\n\t// point compression not supported yet\n\treturn null;\n    case 4:\n    case 6:\n    case 7:\n\tvar len = (s.length - 2) / 2;\n\tvar xHex = s.substr(2, len);\n\tvar yHex = s.substr(len+2, len);\n\n\treturn new ECPointFp(this,\n\t\t\t     this.fromBigInteger(new BigInteger(xHex, 16)),\n\t\t\t     this.fromBigInteger(new BigInteger(yHex, 16)));\n\n    default: // unsupported\n\treturn null;\n    }\n}\n\nfunction curveFpEncodePointHex(p) {\n\tif (p.isInfinity()) return \"00\";\n\tvar xHex = p.getX().toBigInteger().toString(16);\n\tvar yHex = p.getY().toBigInteger().toString(16);\n\tvar oLen = this.getQ().toString(16).length;\n\tif ((oLen % 2) != 0) oLen++;\n\twhile (xHex.length < oLen) {\n\t\txHex = \"0\" + xHex;\n\t}\n\twhile (yHex.length < oLen) {\n\t\tyHex = \"0\" + yHex;\n\t}\n\treturn \"04\" + xHex + yHex;\n}\n\nECCurveFp.prototype.getQ = curveFpGetQ;\nECCurveFp.prototype.getA = curveFpGetA;\nECCurveFp.prototype.getB = curveFpGetB;\nECCurveFp.prototype.equals = curveFpEquals;\nECCurveFp.prototype.getInfinity = curveFpGetInfinity;\nECCurveFp.prototype.fromBigInteger = curveFpFromBigInteger;\nECCurveFp.prototype.reduce = curveReduce;\n//ECCurveFp.prototype.decodePointHex = curveFpDecodePointHex;\nECCurveFp.prototype.encodePointHex = curveFpEncodePointHex;\n\n// from: https://github.com/kaielvin/jsbn-ec-point-compression\nECCurveFp.prototype.decodePointHex = function(s)\n{\n\tvar yIsEven;\n    switch(parseInt(s.substr(0,2), 16)) { // first byte\n    case 0:\n\treturn this.infinity;\n    case 2:\n\tyIsEven = false;\n    case 3:\n\tif(yIsEven == undefined) yIsEven = true;\n\tvar len = s.length - 2;\n\tvar xHex = s.substr(2, len);\n\tvar x = this.fromBigInteger(new BigInteger(xHex,16));\n\tvar alpha = x.multiply(x.square().add(this.getA())).add(this.getB());\n\tvar beta = alpha.sqrt();\n\n    if (beta == null) throw \"Invalid point compression\";\n\n    var betaValue = beta.toBigInteger();\n    if (betaValue.testBit(0) != yIsEven)\n    {\n        // Use the other root\n        beta = this.fromBigInteger(this.getQ().subtract(betaValue));\n    }\n    return new ECPointFp(this,x,beta);\n    case 4:\n    case 6:\n    case 7:\n\tvar len = (s.length - 2) / 2;\n\tvar xHex = s.substr(2, len);\n\tvar yHex = s.substr(len+2, len);\n\n\treturn new ECPointFp(this,\n\t\t\t     this.fromBigInteger(new BigInteger(xHex, 16)),\n\t\t\t     this.fromBigInteger(new BigInteger(yHex, 16)));\n\n    default: // unsupported\n\treturn null;\n    }\n}\nECCurveFp.prototype.encodeCompressedPointHex = function(p)\n{\n\tif (p.isInfinity()) return \"00\";\n\tvar xHex = p.getX().toBigInteger().toString(16);\n\tvar oLen = this.getQ().toString(16).length;\n\tif ((oLen % 2) != 0) oLen++;\n\twhile (xHex.length < oLen)\n\t\txHex = \"0\" + xHex;\n\tvar yPrefix;\n\tif(p.getY().toBigInteger().isEven()) yPrefix = \"02\";\n\telse                                 yPrefix = \"03\";\n\n\treturn yPrefix + xHex;\n}\n\n\nECFieldElementFp.prototype.getR = function()\n{\n\tif(this.r != undefined) return this.r;\n\n    this.r = null;\n    var bitLength = this.q.bitLength();\n    if (bitLength > 128)\n    {\n        var firstWord = this.q.shiftRight(bitLength - 64);\n        if (firstWord.intValue() == -1)\n        {\n            this.r = BigInteger.ONE.shiftLeft(bitLength).subtract(this.q);\n        }\n    }\n    return this.r;\n}\nECFieldElementFp.prototype.modMult = function(x1,x2)\n{\n    return this.modReduce(x1.multiply(x2));\n}\nECFieldElementFp.prototype.modReduce = function(x)\n{\n    if (this.getR() != null)\n    {\n        var qLen = q.bitLength();\n        while (x.bitLength() > (qLen + 1))\n        {\n            var u = x.shiftRight(qLen);\n            var v = x.subtract(u.shiftLeft(qLen));\n            if (!this.getR().equals(BigInteger.ONE))\n            {\n                u = u.multiply(this.getR());\n            }\n            x = u.add(v); \n        }\n        while (x.compareTo(q) >= 0)\n        {\n            x = x.subtract(q);\n        }\n    }\n    else\n    {\n        x = x.mod(q);\n    }\n    return x;\n}\nECFieldElementFp.prototype.sqrt = function()\n{\n    if (!this.q.testBit(0)) throw \"unsupported\";\n\n    // p mod 4 == 3\n    if (this.q.testBit(1))\n    {\n    \tvar z = new ECFieldElementFp(this.q,this.x.modPow(this.q.shiftRight(2).add(BigInteger.ONE),this.q));\n    \treturn z.square().equals(this) ? z : null;\n    }\n\n    // p mod 4 == 1\n    var qMinusOne = this.q.subtract(BigInteger.ONE);\n\n    var legendreExponent = qMinusOne.shiftRight(1);\n    if (!(this.x.modPow(legendreExponent, this.q).equals(BigInteger.ONE)))\n    {\n        return null;\n    }\n\n    var u = qMinusOne.shiftRight(2);\n    var k = u.shiftLeft(1).add(BigInteger.ONE);\n\n    var Q = this.x;\n    var fourQ = modDouble(modDouble(Q));\n\n    var U, V;\n    do\n    {\n        var P;\n        do\n        {\n            P = new BigInteger(this.q.bitLength(), new SecureRandom());\n        }\n        while (P.compareTo(this.q) >= 0\n            || !(P.multiply(P).subtract(fourQ).modPow(legendreExponent, this.q).equals(qMinusOne)));\n\n        var result = this.lucasSequence(P, Q, k);\n        U = result[0];\n        V = result[1];\n\n        if (this.modMult(V, V).equals(fourQ))\n        {\n            // Integer division by 2, mod q\n            if (V.testBit(0))\n            {\n                V = V.add(q);\n            }\n\n            V = V.shiftRight(1);\n\n            return new ECFieldElementFp(q,V);\n        }\n    }\n    while (U.equals(BigInteger.ONE) || U.equals(qMinusOne));\n\n    return null;\n}\nECFieldElementFp.prototype.lucasSequence = function(P,Q,k)\n{\n    var n = k.bitLength();\n    var s = k.getLowestSetBit();\n\n    var Uh = BigInteger.ONE;\n    var Vl = BigInteger.TWO;\n    var Vh = P;\n    var Ql = BigInteger.ONE;\n    var Qh = BigInteger.ONE;\n\n    for (var j = n - 1; j >= s + 1; --j)\n    {\n        Ql = this.modMult(Ql, Qh);\n\n        if (k.testBit(j))\n        {\n            Qh = this.modMult(Ql, Q);\n            Uh = this.modMult(Uh, Vh);\n            Vl = this.modReduce(Vh.multiply(Vl).subtract(P.multiply(Ql)));\n            Vh = this.modReduce(Vh.multiply(Vh).subtract(Qh.shiftLeft(1)));\n        }\n        else\n        {\n            Qh = Ql;\n            Uh = this.modReduce(Uh.multiply(Vl).subtract(Ql));\n            Vh = this.modReduce(Vh.multiply(Vl).subtract(P.multiply(Ql)));\n            Vl = this.modReduce(Vl.multiply(Vl).subtract(Ql.shiftLeft(1)));\n        }\n    }\n\n    Ql = this.modMult(Ql, Qh);\n    Qh = this.modMult(Ql, Q);\n    Uh = this.modReduce(Uh.multiply(Vl).subtract(Ql));\n    Vl = this.modReduce(Vh.multiply(Vl).subtract(P.multiply(Ql)));\n    Ql = this.modMult(Ql, Qh);\n\n    for (var j = 1; j <= s; ++j)\n    {\n        Uh = this.modMult(Uh, Vl);\n        Vl = this.modReduce(Vl.multiply(Vl).subtract(Ql.shiftLeft(1)));\n        Ql = this.modMult(Ql, Ql);\n    }\n\n    return [ Uh, Vl ];\n}\n\nvar exports = {\n  ECCurveFp: ECCurveFp,\n  ECPointFp: ECPointFp,\n  ECFieldElementFp: ECFieldElementFp\n}\n\nmodule.exports = exports\n", "// Named EC curves\n\n// Requires ec.js, jsbn.js, and jsbn2.js\nvar BigInteger = require('jsbn').BigInteger\nvar ECCurveFp = require('./ec.js').ECCurveFp\n\n\n// ----------------\n// X9ECParameters\n\n// constructor\nfunction X9ECParameters(curve,g,n,h) {\n    this.curve = curve;\n    this.g = g;\n    this.n = n;\n    this.h = h;\n}\n\nfunction x9getCurve() {\n    return this.curve;\n}\n\nfunction x9getG() {\n    return this.g;\n}\n\nfunction x9getN() {\n    return this.n;\n}\n\nfunction x9getH() {\n    return this.h;\n}\n\nX9ECParameters.prototype.getCurve = x9getCurve;\nX9ECParameters.prototype.getG = x9getG;\nX9ECParameters.prototype.getN = x9getN;\nX9ECParameters.prototype.getH = x9getH;\n\n// ----------------\n// SECNamedCurves\n\nfunction fromHex(s) { return new BigInteger(s, 16); }\n\nfunction secp128r1() {\n    // p = 2^128 - 2^97 - 1\n    var p = fromHex(\"FFFFFFFDFFFFFFFFFFFFFFFFFFFFFFFF\");\n    var a = fromHex(\"FFFFFFFDFFFFFFFFFFFFFFFFFFFFFFFC\");\n    var b = fromHex(\"E87579C11079F43DD824993C2CEE5ED3\");\n    //byte[] S = Hex.decode(\"000E0D4D696E6768756151750CC03A4473D03679\");\n    var n = fromHex(\"FFFFFFFE0000000075A30D1B9038A115\");\n    var h = BigInteger.ONE;\n    var curve = new ECCurveFp(p, a, b);\n    var G = curve.decodePointHex(\"04\"\n                + \"161FF7528B899B2D0C28607CA52C5B86\"\n\t\t+ \"CF5AC8395BAFEB13C02DA292DDED7A83\");\n    return new X9ECParameters(curve, G, n, h);\n}\n\nfunction secp160k1() {\n    // p = 2^160 - 2^32 - 2^14 - 2^12 - 2^9 - 2^8 - 2^7 - 2^3 - 2^2 - 1\n    var p = fromHex(\"FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFEFFFFAC73\");\n    var a = BigInteger.ZERO;\n    var b = fromHex(\"7\");\n    //byte[] S = null;\n    var n = fromHex(\"0100000000000000000001B8FA16DFAB9ACA16B6B3\");\n    var h = BigInteger.ONE;\n    var curve = new ECCurveFp(p, a, b);\n    var G = curve.decodePointHex(\"04\"\n                + \"3B4C382CE37AA192A4019E763036F4F5DD4D7EBB\"\n                + \"938CF935318FDCED6BC28286531733C3F03C4FEE\");\n    return new X9ECParameters(curve, G, n, h);\n}\n\nfunction secp160r1() {\n    // p = 2^160 - 2^31 - 1\n    var p = fromHex(\"FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF7FFFFFFF\");\n    var a = fromHex(\"FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF7FFFFFFC\");\n    var b = fromHex(\"1C97BEFC54BD7A8B65ACF89F81D4D4ADC565FA45\");\n    //byte[] S = Hex.decode(\"1053CDE42C14D696E67687561517533BF3F83345\");\n    var n = fromHex(\"0100000000000000000001F4C8F927AED3CA752257\");\n    var h = BigInteger.ONE;\n    var curve = new ECCurveFp(p, a, b);\n    var G = curve.decodePointHex(\"04\"\n\t\t+ \"4A96B5688EF573284664698968C38BB913CBFC82\"\n\t\t+ \"23A628553168947D59DCC912042351377AC5FB32\");\n    return new X9ECParameters(curve, G, n, h);\n}\n\nfunction secp192k1() {\n    // p = 2^192 - 2^32 - 2^12 - 2^8 - 2^7 - 2^6 - 2^3 - 1\n    var p = fromHex(\"FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFEFFFFEE37\");\n    var a = BigInteger.ZERO;\n    var b = fromHex(\"3\");\n    //byte[] S = null;\n    var n = fromHex(\"FFFFFFFFFFFFFFFFFFFFFFFE26F2FC170F69466A74DEFD8D\");\n    var h = BigInteger.ONE;\n    var curve = new ECCurveFp(p, a, b);\n    var G = curve.decodePointHex(\"04\"\n                + \"DB4FF10EC057E9AE26B07D0280B7F4341DA5D1B1EAE06C7D\"\n                + \"9B2F2F6D9C5628A7844163D015BE86344082AA88D95E2F9D\");\n    return new X9ECParameters(curve, G, n, h);\n}\n\nfunction secp192r1() {\n    // p = 2^192 - 2^64 - 1\n    var p = fromHex(\"FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFEFFFFFFFFFFFFFFFF\");\n    var a = fromHex(\"FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFEFFFFFFFFFFFFFFFC\");\n    var b = fromHex(\"64210519E59C80E70FA7E9AB72243049FEB8DEECC146B9B1\");\n    //byte[] S = Hex.decode(\"3045AE6FC8422F64ED579528D38120EAE12196D5\");\n    var n = fromHex(\"FFFFFFFFFFFFFFFFFFFFFFFF99DEF836146BC9B1B4D22831\");\n    var h = BigInteger.ONE;\n    var curve = new ECCurveFp(p, a, b);\n    var G = curve.decodePointHex(\"04\"\n                + \"188DA80EB03090F67CBF20EB43A18800F4FF0AFD82FF1012\"\n                + \"07192B95FFC8DA78631011ED6B24CDD573F977A11E794811\");\n    return new X9ECParameters(curve, G, n, h);\n}\n\nfunction secp224r1() {\n    // p = 2^224 - 2^96 + 1\n    var p = fromHex(\"FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF000000000000000000000001\");\n    var a = fromHex(\"FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFEFFFFFFFFFFFFFFFFFFFFFFFE\");\n    var b = fromHex(\"B4050A850C04B3ABF54132565044B0B7D7BFD8BA270B39432355FFB4\");\n    //byte[] S = Hex.decode(\"BD71344799D5C7FCDC45B59FA3B9AB8F6A948BC5\");\n    var n = fromHex(\"FFFFFFFFFFFFFFFFFFFFFFFFFFFF16A2E0B8F03E13DD29455C5C2A3D\");\n    var h = BigInteger.ONE;\n    var curve = new ECCurveFp(p, a, b);\n    var G = curve.decodePointHex(\"04\"\n                + \"B70E0CBD6BB4BF7F321390B94A03C1D356C21122343280D6115C1D21\"\n                + \"BD376388B5F723FB4C22DFE6CD4375A05A07476444D5819985007E34\");\n    return new X9ECParameters(curve, G, n, h);\n}\n\nfunction secp256r1() {\n    // p = 2^224 (2^32 - 1) + 2^192 + 2^96 - 1\n    var p = fromHex(\"FFFFFFFF00000001000000000000000000000000FFFFFFFFFFFFFFFFFFFFFFFF\");\n    var a = fromHex(\"FFFFFFFF00000001000000000000000000000000FFFFFFFFFFFFFFFFFFFFFFFC\");\n    var b = fromHex(\"5AC635D8AA3A93E7B3EBBD55769886BC651D06B0CC53B0F63BCE3C3E27D2604B\");\n    //byte[] S = Hex.decode(\"C49D360886E704936A6678E1139D26B7819F7E90\");\n    var n = fromHex(\"FFFFFFFF00000000FFFFFFFFFFFFFFFFBCE6FAADA7179E84F3B9CAC2FC632551\");\n    var h = BigInteger.ONE;\n    var curve = new ECCurveFp(p, a, b);\n    var G = curve.decodePointHex(\"04\"\n                + \"6B17D1F2E12C4247F8BCE6E563A440F277037D812DEB33A0F4A13945D898C296\"\n\t\t+ \"4FE342E2FE1A7F9B8EE7EB4A7C0F9E162BCE33576B315ECECBB6406837BF51F5\");\n    return new X9ECParameters(curve, G, n, h);\n}\n\n// TODO: make this into a proper hashtable\nfunction getSECCurveByName(name) {\n    if(name == \"secp128r1\") return secp128r1();\n    if(name == \"secp160k1\") return secp160k1();\n    if(name == \"secp160r1\") return secp160r1();\n    if(name == \"secp192k1\") return secp192k1();\n    if(name == \"secp192r1\") return secp192r1();\n    if(name == \"secp224r1\") return secp224r1();\n    if(name == \"secp256r1\") return secp256r1();\n    return null;\n}\n\nmodule.exports = {\n  \"secp128r1\":secp128r1,\n  \"secp160k1\":secp160k1,\n  \"secp160r1\":secp160r1,\n  \"secp192k1\":secp192k1,\n  \"secp192r1\":secp192r1,\n  \"secp224r1\":secp224r1,\n  \"secp256r1\":secp256r1\n}\n"]}