// 云函数入口文件
const cloud = require('wx-server-sdk')

cloud.init({ env: cloud.DYNAMIC_CURRENT_ENV });

const db = cloud.database()

// 云函数入口函数
exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext()
  const openid = wxContext.OPENID

  try {
    // 获取当前用户的宝宝信息
    const { data } = await db.collection('baby_info')
      .where({
        _openid: openid
      })
      .get()

    return {
      code: 0,
      data: data,
      message: '获取成功'
    }
  } catch (error) {
    console.error('获取宝宝信息失败：', error)
    return {
      code: -1,
      data: null,
      message: '获取失败'
    }
  }
}
