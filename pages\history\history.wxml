<view class="container" bindrefresherrefresh="onRefresh" refresher-enabled="{{true}}" refresher-triggered="{{refreshing}}" refresher-default-style="black" refresher-background="#f2f2f2" bindtap="onPageTap" bindscroll="onPageScroll" refresher-threshold="50">
  <view class="custom-nav-bar">
      <image class="clear-image" src="/images/clear.png" bindtap="clearStories" mode="aspectFit"></image>
      <view class="nav-tabs">
        <view class="nav-tab {{activeTab === 'favorite' ? 'active' : ''}}" bindtap="switchTab" data-tab="favorite">
          <text>收藏</text>
        </view>
        <view class="nav-tab {{activeTab === 'history' ? 'active' : ''}}" bindtap="switchTab" data-tab="history">
          <text>历史</text>
        </view>
      </view>
  </view>
  
  <!-- 隐藏原来的tab切换，保留swiper功能 -->
  <view class="tab-container" style="display: none;">
    <view class="tab-item {{activeTab === 'favorite' ? 'active' : ''}}" bindtap="switchTab" data-tab="favorite">
      <text>收藏</text>
    </view>
    <view class="tab-item {{activeTab === 'history' ? 'active' : ''}}" bindtap="switchTab" data-tab="history">
      <text>历史</text>
    </view>
  </view>
  
  <!-- 使用条件渲染替代swiper组件 -->
  <view class="tab-content-container" style="height: calc(100vh - 190rpx);">
    <!-- 收藏内容 -->
    <view class="tab-content" wx:if="{{activeTab === 'favorite'}}">
      <scroll-view scroll-y="true" style="height: 100%;">
        <block wx:if="{{favoriteStories.length === 0}}">
          <view class="empty-state">
            <text>空空如也!</text>
            <text class="subtitle">你还没有收藏任何故事，快去收藏喜欢的故事吧！</text>
          </view>
        </block>
        <block wx:else>
          <view class="story-list">
            <view class="story-item-wrapper" wx:for="{{favoriteStories}}" wx:key="_id">
              <movable-area class="movable-container">
                <movable-view class="story-item" direction="horizontal" x="{{item.x || 0}}" inertia="true" out-of-bounds="true" damping="40" friction="5" bindchange="handleMovableChange" bindtouchend="handleTouchEnd" data-index="{{index}}" data-type="ai" data-force-update="{{forceUpdate}}">
                  <view class="story-content-wrapper">
                    <view class="story-tap-area" bindtap="onStoryTap" data-storyid="{{item._id}}">
                      <image class="story-image" src="{{item.image}}"></image>
                      <view class="story-text-content">
                        <view class="story-title">{{item.title}}</view>
                        <view class="story-content-preview">
                          <text class="story-content" user-select="true">{{item.content}}</text>
                        </view>
                        <view class="story-time">
                          {{item.createTime}}
                          <text wx:if="{{item.isCustom}}" class="custom-tag">自定义</text>
                        </view>
                      </view>
                    </view>
                  </view>
                </movable-view>
                <!-- 删除按钮 -->
                <view class="delete-btn" style="transform: translateX({{item.x < -10? 0 : 10}}rpx);" bindtap="deleteStory" data-storyid="{{item._id}}">删除</view>
              </movable-area>
            </view>
          </view>
        </block>
      </scroll-view>
    </view>
    
    <!-- 历史内容 -->
    <view class="tab-content" wx:if="{{activeTab === 'history'}}">
      <scroll-view scroll-y="true" style="height: 100%;">
        <block wx:if="{{historyStories.length === 0}}">
          <view class="empty-state">
            <text>空空如也!</text>
            <text class="subtitle">你还没有听过任何故事，快去首页听一个吧！</text>
          </view>
        </block>
        <block wx:else>
          <view class="story-list">
            <view class="story-item-wrapper" wx:for="{{historyStories}}" wx:key="_id">
              <movable-area class="movable-container">
                <movable-view class="story-item" direction="horizontal" x="{{item.x || 0}}" inertia="true" out-of-bounds="true" damping="40" friction="5" bindchange="handleMovableChange" bindtouchend="handleTouchEnd" data-index="{{index}}" data-type="ai" data-force-update="{{forceUpdate}}">
                  <view class="story-content-wrapper">
                    <view class="story-tap-area" bindtap="onStoryTap" data-storyid="{{item._id}}">
                      <image class="story-image" src="{{item.image}}"></image>
                      <view class="story-text-content">
                        <view class="story-title">{{item.title}}</view>
                        <view class="story-content-preview">
                          <text class="story-content" user-select="true">{{item.content}}</text>
                        </view>
                        <view class="story-time">
                          {{item.createTime}}
                          <text wx:if="{{item.isCustom}}" class="custom-tag">自定义</text>
                        </view>
                      </view>
                    </view>
                  </view>
                </movable-view>
                <!-- 删除按钮 -->
                <view class="delete-btn" style="transform: translateX({{item.x < -10? 0 : 10}}rpx);" bindtap="deleteStory" data-storyid="{{item._id}}">删除</view>
              </movable-area>
            </view>
          </view>
        </block>
      </scroll-view>
    </view>
  </view>

    <!-- AI -->
  <view class="AI-btn" bindtap="customStory">
    <!-- 修正 src 属性 -->
    <image src="/images/AI_background.png" ></image> 
  </view>

   <!-- home -->
  <view class="tab-home" bindtap="ToHome">
    <image src="/images/home.png" ></image>
  </view>

  <!-- history -->
  <view class="tab-history-inactive" catchtap="onTabHistory">
    <image class="tab-image" src="/images/history.png" ></image>
  </view>
</view>