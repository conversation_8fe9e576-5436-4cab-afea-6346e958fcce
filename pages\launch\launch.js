Page({
  data: {
    showPrivacyPopup: false
  },

  onLoad() {
    this.checkPrivacyStatus();
  },

  onShow() {
    // 防止onLoad没触发，再次检查隐私协议状态
    this.checkPrivacyStatus();
  },

  // 检查隐私协议状态
  checkPrivacyStatus() {
    const privacyAgreed = wx.getStorageSync('privacy_agreed');
    
    if (privacyAgreed) {
      // 已同意隐私协议，直接跳转到首页
      wx.reLaunch({
        url: '/pages/home/<USER>'
      });
    } else {
      // 未同意隐私协议，显示隐私弹窗
      this.showPrivacyPopup();
    }
  },

  // 显示隐私协议弹窗
  showPrivacyPopup() {
    // 可以添加延迟显示，提升用户体验
    setTimeout(() => {
      this.setData({
        showPrivacyPopup: true
      });
    }, 500); // 500ms 延迟，避免页面闪烁
  },

  // 用户同意隐私协议
  onAgree() {
    // 保存隐私协议同意状态
    wx.setStorageSync('privacy_agreed', true);
    
    // 隐藏弹窗
    this.setData({
      showPrivacyPopup: false
    });
    
    // 跳转到首页
    wx.reLaunch({
      url: '/pages/home/<USER>'
    });
  },

  // 用户不同意隐私协议
  onDisagree() {
    wx.showModal({
      title: '提示',
      content: '您需要同意隐私协议才能使用本小程序',
      showCancel: false,
      success: () => {
        // 用户点击确定后，退出小程序
        wx.exitMiniProgram();
      }
    });
  }
});